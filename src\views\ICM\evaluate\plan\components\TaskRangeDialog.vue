<template>
  <Dialog
    v-model="visible"
    title="添加评价任务范围"
    width="1200px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div>
      <div class="flex h-500px">
        <!-- 左侧流程类型树 -->
        <div class="w-300px border-r border-gray-200 pr-4">
          <div class="mb-4">
            <ElInput
              v-model="treeFilterText"
              placeholder="请输入关键字查询"
              :prefix-icon="Search"
              clearable
              @input="handleTreeFilter"
            />
          </div>
          <div class="bg-blue-50 p-3 mb-4 rounded">
            <div class="text-sm font-medium text-blue-800 mb-2">全部</div>
            <div class="text-xs text-blue-600">▼ 流程类型</div>
          </div>
          <ElTree
            ref="treeRef"
            :data="treeData"
            :filter-node-method="filterNode"
            :expand-on-click-node="false"
            :default-expanded-keys="['1']"
            node-key="id"
            @node-click="handleNodeClick"
          >
            <template #default="{ node }">
              <div class="flex items-center">
                <span class="ml-2 text-sm">{{ node.label }}</span>
              </div>
            </template>
          </ElTree>
        </div>

        <!-- 右侧任务范围表格 -->
        <div class="flex-1 pl-4">
          <!-- 搜索区域 -->
          <div class="mb-4">
            <div class="flex items-center gap-4 mb-3">
              <ElInput
                v-model="searchParams.keyword"
                placeholder="请输入关键字查询"
                class="w-200px"
                clearable
                @input="handleSearch"
              />
              <ElInput
                v-model="searchParams.controlFunctionName"
                placeholder="请输入流程类型名称"
                class="w-200px"
                clearable
                @input="handleSearch"
              />
              <ElSelect
                v-model="searchParams.controlLevel"
                placeholder="请选择控制级别"
                class="w-120px"
                clearable
                @change="handleSearch"
              >
                <ElOption
                  v-for="item in controlLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
              <ElSelect
                v-model="searchParams.controlType"
                placeholder="请选择控制类型"
                class="w-120px"
                clearable
                @change="handleSearch"
              >
                <ElOption
                  v-for="item in controlTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </div>
            <div class="flex items-center gap-4">
              <Button type="primary" @click="handleSearch">查询</Button>
              <Button @click="handleReset">重置</Button>
            </div>
          </div>

          <!-- 表格区域 -->
          <div class="border rounded">
            <Table
              ref="tableRef"
              v-model:pageSize="pageSize"
              v-model:currentPage="currentPage"
              :data="tableList"
              :columns="allSchemas.tableColumns"
              :loading="loading"
              :pagination="{
                total: total
              }"
              selection
              row-key="id"
              :reserve-selection="true"
              @register="handleRegister"
              @refresh="tableMethods.getList"
              height="400px"
            />
          </div>
        </div>
      </div>

      <!-- 已选择项目 -->
      <div class="mt-8">
        <div class="mb-2">
          <span class="text-sm font-medium">已选: {{ selectedItems.length }}</span>
          <span
            class="ml-18px text-sm text-red-500 cursor-pointer hover:text-red-700"
            @click="handleClearSelected"
            >清空</span
          >
        </div>
        <div class="flex flex-wrap gap-2">
          <ElTag
            v-for="(item, index) in selectedItems"
            :key="item.id"
            :closable="true"
            @close="handleRemoveSelected(index)"
            class="mb-1"
          >
            {{ item.controlFunctionName }}
          </ElTag>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-4">
        <Button @click="handleClose">取消</Button>
        <Button type="primary" @click="handleConfirm">确定</Button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed, toRefs } from 'vue'
import { Dialog, Button, Table } from '@/components'
import { ElInput, ElTree, ElSelect, ElOption, ElTag } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getTaskRangeListApi, getFlowTypeTreeApi } from '@/api/ICM/evaluate'
import { CONTROL_LEVEL_LIST, CONTROL_TYPE_LIST } from '../constants/planConstants'
import { EvaluationTaskRangeVO } from '@/api/ICM/evaluate/index'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { useTable } from '@/hooks/web/useTable'
import { taskRangeSchemas } from '../schemas/planSchemas'

const emit = defineEmits(['confirm'])

// 弹窗状态
const visible = ref(false)

// 左侧树形数据
const treeData = ref([])
const treeFilterText = ref('')
const treeRef = ref()

// 搜索参数
const searchParams = reactive({
  keyword: '',
  controlFunctionName: '',
  controlLevel: '',
  controlType: '',
  flowType: ''
})

// 表格引用
const tableRef = ref()
const elTableRef = ref()

// 选中的项目
const selectedItems = ref<EvaluationTaskRangeVO[]>([])

// 选项列表
const controlLevelOptions = CONTROL_LEVEL_LIST
const controlTypeOptions = CONTROL_TYPE_LIST

// 使用CRUD Schemas生成表格列配置
const { allSchemas } = useCrudSchemas(taskRangeSchemas)

// 获取列表数据的API函数
const getListApi = async (params: any) => {
  const requestParams = {
    pageNum: params.pageNo,
    pageSize: params.pageSize,
    ...searchParams
  }

  console.log('API调用参数:', requestParams)
  const res = await getTaskRangeListApi(requestParams)
  return res
}

// 使用表格hook
const { register, tableMethods, tableObject } = useTable({
  getListApi
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 存储需要预选择的数据
const pendingSelectedItems = ref<EvaluationTaskRangeVO[]>([])

// 注册表格实例的方法
const handleRegister = (ref: any, elRef: any) => {
  tableRef.value = ref // Table组件实例
  elTableRef.value = elRef // ElTable实例
  register(ref, elRef)
  console.log('注册表格实例:', { ref, elRef })

  // 监听Table组件内部的selections变化
  if (ref && ref.selections) {
    watch(
      () => ref.selections,
      (newSelections) => {
        console.log('Table组件selections变化:', newSelections)
        selectedItems.value = [...newSelections]
      },
      { deep: true }
    )
  }
}

// 监听数据加载完成，设置预选择的数据
watch(
  () => [tableList.value, loading.value],
  ([newTableList, newLoading]) => {
    if (!newLoading && newTableList.length > 0 && pendingSelectedItems.value.length > 0) {
      console.log('数据加载完成，设置预选择状态:', pendingSelectedItems.value)

      setTimeout(() => {
        if (elTableRef.value) {
          pendingSelectedItems.value.forEach((item) => {
            const row = newTableList.find((r) => r.id === item.id)
            if (row) {
              console.log('设置行选择状态:', row)
              elTableRef.value.toggleRowSelection(row, true)
            }
          })
          // 清空待选择的数据
          pendingSelectedItems.value = []
        }
      }, 50)
    }
  },
  { deep: true }
)

// 树节点过滤方法
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.includes(value)
}

// 树过滤处理
const handleTreeFilter = () => {
  treeRef.value.filter(treeFilterText.value)
}

// 节点点击处理
const handleNodeClick = (data: any) => {
  if (data.type === 'flow') {
    searchParams.flowType = data.value
    handleSearch()
  }
}

// 搜索处理
const handleSearch = async () => {
  tableMethods.getList()
}

// 重置处理
const handleReset = () => {
  Object.assign(searchParams, {
    keyword: '',
    controlFunctionName: '',
    controlLevel: '',
    controlType: '',
    flowType: ''
  })
  handleSearch()
}

// 加载树形数据
const loadTreeData = async () => {
  try {
    const res = await getFlowTypeTreeApi()
    treeData.value = res.data || []
  } catch (error) {
    console.error('加载树形数据失败:', error)
  }
}

// 移除已选择的项目
const handleRemoveSelected = (index: number) => {
  const removedItem = selectedItems.value[index]
  console.log('移除项目:', removedItem)

  // 通过ElTable实例取消选择
  if (elTableRef.value) {
    elTableRef.value.toggleRowSelection(removedItem, false)
  }
}

// 清空已选择的项目
const handleClearSelected = () => {
  console.log('清空选择')

  // 通过ElTable实例清空选择
  if (elTableRef.value) {
    elTableRef.value.clearSelection()
  }

  // 手动清空selectedItems数组（以防watch没有及时触发）
  selectedItems.value = []
  pendingSelectedItems.value = []
}

// 打开弹窗
const open = async (preSelectedItems: EvaluationTaskRangeVO[] = []) => {
  visible.value = true
  selectedItems.value = [...preSelectedItems]
  pendingSelectedItems.value = [...preSelectedItems]

  console.log('打开弹窗，预选择数据:', preSelectedItems)

  // 重置分页状态到第一页
  tableObject.currentPage = 1
  tableObject.pageSize = 10

  // 重置搜索条件
  Object.assign(searchParams, {
    keyword: '',
    controlFunctionName: '',
    controlLevel: '',
    controlType: '',
    flowType: ''
  })

  // 重置树形筛选
  treeFilterText.value = ''

  await nextTick()
  await loadTreeData()
  tableMethods.getList()

  // 清空之前的选择状态
  setTimeout(() => {
    if (elTableRef.value) {
      elTableRef.value.clearSelection()
    }
  }, 50)
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  selectedItems.value = []
  pendingSelectedItems.value = []

  // 清空表格选择
  if (elTableRef.value) {
    elTableRef.value.clearSelection()
  }

  // 重置搜索参数和分页状态
  Object.assign(searchParams, {
    keyword: '',
    controlFunctionName: '',
    controlLevel: '',
    controlType: '',
    flowType: ''
  })

  // 重置树形筛选
  treeFilterText.value = ''

  // 重置分页状态
  tableObject.currentPage = 1
  tableObject.pageSize = 10
}

// 确认选择
const handleConfirm = () => {
  console.log('确认选择的数据:', selectedItems.value)
  emit('confirm', selectedItems.value)
  handleClose()
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped lang="scss"></style>
