<template>
  <div class="attachment-upload">
    <div class="upload-section" v-if="mode !== 'view'">
      <Button type="primary" @click="handleUpload">
        <Icon icon="ep:upload-filled" />
        上传文件
      </Button>
    </div>

    <div class="file-list-section">
      <div>
        <Table
          height="200px"
          :data="attachments"
          :columns="columns"
          :pagination="false"
          style="width: 100%"
        >
          <template #fileName="{ row }">
            <div class="file-name">
              <Icon :icon="getFileIcon(row.fileName)" class="file-icon" />
              <span>{{ row.fileName }}</span>
            </div>
          </template>

          <template #fileSize="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>

          <template #isActive="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleVersionChange(row)"
              :disabled="mode === 'view'"
            />
          </template>

          <template #action="{ row, $index }">
            <Button link type="primary" @click="handlePreview(row)">预览</Button>
            <Button link type="danger" @click="handleDelete(row, $index)" v-if="mode !== 'view'">
              删除
            </Button>
          </template>
        </Table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button, Table, Icon } from '@/components'
import { ElMessage, ElMessageBox, ElSwitch } from 'element-plus'
import { useSelectFile } from '@/components/UploadFile/src/useSelectFile'
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { SUPPORTED_FILE_TYPES } from '../constants/planConstants'

const { selectFile } = useSelectFile()
const { uploadUrl, httpRequest } = useUpload()

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'edit' // edit, view
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 附件列表
const attachments = ref([])

// 表格列配置
const columns = ref([
  {
    label: '文件名称',
    prop: 'fileName',
    // width: 250,
    slots: { default: 'fileName' }
  },
  {
    label: '上传人',
    prop: 'uploader'
    // width: 120
  },
  {
    label: '上传时间',
    prop: 'uploadTime'
    // width: 160
  },
  {
    label: '版本号',
    prop: 'version'
    // width: 100
  },
  {
    label: '文件大小',
    prop: 'fileSize',
    // width: 120,
    slots: { default: 'fileSize' }
  },
  {
    label: '版本应用',
    prop: 'isActive',
    // width: 100,
    slots: { default: 'isActive' }
  },
  {
    label: '操作',
    prop: 'action',
    // width: 120,
    slots: { default: 'action' }
  }
])

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    attachments.value = newVal && Array.isArray(newVal) ? [...newVal] : []
  },
  { immediate: true }
)

// 文件上传处理
const handleUpload = async () => {
  try {
    const { file } = await selectFile({
      maxSize: 50, // 最大50MB
      accept: SUPPORTED_FILE_TYPES
    })

    const res = await httpRequest({
      file: file
    })

    // 创建新的附件对象
    const newAttachment = {
      id: Date.now(),
      fileName: file.name,
      fileUrl: res.data.url || URL.createObjectURL(file),
      fileSize: file.size,
      uploadTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
      uploader: '当前用户',
      version: 'V1.0',
      isActive: true
    }

    attachments.value.push(newAttachment)
    updateModelValue()

    ElMessage.success('上传成功')
  } catch (error) {
    ElMessage.error('文件上传失败')
  }
}

// 删除附件
const handleDelete = (row: any, index: number) => {
  ElMessageBox.confirm('确定要删除该附件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      attachments.value.splice(index, 1)
      updateModelValue()
      ElMessage.success('删除成功')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 预览文件
const handlePreview = (row: any) => {
  if (row.fileUrl) {
    window.open(row.fileUrl, '_blank')
  } else {
    ElMessage.warning('暂无预览链接')
  }
}

// 版本切换
const handleVersionChange = (row: any) => {
  if (row.isActive) {
    // 将其他版本设为非激活
    attachments.value.forEach((item) => {
      if (item.id !== row.id) {
        item.isActive = false
      }
    })
  }
  updateModelValue()
}

// 更新v-model
const updateModelValue = () => {
  emit('update:modelValue', attachments.value)
  emit('change', attachments.value)
}

// 获取文件图标
const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  const iconMap = {
    doc: 'ep:document',
    docx: 'ep:document',
    pdf: 'ep:document',
    xls: 'ep:document',
    xlsx: 'ep:document',
    ppt: 'ep:document',
    pptx: 'ep:document'
  }
  return iconMap[ext] || 'ep:document'
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + ' MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
}

// 暴露方法
defineExpose({
  getAttachments: () => attachments.value
})
</script>

<style scoped lang="scss">
.attachment-upload {
  .upload-section {
    margin-bottom: 20px;
  }

  .file-list-section {
    .file-name {
      display: flex;
      align-items: center;

      .file-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #999;

      .empty-icon {
        margin-bottom: 12px;
      }

      .empty-text {
        font-size: 14px;
      }
    }
  }
}
</style>
