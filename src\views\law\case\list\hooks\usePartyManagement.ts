import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 当事人信息数据结构
export interface PartyInfo {
  // id: string
  partyName: string
  legalRepresentative: string
  contactPerson: string
  contactPhone: string
  address: string
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

// 创建新的当事人信息
const createNewParty = (): PartyInfo => ({
  // id: generateId(),
  partyName: '',
  legalRepresentative: '',
  contactPerson: '',
  contactPhone: '',
  address: ''
})

/**
 * 当事人管理 Hook
 * @param initialData 初始数据
 * @returns 当事人列表管理对象
 */
export const usePartyManagement = (initialData?: PartyInfo[]) => {
  const list = ref<PartyInfo[]>(initialData || [createNewParty()])

  // 添加当事人
  const add = () => {
    list.value.push(createNewParty())
    console.log('添加当事人:', list.value)
  }

  // 删除当事人
  const remove = (index: number) => {
    if (list.value.length <= 1) {
      ElMessage.warning('至少保留一条记录')
      return
    }
    list.value.splice(index, 1)
    ElMessage.success('删除成功')
  }

  // 清空列表
  const clear = () => {
    list.value = [createNewParty()]
  }

  // 设置数据
  const setData = (data: PartyInfo[]) => {
    list.value = data.length > 0 ? data : [createNewParty()]
  }

  // 获取有效数据（过滤掉空数据）
  const getValidData = () => {
    return list.value.filter(
      (item) =>
        item.partyName ||
        item.legalRepresentative ||
        item.contactPerson ||
        item.contactPhone ||
        item.address
    )
  }

  return {
    list,
    add,
    remove,
    clear,
    setData,
    getValidData,
    createNewParty
  }
}
