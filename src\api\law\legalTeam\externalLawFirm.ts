/*
 * @Description: 外聘律所管理相关接口
 */
import request from '@/config/axios'

// 外聘律所数据类型
export interface ExternalLawFirmItem {
  id?: number
  name: string // 律所名称
  creditCode: string // 企业信用代码
  address: string // 律所地址
  status: string // 企业状态
  specialties: string[] // 专业领域
  legalRepresentative: string // 创建人(法定代表人)
  contactPerson: string // 联系人
  contactPhone: string // 联系方式
  organization: string // 所属单位
  remark?: string // 备注
  attachments?: string[] // 附件
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 外聘律所操作日志类型
export interface ExternalLawFirmActionLogItem {
  id: number
  time: string // 操作时间
  actionName: string // 操作人
  action: string // 操作类型
  remark?: string // 备注信息
}

// 外聘律所查询参数类型
export interface ExternalLawFirmQueryParams {
  name?: string
  status?: string
  specialties?: string
  organization?: string
  pageNo?: number
  pageSize?: number
}

// 外聘律所列表响应类型
export interface ExternalLawFirmListResponse {
  list: ExternalLawFirmItem[]
  total: number
}

// 获取外聘律所列表
export const getExternalLawFirmListApi = (params: ExternalLawFirmQueryParams) => {
  return request.get<ExternalLawFirmListResponse>({
    url: '/law/external-firm/page',
    params
  })
}

// 获取外聘律所详情
export const getExternalLawFirmDetailApi = (id: number) => {
  return request.get<ExternalLawFirmItem>({
    url: '/law/external-firm/get',
    params: { id }
  })
}

// 删除外聘律所
export const deleteExternalLawFirmApi = (id: number) => {
  return request.delete({
    url: '/law/external-firm/delete',
    params: { id }
  })
}

// 新增外聘律所
export const addExternalLawFirmApi = (data: Partial<ExternalLawFirmItem>) => {
  return request.post({
    url: '/law/external-firm/create',
    data
  })
}

// 编辑外聘律所
export const updateExternalLawFirmApi = (data: Partial<ExternalLawFirmItem>) => {
  return request.put({
    url: '/law/external-firm/update',
    data
  })
}
//导出外聘律所
export const exportExternalLawFirmApi = (params: ExternalLawFirmQueryParams) => {
  return request.download({
    url: '/law/external-firm/export-excel',
    params
  })
}
