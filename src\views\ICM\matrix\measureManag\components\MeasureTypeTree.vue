<template>
  <div class="measure-type-tree">
    <div class="tree-header">
      <span class="tree-title">措施分类</span>
    </div>
    <el-tree
      ref="treeRef"
      :data="treeData"
      :props="treeProps"
      :expand-on-click-node="false"
      :highlight-current="true"
      node-key="id"
      :default-expanded-keys="defaultExpandedKeys"
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <span class="tree-node">
          <Icon :icon="getNodeIcon(data.type)" class="mr-1" />
          <span :class="getNodeClass(data.type)">{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Icon } from '@/components'

interface TreeNode {
  id: string
  label: string
  type: 'root' | 'company' | 'measureType' | 'riskLevel' | 'riskType'
  value?: string
  children?: TreeNode[]
}

// 发送事件
const emit = defineEmits(['node-click'])

// 树形组件引用
const treeRef = ref()

// 树形数据配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 默认展开的节点
const defaultExpandedKeys = ref(['all-measures', 'risk-classification'])

// 树形数据
const treeData = ref<TreeNode[]>([
  {
    id: 'all-measures',
    label: '全部措施',
    type: 'root',
    children: [
      {
        id: 'company-xxx',
        label: 'XXX集团公司',
        type: 'company',
        value: 'XXX集团公司',
        children: [
          {
            id: 'type-system',
            label: '制度措施',
            type: 'measureType',
            value: 'system'
          },
          {
            id: 'type-process',
            label: '流程措施',
            type: 'measureType',
            value: 'process'
          },
          {
            id: 'type-operation',
            label: '操作措施',
            type: 'measureType',
            value: 'operation'
          },
          {
            id: 'type-management',
            label: '管理措施',
            type: 'measureType',
            value: 'management'
          },
          {
            id: 'type-control',
            label: '控制措施',
            type: 'measureType',
            value: 'control'
          },
          {
            id: 'type-monitoring',
            label: '监控措施',
            type: 'measureType',
            value: 'monitoring'
          }
        ]
      }
    ]
  },
  {
    id: 'risk-classification',
    label: '风险分类',
    type: 'root',
    children: [
      {
        id: 'risk-level-group',
        label: '风险等级',
        type: 'root',
        children: [
          {
            id: 'risk-high',
            label: '高风险',
            type: 'riskLevel',
            value: 'high'
          },
          {
            id: 'risk-medium',
            label: '中风险',
            type: 'riskLevel',
            value: 'medium'
          },
          {
            id: 'risk-low',
            label: '低风险',
            type: 'riskLevel',
            value: 'low'
          }
        ]
      },
      {
        id: 'risk-type-group',
        label: '风险类型',
        type: 'root',
        children: [
          {
            id: 'risk-credit',
            label: '信用风险',
            type: 'riskType',
            value: 'credit'
          },
          {
            id: 'risk-market',
            label: '市场风险',
            type: 'riskType',
            value: 'market'
          },
          {
            id: 'risk-operational',
            label: '操作风险',
            type: 'riskType',
            value: 'operational'
          },
          {
            id: 'risk-liquidity',
            label: '流动性风险',
            type: 'riskType',
            value: 'liquidity'
          },
          {
            id: 'risk-reputation',
            label: '声誉风险',
            type: 'riskType',
            value: 'reputation'
          },
          {
            id: 'risk-compliance',
            label: '合规风险',
            type: 'riskType',
            value: 'compliance'
          }
        ]
      }
    ]
  }
])

// 获取节点图标
const getNodeIcon = (type: string): string => {
  switch (type) {
    case 'root':
      return 'ep:folder'
    case 'company':
      return 'ep:office-building'
    case 'measureType':
      return 'ep:document'
    case 'riskLevel':
      return 'ep:warning'
    case 'riskType':
      return 'ep:warning-filled'
    default:
      return 'ep:folder'
  }
}

// 获取节点样式类
const getNodeClass = (type: string): string => {
  switch (type) {
    case 'root':
      return 'font-semibold text-gray-700'
    case 'company':
      return 'font-medium text-blue-600'
    case 'measureType':
      return 'text-green-600'
    case 'riskLevel':
      return 'text-orange-600'
    case 'riskType':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

// 节点点击事件
const handleNodeClick = (data: TreeNode) => {
  emit('node-click', data)
}

onMounted(() => {
  // 默认选中第一个节点
  if (treeRef.value && treeData.value.length > 0) {
    treeRef.value.setCurrentKey('all-measures')
  }
})
</script>

<style lang="scss" scoped>
.measure-type-tree {
  height: 100%;

  .tree-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #f5f7fa;

    .tree-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
  }

  :deep(.el-tree) {
    background-color: transparent;

    .el-tree-node {
      .el-tree-node__content {
        padding: 8px 16px;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-tree-node__expand-icon {
        color: #c0c4cc;
      }
    }

    .is-current > .el-tree-node__content {
      background-color: #e6f7ff;
      color: #1890ff;
    }
  }

  .tree-node {
    display: flex;
    align-items: center;
    font-size: 13px;

    .mr-1 {
      margin-right: 4px;
    }
  }
}
</style>
