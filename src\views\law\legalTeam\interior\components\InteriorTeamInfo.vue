<template>
  <div v-loading="loading">
    <div class="bg-#f1f2fe lh-40px pl-5">基本信息</div>
    <div class="mt-12px">
      <Descriptions :schema="detailConfig" :data="detailData" :columns="2" />
    </div>

    <!-- 附件展示 -->
    <div class="mt-4">
      <div class="bg-#f1f2fe lh-40px pl-5">附件信息</div>
      <div class="mt-12px p-4">
        <UploadFiles preview v-model="detailData.attachment" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Descriptions } from '@/components'
import { defineProps, ref } from 'vue'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getInteriorTeamDetailApi } from '@/api/law/legalTeam/interiorTeam'
import { interiorTeamSchemas, getBasicInfoSchemas } from '../schemas/interiorTeamSchemas'
import type { InteriorTeamItem } from '@/api/law/legalTeam/interiorTeam'

// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(interiorTeamSchemas)

const props = defineProps({
  id: {
    type: Number,
    required: true
  }
})

const detailData = ref<InteriorTeamItem>({} as InteriorTeamItem)
const loading = ref(false)

// 详情配置 - 使用统一Schema
const detailConfig = allSchemas.detailSchema.filter((schema) =>
  getBasicInfoSchemas()
    .map((s) => s.field)
    .includes(schema.field)
)

// 获取详情数据
const getDetail = async (id: number) => {
  if (!id) return

  try {
    loading.value = true
    const data = await getInteriorTeamDetailApi(id)
    detailData.value = data
  } catch (error) {
    console.error('获取详情失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getDetail(props.id)
})
</script>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
