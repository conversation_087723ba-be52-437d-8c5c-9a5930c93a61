<template>
  <div class="type-manag-list">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="allSchemas.searchSchema"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
        />
        <Button type="primary" @click="handleAdd">新建</Button>
        <Button type="primary" @click="handleExport">导出</Button>
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="allSchemas.tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <template #status="{ row }">
          <el-switch
            v-model="row.status"
            active-value="ENABLED"
            inactive-value="DISABLED"
            @change="handleStatusChange(row)"
          />
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleAdd(row)">新增</Button>
          <Button link type="primary" @click="handleEdit(row)">编辑</Button>
          <Button link type="danger" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>
    </ContentWrap>

    <!-- 编辑弹窗 -->
    <UpdateFlowManag
      v-if="showUpdateDialog"
      :mode="dialogConfig.mode"
      :data="dialogConfig.data"
      :parent-info="dialogConfig.parentInfo"
      @success="handleSaveSuccess"
      @close="handleUpdateClose"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { ElMessage, ElMessageBox } from 'element-plus'
import UpdateFlowManag from './components/UpdateFlowManag.vue'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { schemas } from './schemas/flowManagSchemas'
import {
  getTypeManagListApi,
  deleteTypeManagApi,
  exportTypeManagApi
} from '@/api/ICM/matrix/typeManag'
import { useMessage } from '@/hooks/web/useMessage'
const { prompt } = useMessage()

// 使用 CRUD Schemas
const { allSchemas } = useCrudSchemas(schemas)

// 使用表格hook
const {
  tableRegister,
  tableMethods: { getList: getTableList, setSearchParams, exportList },
  tableMethods,
  tableObject
} = useTable({
  getListApi: getTypeManagListApi,
  exportListApi: exportTypeManagApi,
  immediate: true
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 弹窗状态
const showUpdateDialog = ref(false)
const dialogConfig = ref({
  mode: 'add',
  data: {},
  parentInfo: null
})

// 状态切换处理
const handleStatusChange = async (row) => {
  try {
    // 这里可以调用更新状态的API
    ElMessage.success('状态更新成功')
    getTableList()
  } catch (error) {
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 导出
const handleExport = async () => {
  prompt('请输入导出文件名', '批量导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    inputValidator: (value) => {
      if (!value) {
        return '请输入导出文件名'
      }
      return true
    }
  }).then((fileName) => {
    exportList(fileName.value, false)
  })
}

// 新建
const handleAdd = (row) => {
  dialogConfig.value = {
    mode: 'add',
    data: {},
    parentInfo: row
  }
  showUpdateDialog.value = true
}

// 关闭弹窗
const handleUpdateClose = () => {
  showUpdateDialog.value = false
}

// 保存成功回调
const handleSaveSuccess = () => {
  tableMethods.getList()
  showUpdateDialog.value = false
}

// // 查看详情
// const handleView = async (row) => {
//   try {
//     updateRef.value.open('view', row.id)
//   } catch (error) {
//     ElMessage.error('获取详情失败')
//   }
// }

// 编辑
const handleEdit = async (row) => {
  dialogConfig.value = {
    mode: 'edit',
    data: row,
    parentInfo: null
  }
  showUpdateDialog.value = true
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除内控流程类型"${row.type}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteTypeManagApi(row.id)
      ElMessage.success('删除成功')
      getTableList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.type-manag-list {
  height: 100%;
}
</style>
