// 部门树选择组件相关类型定义
export interface DeptTreeSelectProps {
  /** 绑定值 */
  modelValue?: string | number | Array<string | number>
  /** 是否严格的遵循父子不互相关联 */
  checkStrictly?: boolean
  /** 是否支持多选 */
  multiple?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否可筛选 */
  filterable?: boolean
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 空数据时显示的文本 */
  emptyText?: string
  /** 是否在组件挂载时自动加载数据 */
  autoLoad?: boolean
}

export interface DeptTreeSelectEmits {
  /** 更新绑定值 */
  'update:modelValue': [value: any]
  /** 值变化时触发 */
  change: [value: any, node: any]
  /** 清空时触发 */
  clear: []
  /** 获取焦点时触发 */
  focus: []
  /** 失去焦点时触发 */
  blur: []
}

export interface DeptTreeSelectMethods {
  /** 加载部门数据 */
  loadDeptData: () => Promise<void>
  /** 获取选中的节点数据 */
  getSelectedNodes: () => any
  /** 获取选中的节点 key */
  getSelectedKeys: () => number[]
  /** 设置选中的节点 */
  setSelectedKeys: (keys: number[]) => void
  /** 清空选择 */
  clearSelection: () => void
  /** 刷新数据 */
  refresh: () => Promise<void>
}
