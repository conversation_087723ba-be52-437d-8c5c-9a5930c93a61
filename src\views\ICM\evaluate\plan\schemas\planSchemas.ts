import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import {
  PLAN_STATUS_LIST,
  EVALUATION_UNIT_LIST,
  EVALUATED_UNIT_LIST,
  YEAR_LIST,
  CONTROL_LEVEL_LIST,
  CONTROL_TYPE_LIST
} from '../constants/planConstants'

/**
 * 评价计划管理 - 统一Schema配置
 */
export const planSchemas: CrudSchema[] = [
  // ==================== 基础信息组 ====================
  // {
  //   "planYear": "评价计划年度",
  //   "planName": "评价计划名",
  //   "planPeriod": "计划时间区间",
  //   "evaluatingUnitIds": "评价单位ID",
  //   "evaluatedUnitIds": "被评价单位ID",
  //   "description": "评价计划描述",
  //   "status": "1",
  //   "evaluationTasks": [
  //     {
  //       "measureId": "关联措施ID",
  //       "taskName": "评价任务名称",
  //       "companyId": "所属企业ID",
  //       "departmentId": "责任部门ID",
  //       "evaluatingUnitId": "评价单位ID",
  //       "evaluatedUnitId": "被评价单位ID",
  //       "taskDescription": "评价任务描述",
  //       "taskObjective": "评价任务目的",
  //       "status": "1",
  //       "startTime": "2025-07-14",
  //       "endTime": "2025-07-24"
  //     }
  //   ]
  // }
  {
    field: 'planName',
    label: '评价计划名称',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入评价计划名称'
      }
    },
    table: {},
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入评价计划名称',
        style: { width: '100%' },
        maxlength: 200,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'planYear',
    label: '评价计划年度',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择年度',
        options: YEAR_LIST
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择年度',
        options: YEAR_LIST,
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'evaluatingUnitIds',
    label: '评价单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请选择评价单位'
      }
    },
    table: {},
    form: {
      component: 'DeptTreeSelect',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择评价单位(多选)',
        style: { width: '100%' },
        multiple: true,
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'evaluatedUnitIds',
    label: '被评价单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请选择被评价单位'
      }
    },
    table: {
      width: 150
    },
    form: {
      component: 'DeptTreeSelect',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择被评价单位',
        style: { width: '100%' },
        multiple: true,
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'planPeriod',
    label: '评价计划时间范围',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DatePicker',
      componentProps: {
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        valueFormat: 'x',
        format: 'YYYY-MM-DD',
        style: { width: '100%' }
      }
    },
    table: {
      width: 180
    },
    form: {
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        style: { width: '100%' },
        valueFormat: 'x',
        format: 'YYYY-MM-DD',
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'description',
    label: '评价计划描述',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        type: 'textarea',
        placeholder: '请输入评价计划描述',
        style: { width: '100%' },
        maxlength: 500,
        showWordLimit: true,
        rows: 4,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'taskRangeCount',
    label: '评价任务范围数量',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 150,
      formatter: (row) => {
        return row.taskRangeCount ? `${row.taskRangeCount}个` : '0个'
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'status',
    label: '状态',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择状态',
        options: PLAN_STATUS_LIST
      }
    },
    table: {
      width: 100
    },
    form: {},
    detail: {
      span: 1
    }
  },
  {
    field: 'createTime',
    label: '创建时间',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {},
    detail: {
      span: 1
    }
  },
  {
    field: 'action',
    label: '操作',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {
      width: 180,
      fixed: 'right',
      slots: {
        default: 'action'
      }
    }
  }
]

/**
 * 评价任务范围 - Schema配置
 */
export const taskRangeSchemas: CrudSchema[] = [
  {
    field: 'relatedNode',
    label: '关联节点',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  },
  {
    field: 'relatedFlow',
    label: '关联流程',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  },
  {
    field: 'controlFunctionName',
    label: '控制措施名称',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: false,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入控制措施名称'
      }
    },
    table: {}
  },
  {
    field: 'controlFunctionDesc',
    label: '控制措施描述',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  },
  {
    field: 'controlLevel',
    label: '控制级别',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: false,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择控制级别',
        options: CONTROL_LEVEL_LIST
      }
    },
    table: {}
  },
  {
    field: 'controlType',
    label: '控制类型',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: false,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择控制类型',
        options: CONTROL_TYPE_LIST
      }
    },
    table: {}
  },
  {
    field: 'responsibleDept',
    label: '责任部门',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  }
]

/**
 * 表单验证规则
 */
export const getPlanFormRules = () => {
  return {
    planName: [
      { required: true, message: '请输入评价计划名称', trigger: 'blur' },
      { min: 2, max: 200, message: '评价计划名称长度在 2 到 200 个字符', trigger: 'blur' }
    ],
    planYear: [{ required: true, message: '请选择评价计划年度', trigger: 'change' }],
    evaluationUnit: [{ required: true, message: '请选择评价单位', trigger: 'change' }],
    evaluatedUnit: [{ required: true, message: '请选择被评价单位', trigger: 'change' }],
    planTimeRange: [{ required: true, message: '请选择评价计划时间范围', trigger: 'change' }],
    planDescription: [{ max: 500, message: '评价计划描述长度不能超过 500 个字符', trigger: 'blur' }]
  }
}
