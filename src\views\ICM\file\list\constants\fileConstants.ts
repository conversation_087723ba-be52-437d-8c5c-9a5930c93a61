/**
 * 内控文件管理相关常量
 */

import { SelectItemType } from '@/api/public/public.types'

/**
 * 文件状态枚举
 */
export enum FILE_STATUS_ENUM {
  DRAFT = 0, // 草稿
  EFFECTIVE = 1, // 生效
  EXPIRED = 2 // 失效
}

/**
 * 文件状态选项列表
 */
export const FILE_STATUS_LIST: SelectItemType[] = [
  { label: '草稿', value: FILE_STATUS_ENUM.DRAFT },
  { label: '生效', value: FILE_STATUS_ENUM.EFFECTIVE },
  { label: '失效', value: FILE_STATUS_ENUM.EXPIRED }
]

/**
 * 文件类型枚举
 */
export enum FILE_TYPE_ENUM {
  SYSTEM_FILE = '制度文件',
  PROCESS_FILE = '流程文件',
  OPERATION_GUIDE = '操作指南',
  MANAGEMENT_METHOD = '管理办法',
  IMPLEMENTATION_RULES = '实施细则',
  WORK_PROCEDURE = '工作规程',
  BUSINESS_STANDARD = '业务规范',
  OPERATION_MANUAL = '操作手册'
}

/**
 * 文件类型选项列表
 */
export const FILE_TYPE_LIST: SelectItemType[] = [
  { label: '制度文件', value: FILE_TYPE_ENUM.SYSTEM_FILE },
  { label: '流程文件', value: FILE_TYPE_ENUM.PROCESS_FILE },
  { label: '操作指南', value: FILE_TYPE_ENUM.OPERATION_GUIDE },
  { label: '管理办法', value: FILE_TYPE_ENUM.MANAGEMENT_METHOD },
  { label: '实施细则', value: FILE_TYPE_ENUM.IMPLEMENTATION_RULES },
  { label: '工作规程', value: FILE_TYPE_ENUM.WORK_PROCEDURE },
  { label: '业务规范', value: FILE_TYPE_ENUM.BUSINESS_STANDARD },
  { label: '操作手册', value: FILE_TYPE_ENUM.OPERATION_MANUAL }
]

/**
 * 发文部门枚举
 */
export enum DEPARTMENT_ENUM {
  HEAD_OFFICE = '总行',
  RISK_MANAGEMENT = '风险管理部',
  COMPLIANCE = '合规部',
  LEGAL_AFFAIRS = '法律事务部',
  OPERATION_MANAGEMENT = '运营管理部',
  IT = '信息科技部',
  HR = '人力资源部',
  FINANCE = '财务会计部'
}

/**
 * 发文部门选项列表
 */
export const DEPARTMENT_LIST: SelectItemType[] = [
  { label: '总行', value: DEPARTMENT_ENUM.HEAD_OFFICE },
  { label: '风险管理部', value: DEPARTMENT_ENUM.RISK_MANAGEMENT },
  { label: '合规部', value: DEPARTMENT_ENUM.COMPLIANCE },
  { label: '法律事务部', value: DEPARTMENT_ENUM.LEGAL_AFFAIRS },
  { label: '运营管理部', value: DEPARTMENT_ENUM.OPERATION_MANAGEMENT },
  { label: '信息科技部', value: DEPARTMENT_ENUM.IT },
  { label: '人力资源部', value: DEPARTMENT_ENUM.HR },
  { label: '财务会计部', value: DEPARTMENT_ENUM.FINANCE }
]

/**
 * 所属企业枚举
 */
export enum COMPANY_ENUM {
  ALL = '全部',
  XXX_GROUP = 'XXX集团公司'
}

/**
 * 所属企业选项列表
 */
export const COMPANY_LIST: SelectItemType[] = [
  { label: '全部', value: COMPANY_ENUM.ALL },
  { label: 'XXX集团公司', value: COMPANY_ENUM.XXX_GROUP }
]

/**
 * 文件状态映射
 */
export const FILE_STATUS_MAP = {
  [FILE_STATUS_ENUM.DRAFT]: '草稿',
  [FILE_STATUS_ENUM.EFFECTIVE]: '生效',
  [FILE_STATUS_ENUM.EXPIRED]: '失效'
}

/**
 * 文件状态颜色映射
 */
export const FILE_STATUS_COLOR_MAP = {
  [FILE_STATUS_ENUM.DRAFT]: 'warning',
  [FILE_STATUS_ENUM.EFFECTIVE]: 'success',
  [FILE_STATUS_ENUM.EXPIRED]: 'danger'
}

/**
 * 支持的文件类型
 */
// export const SUPPORTED_FILE_TYPES = ['.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx']
export const SUPPORTED_FILE_TYPES = '.doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx'

/**
 * 文件上传最大大小（50MB）
 */
export const MAX_FILE_SIZE = 50 * 1024 * 1024
