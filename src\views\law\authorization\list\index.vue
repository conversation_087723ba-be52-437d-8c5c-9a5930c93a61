<template>
  <div class="authorization-list">
    <ContentWrap v-if="!showDetail">
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="allSchemas.searchSchema"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
        />
        <Button type="primary" @click="handleAdd">新建授权委托</Button>
        <Button type="primary" @click="handleExport">导出</Button>
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="allSchemas.tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleView(row)">详情</Button>
          <Button link type="primary" @click="handleEdit(row)">编辑</Button>
          <Button link type="danger" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>
    </ContentWrap>

    <!-- <UpdateAuthorization ref="updateRef" @close="handleUpdateClose" @save="handleSaveSuccess" /> -->
    <AuthorizationDetail
      v-if="showDetail"
      ref="authorizationDetailRef"
      :id="detailId"
      showClose
      @close="handleDetailClose"
      @save="handleDetailSave"
    />
  </div>
</template>

<script setup>
import { ContentWrap, Search, Button, Table } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import {
  getAuthorizationApplicationPage,
  deleteAuthorizationApplication,
  exportAuthorizationApplication
} from '@/api/law/authorization'
import { ElMessage, ElMessageBox } from 'element-plus'
import { nextTick, onMounted, reactive, ref, toRefs } from 'vue'
// import UpdateAuthorization from './components/UpdateAuthorization.vue'
import AuthorizationDetail from '../detail/index.vue'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { authorizationSchemas } from '../schemas/authorizationSchemas'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
const { prompt } = useMessage()
const router = useRouter()

// 使用 CRUD Schemas
const { allSchemas } = useCrudSchemas(authorizationSchemas)

const authorizationDetailRef = ref()

// 表格相关
const {
  tableObject,
  tableMethods: { getList: getTableList, setSearchParams, exportList },
  tableRegister
} = useTable({
  getListApi: getAuthorizationApplicationPage,
  exportListApi: exportAuthorizationApplication
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 新增
const handleAdd = () => {
  router.push({
    path: '/law/authorization/create',
    query: {
      mode: 'add'
    }
  })
}
const showDetail = ref(false)
const detailId = ref(null)
// 查看详情
const handleView = (row) => {
  showDetail.value = true
  detailId.value = row.id
}

// 编辑
const handleEdit = (row) => {
  router.push({
    path: '/law/authorization/create',
    query: {
      mode: 'edit',
      id: row.id
    }
  })
}

// 删除
const handleDelete = async (row) => {
  ElMessageBox.confirm(`确定要删除授权委托"${row.authorizationCode}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteAuthorizationApplication(row.id)
      ElMessage.success('删除成功')
      await getTableList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 导出
const handleExport = () => {
  prompt('请输入导出文件名', '批量导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    //必填
    inputValidator: (value) => {
      if (!value) {
        return '请输入导出文件名'
      }
      return true
    }
  }).then((fileName) => {
    exportList(fileName.value, false)
  })
}

// 关闭更新弹窗
const handleUpdateClose = () => {
  // 刷新列表
}

// 保存成功
const handleSaveSuccess = () => {
  getTableList()
}

// 关闭详情弹窗
const handleDetailClose = () => {
  showDetail.value = false
  detailId.value = null
  // 处理关闭逻辑
}

// 保存详情
const handleDetailSave = () => {
  getTableList()
}

onMounted(() => {
  getTableList()
})
</script>
