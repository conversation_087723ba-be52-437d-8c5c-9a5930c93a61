<template>
  <ContentWrap title="高级复选框组件示例">
    <div class="example-container">
      <!-- 优点选择 -->
      <div class="section">
        <h3>律师优点选择</h3>
        <AdvancedCheckbox v-model="selectedAdvantages" type="advantage" />
        <div class="selected-info"> 已选择的优点: {{ selectedAdvantages.join(', ') || '无' }} </div>
      </div>

      <!-- 缺点选择 -->
      <div class="section">
        <h3>律师缺点选择</h3>
        <AdvancedCheckbox v-model="selectedDisadvantages" type="disadvantage" />
        <div class="selected-info">
          已选择的缺点: {{ selectedDisadvantages.join(', ') || '无' }}
        </div>
      </div>

      <!-- 显示结果 -->
      <div class="result-section">
        <h3>选择结果</h3>
        <el-card>
          <div
            ><strong>优点:</strong>
            {{ selectedAdvantages.length > 0 ? selectedAdvantages.join('、') : '未选择' }}</div
          >
          <div
            ><strong>缺点:</strong>
            {{
              selectedDisadvantages.length > 0 ? selectedDisadvantages.join('、') : '未选择'
            }}</div
          >
        </el-card>
      </div>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ContentWrap, AdvancedCheckbox } from '@/components'

// 已选择的优点和缺点
const selectedAdvantages = ref<string[]>([])
const selectedDisadvantages = ref<string[]>([])
</script>

<style scoped lang="scss">
.example-container {
  max-width: 800px;

  .section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #303133;
    }

    .selected-info {
      margin-top: 15px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      color: #606266;
    }
  }

  .result-section {
    margin-top: 30px;

    h3 {
      margin-bottom: 15px;
      color: #303133;
    }

    .el-card {
      :deep(.el-card__body) {
        div {
          margin-bottom: 10px;
          line-height: 1.6;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
