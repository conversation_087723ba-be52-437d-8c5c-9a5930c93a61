$border-color: #ebeef5;
$striped: #f8faff;

.d-table {
  // border-top: 1px solid $border-color;
  // border-left: 1px solid $border-color;

  &::before {
    height: 0;
  }

  .el-table__header-wrapper {
    border-radius: 8px 8px 0px 0px;
  }

  .el-table__inner-wrapper {
    &::before {
      height: 0;
    }
  }

  .el-table-column--selection .cell {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .el-table__cell {
    padding: 12px 0px;
  }

  thead > tr > th.el-table__cell {
    color: #222 !important;
    background: #f5f7fa !important;
  }

  th,
  td {
    border-bottom: 1px solid $border-color !important;
    border-right: 1px solid $border-color;
  }

  &.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: $striped !important;
  }
}

.d-pagination {
  .el-pager li {
    font-weight: 400;
  }

  .el-pagination {
    position: static !important;
  }
}
