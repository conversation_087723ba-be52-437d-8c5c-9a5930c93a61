<template>
  <ContentWrap>
    <div class="flex items-start">
      <Search
        ref="searchRef"
        :schema="allSchemas.searchSchema"
        :show-search="true"
        :show-reset="true"
        :expand="false"
        layout="inline"
        @search="setSearchParams"
        @reset="setSearchParams"
      />
      <Button type="primary" @click="handleAdd">新增+</Button>
      <Button type="primary" @click="handleExport">导出</Button>
    </div>
  </ContentWrap>

  <!-- 表格区域 -->
  <ContentWrap>
    <Table
      ref="tableRef"
      v-model:pageSize="pageSize"
      height="calc(70vh)"
      maxHeight="calc(70vh)"
      v-model:currentPage="currentPage"
      :columns="allSchemas.tableColumns"
      :data="tableList"
      :loading="loading"
      :pagination="{
        total: total
      }"
      @register="register"
    >
      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <Button link type="primary" @click="handleView(row)">详情</Button>
        <Button link type="primary" @click="handleEdit(row)">修改</Button>
        <Button link type="danger" @click="handleDelete(row)">删除</Button>
      </template>
    </Table>
  </ContentWrap>

  <!-- 新增/编辑弹窗 -->
  <PlanForm ref="planFormRef" @close="handleFormClose" @save="handleSaveSuccess" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getEvaluationPlanListApi,
  deleteEvaluationPlanApi,
  exportEvaluationPlanApi
} from '@/api/ICM/evaluate'
import { planSchemas } from './schemas/planSchemas'
import { PLAN_STATUS_MAP, PLAN_STATUS_COLOR_MAP } from './constants/planConstants'
import { ElMessage, ElMessageBox } from 'element-plus'
import PlanForm from './components/PlanForm.vue'

defineOptions({ name: 'ICMEvaluatePlan' })

// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(planSchemas)

// 使用表格hook
const {
  register,
  tableMethods: { getList: getTableList, setSearchParams },
  tableObject
} = useTable({
  getListApi: getEvaluationPlanListApi
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 弹窗状态
const planFormRef = ref()

// 获取状态类型
const getStatusType = (status: number) => {
  return PLAN_STATUS_COLOR_MAP[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  return PLAN_STATUS_MAP[status] || '未知'
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      ...searchParams.value
    }

    await exportEvaluationPlanApi(params)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 新建
const handleAdd = () => {
  planFormRef.value.open('add')
}

// 编辑
const handleEdit = (row: any) => {
  planFormRef.value.open('edit', row.id)
}

// 查看详情
const handleView = (row: any) => {
  planFormRef.value.open('view', row.id)
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该评价计划吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteEvaluationPlanApi(row.id)
    ElMessage.success('删除成功')

    // 刷新列表
    getTableList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 关闭新增/编辑弹窗
const handleFormClose = () => {
  // 弹窗关闭逻辑
}

// 保存成功
const handleSaveSuccess = () => {
  // 刷新列表
  getTableList()
}

onMounted(() => {
  getTableList()
})
</script>

<style scoped lang="scss"></style>
