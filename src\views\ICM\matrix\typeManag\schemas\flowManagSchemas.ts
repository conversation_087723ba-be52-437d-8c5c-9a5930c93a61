import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
/**
 * 案件管理 - 统一Schema配置
 * 通过 isSearch、isTable、isForm、isDetail 控制字段在不同场景下的显示
 */
export const schemas: CrudSchema[] = [
  // ==================== 序号列 ====================
  {
    field: 'index',
    label: '序号',
    type: 'index',
    isTable: true,
    isSearch: false,
    isForm: false
  },

  // ==================== 基础信息 ====================
  {
    field: 'processTypeName',
    label: '内控流程类型',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入内控流程类型'
      }
      // colProps: {
      //   span: 24
      // }
    },
    table: {
      // width: 180
    },
    form: {
      component: 'Input',
      componentProps: {
        style: {
          width: '100%'
        },
        maxlength: 50,
        clearable: true
      }
    }
  },

  {
    field: 'companyId',
    label: '所属企业',
    isSearch: true,
    isTable: true,
    isForm: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请选择',
        style: {
          width: '120px'
        }
      }
    },
    table: {
      // width: 160
    },
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: {
          width: '100%'
        },
        clearable: true
      }
    }
  },
  {
    field: 'status',
    label: '状态',
    isSearch: false,
    isTable: true,
    isForm: true,
    isDetail: true,

    table: {
      // width: 100
    },
    form: {
      component: 'Switch',
      componentProps: {
        placeholder: '请选择状态',
        activeValue: 'ENABLED',
        inactiveValue: 'DISABLED'
      }
    }
  },
  {
    field: 'description',
    label: '相关描述',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      // width: 200
    },
    form: {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        // maxlength: 200,
        clearable: true,
        style: {
          width: '100%'
        }
      }
    }
  },

  {
    //最近更新时间
    field: 'createTime',
    label: '最近更新时间',
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {
      formatter: dateFormatter
    }
  },
  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isTable: true,
    table: {
      width: 200,
      fixed: 'right'
    }
  }
]
