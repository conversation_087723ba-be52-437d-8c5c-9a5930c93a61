<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div
      v-if="visible"
      class="px-4 box-border bg-[white] duration-[0.2s] fixed z-990 top-50px h-[calc(100vh-65px)]"
      :class="{
        'left-200px w-[calc(100%-200px)] ': true
      }"
    >
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            showSave
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSave"
          />
        </div>
      </div>
      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
          <div class="mt-12px">
            <Form
              :schema="allSchemas.formSchema"
              @register="register"
              isCol
              :rules="formRules"
              :model="formData"
            />
          </div>

          <div class="bg-#f1f2fe lh-40px pl-5 mt-20px">评价任务范围</div>
          <div class="mt-12px">
            <div class="flex items-center mb-4">
              <Button type="primary" @click="handleAddTaskRange">添加评价任务范围</Button>
            </div>
            <div class="border rounded-lg p-4 min-h-200px">
              <div>
                <Table
                  :columns="taskRangeColumns"
                  :data="formData.taskRanges"
                  :show-selection="false"
                  max-height="300px"
                >
                  <template #action="{ $index }">
                    <Button link type="danger" @click="handleRemoveTaskRange($index)">
                      删除
                    </Button>
                  </template>
                </Table>
              </div>
            </div>
          </div>

          <div class="bg-#f1f2fe lh-40px pl-5 mt-20px">附件</div>
          <div class="mt-12px">
            <AttachmentUpload
              v-model="formData.attachments"
              :mode="mode"
              @change="handleAttachmentChange"
            />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>

  <!-- 任务范围选择弹窗 -->
  <TaskRangeDialog ref="taskRangeDialogRef" @confirm="handleTaskRangeConfirm" />
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { Back, Form, Table, Button } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getEvaluationPlanDetailApi,
  createEvaluationPlanApi,
  updateEvaluationPlanApi
} from '@/api/ICM/evaluate'
import { planSchemas, taskRangeSchemas, getPlanFormRules } from '../schemas/planSchemas'
import { useMessage } from '@/hooks/web/useMessage'
import AttachmentUpload from './AttachmentUpload.vue'
import TaskRangeDialog from './TaskRangeDialog.vue'

// 使用 CRUD Schemas 生成表单配置
const { allSchemas } = useCrudSchemas(planSchemas)

// 任务范围表格列配置
const taskRangeColumns = computed(() => {
  const columns = useCrudSchemas(taskRangeSchemas).allSchemas.tableColumns
  // 添加操作列
  columns.push({
    field: 'action',
    label: '操作',
    width: 80,
    slots: { default: 'action' }
  })
  return columns
})

const message = useMessage()

// 发送事件
const emit = defineEmits(['save', 'close'])

// 响应式数据
const visible = ref(false)
const mode = ref('add') // add, edit, view
const planId = ref(null)
const formData = ref({
  attachments: [],
  taskRanges: []
})

// 标题计算属性
const title = computed(() => {
  const titleMap = {
    add: '新增评价计划',
    edit: '编辑评价计划',
    view: '查看评价计划'
  }
  return titleMap[mode.value] || '评价计划'
})

// 表单验证规则
const formRules = reactive(getPlanFormRules())

// 使用表单Hook
const { register, methods } = useForm()

// 任务范围弹窗引用
const taskRangeDialogRef = ref()

// 设置表单数据
const setValue = async () => {
  if ((mode.value === 'edit' || mode.value === 'view') && planId.value) {
    try {
      const res = await getEvaluationPlanDetailApi(planId.value)
      const data = res
      formData.value = {
        ...data,
        attachments: data.attachments || [],
        taskRanges: data.taskRanges || []
      }

      // 设置表单值
      await nextTick()
      methods.setValues(formData.value)
    } catch (error) {
      message.error('获取详情失败')
    }
  } else {
    // 新增模式，重置表单
    formData.value = {
      attachments: [],
      taskRanges: []
    }
    await nextTick()
    methods.resetFields()
  }
}

// 打开弹窗
const open = async (openMode: string, id?: number) => {
  mode.value = openMode
  planId.value = id
  visible.value = true

  await nextTick()
  await setValue()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  emit('close')
}

// 保存
const handleSave = async () => {
  try {
    const valid = await methods.validate()
    if (!valid) return

    const formValues = methods.getValues()
    const submitData = {
      ...formValues,
      attachments: formData.value.attachments,
      taskRanges: formData.value.taskRanges,
      taskRangeCount: formData.value.taskRanges.length
    }

    if (mode.value === 'add') {
      await createEvaluationPlanApi(submitData)
      message.success('创建成功')
    } else if (mode.value === 'edit') {
      await updateEvaluationPlanApi({ ...submitData, id: planId.value })
      message.success('更新成功')
    }

    handleClose()
    emit('save')
  } catch (error) {
    message.error('保存失败')
  }
}

// 添加任务范围
const handleAddTaskRange = () => {
  // 传递已有的任务范围数据给弹窗，以便在编辑模式下正确显示
  const existingTaskRanges = formData.value.taskRanges || []
  taskRangeDialogRef.value.open(existingTaskRanges)
}

// 删除任务范围
const handleRemoveTaskRange = (index: number) => {
  formData.value.taskRanges.splice(index, 1)
}

// 任务范围确认
const handleTaskRangeConfirm = (selectedRanges: any[]) => {
  // 直接替换任务范围数据，因为弹窗返回的是所有选中的数据
  formData.value.taskRanges = [...selectedRanges]
}

// 附件变化处理
const handleAttachmentChange = (attachments: any[]) => {
  formData.value.attachments = attachments
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped lang="scss"></style>
