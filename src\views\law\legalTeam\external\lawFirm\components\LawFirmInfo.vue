<template>
  <div v-loading="loading">
    <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
    <div class="mt-12px">
      <Descriptions :schema="allSchemas.detailSchema" :data="detailData" :columns="2" />
    </div>

    <!-- 专业领域展示 -->
    <div v-if="detailData.specialties && detailData.specialties.length > 0" class="mt-4">
      <div class="bg-#f1f2fe lh-40px pl-5">专业领域</div>
      <div class="mt-12px p-4">
        <div class="flex flex-wrap gap-2">
          <el-tag v-for="(specialty, index) in detailData.specialties" :key="index" type="info">
            {{ specialty }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 附件展示 -->
    <div class="mt-4">
      <div class="bg-#f1f2fe lh-40px pl-5">附件信息</div>
      <div class="mt-12px p-4">
        <UploadFiles preview v-model="detailData.attachment" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Descriptions } from '@/components'
import { defineProps, watch, ref } from 'vue'
import { getExternalLawFirmDetailApi } from '@/api/law/legalTeam/externalLawFirm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getBasicInfoSchemas } from '../schemas/lawFirmSchemas'
import type { ExternalLawFirmItem } from '@/api/law/legalTeam/externalLawFirm'

// 使用 CRUD Schemas 生成详情配置
const basicSchemas = getBasicInfoSchemas()
const { allSchemas } = useCrudSchemas(basicSchemas)

const props = defineProps({
  id: {
    type: Number,
    required: true
  }
})

const detailData = ref<ExternalLawFirmItem>({} as ExternalLawFirmItem)
const loading = ref(false)

// 获取详情数据
const getDetail = async (id: number) => {
  if (!id) return

  try {
    loading.value = true
    const data = await getExternalLawFirmDetailApi(id)
    detailData.value = data
  } catch (error) {
    console.error('获取详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听id变化
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      getDetail(newId)
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
