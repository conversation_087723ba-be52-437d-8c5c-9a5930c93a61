import request from '@/config/axios'

// 获取内控措施分页列表
export const getMeasureListApi = (params: any) => {
  return request.get({ url: 'ic/measure/page', params })
}

// 获取内控措施详情
export const getMeasureDetailApi = (id: string) => {
  return request.get({ url: '/ic/measure/get', params: { id } })
}

// 创建内控措施
export const createMeasureApi = (data: any) => {
  return request.post({ url: 'ic/measure/create', data })
}

// 更新内控措施
export const updateMeasureApi = (data: any) => {
  return request.put({ url: 'ic/measure/update', data })
}

// 删除内控措施
export const deleteMeasureApi = (id: string) => {
  return request.delete({ url: 'ic/measure/delete', params: { id } })
}

// 导出内控措施
export const exportMeasureApi = (params: any) => {
  return request.get({ url: 'ic/measure/export-excel', params })
}

// 获取风险点选项列表
export const getRiskPointListApi = () => {
  return request.get({ url: '/api/ICM/measure/risk-points' })
}

// 获取控制细项编号选项列表
export const getControlItemListApi = () => {
  return request.get({ url: '/api/ICM/measure/control-items' })
}
