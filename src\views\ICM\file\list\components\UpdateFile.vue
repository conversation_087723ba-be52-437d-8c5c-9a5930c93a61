<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div
      v-if="visible"
      class="px-4 box-border bg-[white] duration-[0.2s] fixed z-990 top-50px h-[calc(100vh-65px)]"
      :class="{
        'left-200px w-[calc(100%-200px)] ': true
      }"
    >
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            showSave
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSave"
          />
        </div>
      </div>
      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
          <div class="mt-12px">
            <Form
              :schema="allSchemas.formSchema"
              @register="register"
              isCol
              :rules="formRules"
              :model="formData"
            />
          </div>

          <div class="bg-#f1f2fe lh-40px pl-5 mt-20px">附件</div>
          <div class="mt-12px">
            <AttachmentManager
              v-model="formData.attachments"
              :mode="mode"
              @change="handleAttachmentChange"
            />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getICMFileDetailApi, createICMFileApi, updateICMFileApi } from '@/api/ICM/file'
import { fileSchemas, getFileFormRules } from '../schemas/fileSchemas'
import { useMessage } from '@/hooks/web/useMessage'
import AttachmentManager from './AttachmentManager.vue'

// 使用 CRUD Schemas 生成表单配置
const { allSchemas } = useCrudSchemas(fileSchemas)

const message = useMessage()

// 发送事件
const emit = defineEmits(['save', 'close'])

// 响应式数据
const visible = ref(false)
const mode = ref('add') // add 或 edit
const fileId = ref(null)
const formData = ref({
  attachments: []
})

// 标题计算属性
const title = computed(() => {
  return mode.value === 'add' ? '新增内控文件' : '修订内控文件'
})

// 表单验证规则
const formRules = reactive(getFileFormRules())

// 使用表单Hook
const { register, methods } = useForm()

// 设置表单数据
const setValue = async () => {
  if (mode.value === 'edit' && fileId.value) {
    try {
      const res = await getICMFileDetailApi(fileId.value)

      // if (res && res.code === 200 && res.data) {
      const data = res
      formData.value = {
        ...data,
        attachments: data.attachments || []
      }

      // 设置表单值
      const formValues = {}
      allSchemas.formSchema.forEach((item) => {
        formValues[item.field] = data[item.field]
      })
      methods.setValues(formValues)
      // }
    } catch (error) {
      console.error('加载文件详情失败:', error)
      message.error('加载文件详情失败')
    }
  } else {
    // 新增模式，重置表单
    formData.value = {
      attachments: []
    }
    methods.resetFields()
  }
}

// 打开弹窗
const open = async (type, id = null) => {
  mode.value = type
  fileId.value = id
  visible.value = true

  await nextTick()
  await setValue()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  fileId.value = null
  formData.value = {
    attachments: []
  }
  emit('close')
}

// 附件变化处理
const handleAttachmentChange = (attachments) => {
  formData.value.attachments = attachments
}

// 保存
const handleSave = async () => {
  try {
    const valid = await methods.validate()
    if (!valid) return

    const formValues = await methods.getFormData()

    // 合并表单数据和附件数据
    const submitData = {
      ...formValues,
      attachments: formData.value.attachments
    }

    if (mode.value === 'add') {
      await createICMFileApi(submitData)
      message.success('新增成功')
    } else {
      submitData.id = fileId.value
      await updateICMFileApi(submitData)
      message.success('修订成功')
    }

    handleClose()
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  }
}

// 对外暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.bg-\#f1f2fe {
  background-color: #f1f2fe;
}

.lh-40px {
  line-height: 40px;
}

.pl-5 {
  padding-left: 20px;
}

.mt-12px {
  margin-top: 12px;
}

.mt-20px {
  margin-top: 20px;
}
</style>
