<script setup>
import { ref, computed, nextTick } from 'vue'
import { Back } from '@/components'
import KnowledgeInfo from './KnowledgeInfo.vue'
import KnowledgeLog from './KnowledgeLog.vue'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle, layoutSize, isCollapsed } = useLayout()
import { LogList } from '@/components/LogList'
// 发送事件
const emit = defineEmits(['close'])

// 响应式数据
const visible = ref(false)
const editId = ref(null)
const activeTab = ref('基本信息')

const title = computed(() => '知识详情')

// Tab列表配置
const tabList = [
  {
    name: '基本信息',
    component: KnowledgeInfo
  },
  {
    name: '操作日志',
    component: LogList
  }
]
const data = ref({})
// 打开详情页
const open = async (row) => {
  visible.value = true
  data.value = row
  console.log(data.value)

  activeTab.value = '基本信息'
  await nextTick()
}

// 关闭详情页
const handleClose = () => {
  visible.value = false
  editId.value = null
  activeTab.value = '基本信息'
  emit('close')
}

// 对外暴露方法
defineExpose({
  open
})
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            title="知识详情"
            hasEmit
            showCancel
            :showSave="false"
            @back="handleClose"
            @cancel="handleClose"
          />
        </div>
      </div>
      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <ElTabs type="border-card" v-model="activeTab">
          <ElTabPane v-for="item in tabList" :key="item.name" :label="item.name" :name="item.name">
            <component :is="item.component" :data="data" :id="data.id" v-if="data" :type="5" />
          </ElTabPane>
        </ElTabs>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
// 样式已移动到子组件中
</style>
