<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0 my-18px">
        <div class="flex items-center flex-1 pos-relative">
          <Back
            :title="title"
            hasEmit
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSaveAction"
          />
          <div class="pos-absolute right-0 top-0 h-full flex items-center">
            <div class="flex mr-28px items-center">
              <div>当前状态：</div>
              <el-tag type="primary" size="large">{{ getStatusText(taskData.status) }}</el-tag>
            </div>
          </div>
        </div>
      </div>

      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div class="p-6">
          <!-- 基础信息展示 -->
          <div class="mb-6">
            <Title title="基础信息">
              <Descriptions
                :data="taskData"
                :columns="2"
                :schema="basicInfoSchema"
                :collapse="false"
              />
            </Title>
          </div>

          <!-- 评价任务描述 -->
          <div class="mb-6">
            <Title title="评价任务描述">
              <el-form :model="formData" label-width="120px">
                <el-form-item label="任务描述">
                  <el-input
                    v-model="formData.taskDescription"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入评价任务描述"
                    maxlength="2000"
                    show-word-limit
                  />
                </el-form-item>
              </el-form>
            </Title>
          </div>

          <!-- 评价任务目的 -->
          <div class="mb-6">
            <Title title="评价任务目的">
              <el-form :model="formData" label-width="120px">
                <el-form-item label="任务目的">
                  <el-input
                    v-model="formData.taskPurpose"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入评价任务目的"
                    maxlength="2000"
                    show-word-limit
                  />
                </el-form-item>
              </el-form>
            </Title>
          </div>

          <!-- 领导小组 -->
          <div class="mb-6">
            <Title title="领导小组">
              <GroupMemberTable
                v-model="formData.leadershipGroup"
                title="领导小组"
                :disabled="mode === 'view'"
              />
            </Title>
          </div>

          <!-- 评价工作小组 -->
          <div class="mb-6">
            <Title title="评价工作小组">
              <GroupMemberTable
                v-model="formData.workingGroup"
                title="评价工作小组"
                :disabled="mode === 'view'"
              />
            </Title>
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<script setup>
import { computed, ref, reactive, nextTick } from 'vue'
import { Back, Descriptions, Title } from '@/components'
import { useMessage } from '@/hooks/web/useMessage'
import { useLayout } from '@/hooks/web/useLayout'
import GroupMemberTable from '../components/GroupMemberTable.vue'
// import { getEvaluationTaskDetailApi, updateEvaluationTaskConfigApi } from '@/api/ICM/evaluate'

const { getModalStyle } = useLayout()
const message = useMessage()

const emit = defineEmits(['close', 'save'])

const mode = ref('config') // 模式：config, view
const visible = ref(false)
const editId = ref(null)

const title = computed(() => {
  switch (mode.value) {
    case 'config':
      return '评价任务配置'
    case 'view':
      return '查看任务配置'
    default:
      return '任务配置'
  }
})

// 任务基础数据
const taskData = ref({
  controlFunctionName: '',
  planName: '',
  evaluationUnit: '',
  evaluatedUnit: '',
  year: '',
  planTimeRange: '',
  relatedNode: '',
  relatedFlow: '',
  responsibleDept: '',
  status: '',
  createTime: ''
})

// 表单数据
const formData = reactive({
  taskDescription: '',
  taskPurpose: '',
  leadershipGroup: [],
  workingGroup: []
})

// 基础信息展示配置
const basicInfoSchema = [
  {
    field: 'controlFunctionName',
    label: '评价措施任务名称'
  },
  {
    field: 'planName',
    label: '评价计划名称'
  },
  {
    field: 'evaluationUnit',
    label: '评价单位'
  },
  {
    field: 'evaluatedUnit',
    label: '被评价单位'
  },
  {
    field: 'year',
    label: '年度'
  },
  {
    field: 'planTimeRange',
    label: '评价计划时间范围'
  },
  {
    field: 'relatedNode',
    label: '关联节点'
  },
  {
    field: 'relatedFlow',
    label: '关联流程'
  },
  {
    field: 'responsibleDept',
    label: '责任部门'
  },
  {
    field: 'createTime',
    label: '创建时间'
  }
]

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    NOT_STARTED: '未开始',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    PAUSED: '已暂停'
  }
  return statusMap[status] || '未知'
}

// 打开弹窗
const open = async (type, id) => {
  mode.value = type
  visible.value = true
  editId.value = id

  if (id) {
    await getTaskDetail(id)
  }
}

// 获取任务详情
const getTaskDetail = async (id) => {
  try {
    // const res = await getEvaluationTaskDetailApi(id)
    // 模拟数据
    const res = {
      id: id,
      controlFunctionName: '分级授权审批',
      planName: '2025年3季度制度与流程评价计划',
      evaluationUnit: 'XXX公司, XXX公司',
      evaluatedUnit: 'XXX公司',
      year: '2025',
      planTimeRange: '2025.7.1-2025.9.30',
      relatedNode: '节点名称',
      relatedFlow: '流程名称',
      responsibleDept: 'XXX部',
      status: 'NOT_STARTED',
      createTime: '2025.06.10',
      taskDescription: '',
      taskPurpose: '',
      leadershipGroup: [],
      workingGroup: []
    }

    taskData.value = { ...res }
    formData.taskDescription = res.taskDescription || ''
    formData.taskPurpose = res.taskPurpose || ''
    formData.leadershipGroup = res.leadershipGroup || []
    formData.workingGroup = res.workingGroup || []
  } catch (error) {
    message.error('获取任务详情失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  editId.value = ''
  emit('close')
}

// 保存配置
const handleSaveAction = async () => {
  try {
    if (mode.value === 'view') {
      handleClose()
      return
    }

    const saveData = {
      id: editId.value,
      taskDescription: formData.taskDescription,
      taskPurpose: formData.taskPurpose,
      leadershipGroup: formData.leadershipGroup,
      workingGroup: formData.workingGroup
    }

    // await updateEvaluationTaskConfigApi(saveData)
    console.log('保存配置数据：', saveData)

    message.success('保存成功')
    emit('save')
    handleClose()
  } catch (error) {
    message.error('保存失败')
  }
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
:deep(.el-descriptions__body) {
  background-color: #fafafa;
}
</style>
