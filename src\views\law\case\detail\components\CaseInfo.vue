<template>
  <div class="case-info">
    <Descriptions
      title="案件详情"
      :data="userData"
      :columns="2"
      :schema="basicAllSchemas.detailSchema"
      :collapse="false"
    ></Descriptions>

    <Descriptions
      title="诉讼信息"
      :data="userData"
      :columns="2"
      :schema="litigationAllSchemas.detailSchema"
      :collapse="false"
    ></Descriptions>

    <Title title="原被告信息">
      <div>
        <div class="text-center my-8px">原告/申请人</div>
        <Table :columns="tableColumns" :data="userData.plaintiffs || []"></Table>
      </div>
      <div>
        <div class="text-center my-8px">被告/被申请人</div>
        <Table :columns="tableColumns" :data="userData.defendants || []"></Table>
      </div>
      <div>
        <div class="text-center my-8px">第三人</div>
        <Table :columns="tableColumns" :data="userData.thirdParties || []"></Table>
      </div>
    </Title>

    <Descriptions
      title="经办信息"
      :data="userData"
      :columns="2"
      :schema="processingInfoSchema"
      :collapse="false"
    ></Descriptions>
  </div>
</template>

<script setup>
import { Back, Form, Descriptions, Title, Table } from '@/components'
import { getDictLabel, DICT_TYPE } from '@/utils/dict'
import { defineProps, watch, ref, reactive, onMounted } from 'vue'
import { getCaseDetailApi, addCaseApi, updateCaseApi } from '@/api/law/case'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getBasicSchemas, getLitigationSchemas } from '../../schemas/caseSchemas'
// 使用 CRUD Schemas 生成详情配置
const basicSchemas = getBasicSchemas()
const litigationSchemas = getLitigationSchemas()
const { allSchemas: basicAllSchemas } = useCrudSchemas(basicSchemas)
const { allSchemas: litigationAllSchemas } = useCrudSchemas(litigationSchemas)

const props = defineProps({
  id: {
    type: [String, Number],
    required: false,
    default: null
  }
})
const userData = ref({
  plaintiffs: [],
  defendants: [],
  thirdParties: [],
  caseName: '',
  caseNo: '',
  caseType: '',
  caseProcedure: '',
  involvedAmount: '',
  caseRemark: '',
  isMajor: '',
  isForeign: '',
  belongOrganization: '',
  litigationReason: '',
  litigationDate: '',
  litigationPosition: '',
  lawyer: '',
  caseStatus: '',
  acceptanceAgency: '',
  acceptanceAgencyType: '',
  acceptanceHandler: '',
  acceptanceHandlerPhone: ''
})
const tableData = reactive([
  {
    name: '张三',
    legalRepresentative: '李四',
    contact: '王五',
    phone: '12345678901'
  },
  {
    name: '赵六',
    legalRepresentative: '钱七',
    contact: '孙八',
    phone: '09876543210'
  }
])
const tableColumns = [
  {
    field: 'index',
    label: '序号',
    type: 'index'
  },
  {
    field: 'partyName',
    label: '姓名'
  },
  {
    field: 'legalRepresentative',
    label: '法定代表人'
  },
  {
    field: 'contactPerson',
    label: '联系人'
  },
  {
    field: 'contactPhone',
    label: '联系电话'
  },
  {
    field: 'address',
    label: '地址'
  }
]

const processingInfoSchema = [
  //经办人 时间  单位  部门
  {
    field: 'acceptanceHandler',
    label: '经办人'
  },
  {
    field: 'acceptanceDate',
    label: '经办时间'
  },
  {
    field: 'acceptanceAgency',
    label: '经办单位'
  },
  {
    field: 'acceptanceDepartment',
    label: '经办部门'
  }
]
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      getCaseDetailApi(newId).then((res) => {
        userData.value = { ...userData.value, ...res }
        userData.value.plaintiffs = res.casePartiess.filter(
          (item) => item.partyType === 'PLAINTIFF'
        )
        userData.value.defendants = res.casePartiess.filter(
          (item) => item.partyType === 'DEFENDANT'
        )
        userData.value.thirdParties = res.casePartiess.filter(
          (item) => item.partyType === 'THIRD_PARTY'
        )
      })
    } else {
      // 新增时清空数据
      userData.value = {
        plaintiffs: [],
        defendants: [],
        thirdParties: [],
        caseName: '',
        caseNo: '',
        caseType: '',
        caseProcedure: '',
        involvedAmount: '',
        caseRemark: '',
        isMajor: '',
        isForeign: '',
        belongOrganization: '',
        litigationReason: '',
        litigationDate: '',
        litigationPosition: '',
        lawyer: '',
        caseStatus: '',
        acceptanceAgency: '',
        acceptanceAgencyType: '',
        acceptanceHandler: '',
        acceptanceHandlerPhone: ''
      }
    }
  },
  { immediate: true }
)

// // 组件挂载时立即获取数据
// onMounted(() => {
//   console.log(props.id, 1111)

//   if (props.id) {
//     console.log('拿数据')

//     getCaseDetailApi(props.id).then((res) => {
//       userData.value = res
//     })
//   }
// })
</script>

<style lang="less" scoped></style>
