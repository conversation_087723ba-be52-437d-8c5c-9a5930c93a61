<template>
  <div class="interior-team-list">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="allSchemas.searchSchema"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
        />
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button type="primary" @click="handleExport">导出</Button>
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="allSchemas.tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleView(row)">查看详情</Button>
          <Button link type="primary" @click="handleEdit(row)">编辑</Button>
          <Button link type="danger" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>
    </ContentWrap>

    <!-- 新增/编辑弹窗 -->
    <UpdateInteriorTeam ref="updateRef" @close="handleUpdateClose" @save="handleSaveSuccess" />

    <!-- 详情弹窗 -->
    <InteriorTeamDetail ref="detailRef" @close="handleDetailClose" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getInteriorTeamListApi,
  deleteInteriorTeamApi,
  exportInteriorTeamApi
} from '@/api/law/legalTeam/interiorTeam'
import { interiorTeamSchemas } from './schemas/interiorTeamSchemas'
import { ElMessage, ElMessageBox } from 'element-plus'
import UpdateInteriorTeam from './components/UpdateInteriorTeam.vue'
import InteriorTeamDetail from './components/InteriorTeamDetail.vue'
import { useMessage } from '@/hooks/web/useMessage'
const { prompt } = useMessage()
// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(interiorTeamSchemas)

// 搜索组件引用
const searchRef = ref()

const {
  tableObject,
  tableMethods: { getList: getTableList, setSearchParams, exportList },
  tableRegister
} = useTable({
  getListApi: getInteriorTeamListApi,
  immediate: true,
  exportListApi: exportInteriorTeamApi
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 弹窗状态
const updateRef = ref()
const detailRef = ref()

// 导出
const handleExport = () => {
  prompt('请输入导出文件名', '批量导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    inputValidator: (value) => {
      if (!value) {
        return '请输入导出文件名'
      }
      return true
    }
  }).then((fileName) => {
    exportList(fileName.value, false)
  })
}

// 新建
const handleAdd = () => {
  updateRef.value.open('add')
}

// 关闭新增/编辑弹窗
const handleUpdateClose = () => {
  // 弹窗关闭逻辑
}

// 保存成功回调
const handleSaveSuccess = () => {
  tableMethods.getList()
}

// 查看详情
const handleView = (row) => {
  detailRef.value.open(row.id)
}

// 关闭详情弹窗
const handleDetailClose = () => {
  // 详情弹窗关闭逻辑
}

// 编辑
const handleEdit = (row) => {
  updateRef.value.open('edit', row.id)
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除"${row.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteInteriorTeamApi(row.id)
      ElMessage.success('删除成功')
      getTableList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.interior-team-list {
  height: 100%;
}
</style>
