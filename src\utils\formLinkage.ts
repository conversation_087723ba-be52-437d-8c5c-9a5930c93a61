import { FormLinkageConfig } from '@/types/form'
import { ComponentOptions } from '@/types/components'

/**
 * 创建简单的一对一联动配置
 * @param triggerField 触发字段
 * @param targetField 目标字段
 * @param api API函数
 * @param optionMapper 选项映射函数
 */
export const createSimpleLinkage = (
  triggerField: string,
  targetField: string,
  api: (value: any, formModel?: Recordable) => Promise<any>,
  optionMapper?: (data: any) => ComponentOptions[]
): FormLinkageConfig => {
  return {
    triggerField,
    targetField,
    api,
    optionMapper,
    clearValue: true,
    clearOptions: true
  }
}

/**
 * 创建条件联动配置
 * @param triggerField 触发字段
 * @param targetField 目标字段
 * @param api API函数
 * @param condition 联动条件
 * @param optionMapper 选项映射函数
 */
export const createConditionalLinkage = (
  triggerField: string,
  targetField: string,
  api: (value: any, formModel?: Recordable) => Promise<any>,
  condition: (value: any, formModel?: Recordable) => boolean,
  optionMapper?: (data: any) => ComponentOptions[]
): FormLinkageConfig => {
  return {
    triggerField,
    targetField,
    api,
    condition,
    optionMapper,
    clearValue: true,
    clearOptions: true
  }
}

/**
 * 创建链式联动配置
 * @param linkageChain 联动链配置
 */
export const createChainLinkage = (
  linkageChain: Array<{
    triggerField: string
    targetField: string
    api: (value: any, formModel?: Recordable) => Promise<any>
    optionMapper?: (data: any) => ComponentOptions[]
  }>
): FormLinkageConfig[] => {
  return linkageChain.map((item) => ({
    ...item,
    clearValue: true,
    clearOptions: true
  }))
}

/**
 * 创建不清空值的联动配置（仅更新选项）
 * @param triggerField 触发字段
 * @param targetField 目标字段
 * @param api API函数
 * @param optionMapper 选项映射函数
 */
export const createKeepValueLinkage = (
  triggerField: string,
  targetField: string,
  api: (value: any, formModel?: Recordable) => Promise<any>,
  optionMapper?: (data: any) => ComponentOptions[]
): FormLinkageConfig => {
  return {
    triggerField,
    targetField,
    api,
    optionMapper,
    clearValue: false, // 不清空值
    clearOptions: true
  }
}

/**
 * 默认选项映射函数
 * @param data 原始数据数组
 * @returns 组件选项数组
 */
export const defaultOptionMapper = (data: any[]): ComponentOptions[] => {
  if (!Array.isArray(data)) return []

  return data.map((item) => ({
    label: item.name || item.label || item.title,
    value: item.id || item.value || item.code,
    ...item
  }))
}

/**
 * 自定义字段映射函数（针对特定API数据结构）
 * @param labelField 标签字段名
 * @param valueField 值字段名
 * @returns 映射函数
 */
export const customFieldMapper = (labelField: string = 'name', valueField: string = 'id') => {
  return (data: any[]): ComponentOptions[] => {
    if (!Array.isArray(data)) return []

    return data.map((item) => ({
      label: item[labelField],
      value: item[valueField],
      ...item
    }))
  }
}

/**
 * 树形数据映射函数
 * @param data 树形数据数组
 * @returns 树形组件选项数组
 */
export const treeOptionMapper = (data: any[]): ComponentOptions[] => {
  if (!Array.isArray(data)) return []

  const mapTree = (nodes: any[]): ComponentOptions[] => {
    return nodes.map((node) => ({
      label: node.name || node.label || node.title,
      value: node.id || node.value || node.code,
      children: node.children ? mapTree(node.children) : undefined,
      ...node
    }))
  }

  return mapTree(data)
}

/**
 * 带标签和值的映射函数
 * @param labelField 标签字段名
 * @param valueField 值字段名
 * @returns 映射函数
 */
export const customOptionMapper = (labelField: string = 'name', valueField: string = 'id') => {
  return (data: any[]): ComponentOptions[] => {
    if (!Array.isArray(data)) return []

    return data.map((item) => ({
      label: item[labelField],
      value: item[valueField],
      ...item
    }))
  }
}

/**
 * 带格式化的选项映射函数
 * @param labelFormatter 标签格式化函数
 * @param valueField 值字段名
 * @returns 映射函数
 */
export const formattedOptionMapper = (
  labelFormatter: (item: any) => string,
  valueField: string = 'id'
) => {
  return (data: any[]): ComponentOptions[] => {
    if (!Array.isArray(data)) return []

    return data.map((item) => ({
      label: labelFormatter(item),
      value: item[valueField],
      ...item
    }))
  }
}

/**
 * 创建省市区联动配置（常用场景）
 * @param provinceApi 获取省份列表的API
 * @param cityApi 获取城市列表的API
 * @param districtApi 获取区县列表的API
 * @returns 省市区联动配置数组
 */
export const createRegionLinkage = (
  provinceApi: () => Promise<any>,
  cityApi: (provinceId: string) => Promise<any>,
  districtApi: (cityId: string) => Promise<any>
): FormLinkageConfig[] => {
  return [
    {
      triggerField: 'provinceId',
      targetField: 'cityId',
      api: (provinceId) => cityApi(provinceId),
      optionMapper: defaultOptionMapper,
      clearValue: true,
      clearOptions: true
    },
    {
      triggerField: 'cityId',
      targetField: 'districtId',
      api: (cityId) => districtApi(cityId),
      optionMapper: defaultOptionMapper,
      clearValue: true,
      clearOptions: true
    }
  ]
}

/**
 * 创建部门用户联动配置（常用场景）
 * @param departmentApi 获取部门列表的API
 * @param userApi 获取用户列表的API
 * @returns 部门用户联动配置
 */
export const createDeptUserLinkage = (
  departmentApi: () => Promise<any>,
  userApi: (deptId: string) => Promise<any>
): FormLinkageConfig => {
  return {
    triggerField: 'deptId',
    targetField: 'userId',
    api: (deptId) => userApi(deptId),
    optionMapper: defaultOptionMapper,
    clearValue: true,
    clearOptions: true
  }
}

/**
 * 创建分类商品联动配置（常用场景）
 * @param categoryApi 获取分类列表的API
 * @param productApi 获取商品列表的API
 * @returns 分类商品联动配置
 */
export const createCategoryProductLinkage = (
  categoryApi: () => Promise<any>,
  productApi: (categoryId: string) => Promise<any>
): FormLinkageConfig => {
  return {
    triggerField: 'categoryId',
    targetField: 'productId',
    api: (categoryId) => productApi(categoryId),
    optionMapper: defaultOptionMapper,
    clearValue: true,
    clearOptions: true
  }
}
