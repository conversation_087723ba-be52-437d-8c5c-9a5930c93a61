import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { DICT_TYPE } from '@/utils/dict'
import {
  getExternalLawFirmListApi,
  deleteExternalLawFirmApi
} from '@/api/law/legalTeam/externalLawFirm'
/**
 * 外聘律师管理 - 统一Schema配置
 * 通过 isSearch、isTable、isForm、isDetail 控制字段在不同场景下的显示
 */

export const lawyerSchemas: CrudSchema[] = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    isTable: true
  },

  // ==================== 基础信息组 ====================
  {
    field: 'lawyerName',
    label: '姓名',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名'
      }
    },
    table: {
      // width: 100
    },
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'gender',
    label: '性别',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.SYSTEM_USER_SEX,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'ethnicity',
    label: '民族',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.NATION,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true,
        filterable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'birthDate',
    label: '出生年月',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'DatePicker',
      componentProps: {
        style: { width: '300px' },
        type: 'month',
        format: 'YYYY-MM',
        valueFormat: 'x',
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'politicalAffiliation',
    label: '政治面貌',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.POLITICAL_STATUS,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'contactPhone',
    label: '联系方式',
    isTable: false,
    isForm: true,
    isDetail: true,
    table: {
      // width: 120
    },
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'practiceYear',
    label: '执业年份',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      // width: 100
      formatter: (row: any) => {
        return row.practiceYear + '年'
      }
    },
    form: {
      component: 'DatePicker',
      componentProps: {
        style: { width: '300px' },
        type: 'year',
        format: 'YYYY',
        valueFormat: 'YYYY',
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'practiceCertCode',
    label: '执业证号',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'firmName',
    label: '所在律所',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {},

    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'firmPosition',
    label: '所内职务',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.POSITION,
    table: {
      // width: 100
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'isArbitrator',
    label: '是否仲裁员',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.YES_NO_LIST,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'professionalFields',
    label: '专业领域',
    isTable: true,
    isForm: true,
    isDetail: false, // 详情页面特殊处理
    dictType: DICT_TYPE.PROFESSIONAL_FIELDS,
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        multiple: true,
        clearable: true,
        collapseTags: true,
        maxCollapseTags: 3
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'belongUnitId',
    label: '所属单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请输入所属单位'
      }
    },
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true
      },
      colProps: { span: 12 }
    },
    table: {
      formatter: (row: any) => {
        return row.belongUnitName
      }
    },
    detail: {
      formatter: (key, row: any) => {
        return row.belongUnitName
      }
    }
  },
  {
    field: 'remark',
    label: '备注',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        clearable: true,
        style: { width: '300px' }
      },
      colProps: { span: 12 }
    },
    detail: {
      span: 12
    }
  },

  // ==================== 评价信息组 ====================
  {
    field: 'attitudeRating',
    label: '态度评分',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面特殊处理
    form: {
      component: 'Rate',
      componentProps: {
        max: 5,
        allowHalf: false,
        showText: true,
        texts: ['极差', '较差', '一般', '良好', '优秀']
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'abilityRating',
    label: '能力评分',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面特殊处理
    form: {
      component: 'Rate',
      componentProps: {
        max: 5,
        allowHalf: false,
        showText: true,
        texts: ['极差', '较差', '一般', '良好', '优秀']
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'advantages',
    label: '律师优点',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'AdvancedCheckbox',
      componentProps: {
        type: 'advantage'
      },
      colProps: { span: 24 }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'defect',
    label: '律师缺点',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'AdvancedCheckbox',
      componentProps: {
        type: 'disadvantage'
      },
      colProps: { span: 24 }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'evaluationRemark',
    label: '评论备注',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 3,
        maxlength: 500,
        clearable: true
      },
      colProps: { span: 24 }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'attachment',
    label: '相关附件',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面特殊处理
    form: {
      component: 'UploadFiles',
      componentProps: {},
      colProps: { span: 24 }
    }
  },

  // ==================== 时间信息组 ====================
  {
    field: 'createTime',
    label: '创建时间',
    isTable: false,
    isDetail: true,
    isForm: false,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'updateTime',
    label: '更新时间',
    isTable: false,
    isDetail: true,
    isForm: false,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },

  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isTable: true,
    isForm: false,
    table: {
      width: 280,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于详情页面分组显示）
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return lawyerSchemas.filter((schema) =>
    [
      'lawyerName',
      'gender',
      'ethnicity',
      'birthDate',
      'politicalAffiliation',
      'contactPhone',
      'practiceYear',
      'practiceCertCode',
      'firmName',
      'firmPosition',
      'isArbitrator',
      'belongUnitId',
      'remark'
    ].includes(schema.field)
  )
}

/**
 * 获取评价信息字段（用于详情页面分组显示）
 */
export const getEvaluationInfoSchemas = (): CrudSchema[] => {
  return lawyerSchemas.filter((schema) =>
    ['advantages', 'defect', 'ratingRemark'].includes(schema.field)
  )
}

/**
 * 获取基础表单验证规则
 */
export const getBasicFormRules = () => {
  return {
    lawyerName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    ethnicity: [{ required: true, message: '请选择性别', trigger: 'change' }],
    nation: [{ required: true, message: '请选择民族', trigger: 'change' }],
    birthDate: [{ required: true, message: '请选择出生年月', trigger: 'change' }],
    politicalAffiliation: [{ required: true, message: '请选择政治面貌', trigger: 'change' }],
    contactPhone: [
      { required: true, message: '请输入联系方式', trigger: 'blur' },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码格式',
        trigger: 'blur'
      }
    ],
    practiceYear: [{ required: true, message: '请选择开始执业年份', trigger: 'change' }],
    practiceCertCode: [{ required: true, message: '请输入执业证号', trigger: 'blur' }],
    firmName: [{ required: true, message: '请输入所在律所', trigger: 'blur' }],
    firmPosition: [{ required: true, message: '请选择所内职务', trigger: 'change' }],
    isArbitrator: [{ required: true, message: '请选择是否仲裁员', trigger: 'change' }],
    professionalFields: [{ required: true, message: '请选择专业领域', trigger: 'change' }],
    belongUnitId: [{ required: true, message: '请选择所属单位', trigger: 'blur' }]
  }
}

/**
 * 获取评价表单验证规则
 */
export const getEvaluationFormRules = () => {
  return {
    attitudeRating: [{ required: true, message: '请进行态度评分', trigger: 'change' }],
    abilityRating: [{ required: true, message: '请进行能力评分', trigger: 'change' }]
  }
}
