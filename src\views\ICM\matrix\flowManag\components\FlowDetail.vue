<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0 my-18px">
        <div class="flex items-center flex-1 pos-relative">
          <Back :title="title" hasEmit @back="handleClose" @cancel="handleClose" />
        </div>
      </div>
      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div class="flow-detail">
          <!-- 基础信息 -->
          <Descriptions
            title="基础信息"
            :data="flowData"
            :columns="2"
            :schema="basicInfoSchemas"
            :collapse="false"
          />

          <!-- 流程节点 -->
          <Title title="流程节点">
            <div class="flow-nodes">
              <Table
                :columns="nodeColumns"
                :data="nodes || []"
                :show-header="true"
                border
                empty-text="暂无流程节点"
              />
            </div>
          </Title>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Back, Descriptions, Title, Table } from '@/components'
import { useMessage } from '@/hooks/web/useMessage'
import {
  getFlowManagementDetailApi,
  type FlowManagementVO,
  getFlowManagementNodeListApi
} from '@/api/ICM/matrix/flowManag'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getBasicInfoSchemas } from '../schemas/flowSchemas'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle } = useLayout()
const message = useMessage()
const emit = defineEmits(['close'])

// 响应式数据
const visible = ref(false)
const editId = ref<number | null>(null)
const flowData = ref<FlowManagementVO>({
  companyId: '',
  processTypeId: '',
  processName: '',
  scope: '',
  description: '',
  processNodes: []
})
const nodes = ref([])
// 标题
const title = computed(() => '查看流程详情')

// 使用 CRUD Schemas 生成详情配置
const basicSchemas = getBasicInfoSchemas()
const { allSchemas } = useCrudSchemas(basicSchemas)
const basicInfoSchemas = allSchemas.detailSchema

// 流程节点表格列配置
const nodeColumns = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    width: 80
  },
  {
    field: 'nodeName',
    label: '节点名称',
    width: 200
  },
  {
    field: 'nodeDescription',
    label: '节点描述',
    minWidth: 300
  }
]

// 打开详情页面
const open = async (id: number) => {
  editId.value = id
  visible.value = true

  try {
    const res = await getFlowManagementDetailApi(id)
    const nodesdata = await getFlowManagementNodeListApi(id)
    flowData.value = res
    nodes.value = nodesdata
  } catch (error) {
    console.error('获取流程详情失败:', error)
    message.error('获取流程详情失败')
  }
}

// 关闭详情页面
const handleClose = () => {
  visible.value = false
  editId.value = null
  flowData.value = {
    belongCompany: '',
    flowType: '',
    flowName: '',
    applicableScope: '',
    description: '',
    nodes: []
  }
  emit('close')
}

// 对外暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.flow-detail {
  .flow-nodes {
    margin-top: 16px;
  }
}

.my-18px {
  margin: 18px 0;
}

.pos-relative {
  position: relative;
}

.pos-absolute {
  position: absolute;
}

.right-0 {
  right: 0;
}

.top-0 {
  top: 0;
}

.h-full {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

:deep(.el-table .el-table__empty-block) {
  min-height: 200px;
}

:deep(.el-table .el-table__empty-text) {
  color: #909399;
}
</style>
