<template>
  <div class="group-member-table">
    <div class="mb-4 flex justify-between items-center">
      <span class="text-lg font-medium">{{ title }}</span>
    </div>

    <el-table :data="memberList" border style="width: 100%">
      <el-table-column label="序号" type="index" width="80" align="center" />

      <el-table-column label="职务" width="150" align="center">
        <template #default="{ row, $index }">
          <el-input
            v-if="row.isEditing"
            v-model="row.position"
            placeholder="请输入职务"
            size="small"
            @blur="saveEdit($index)"
            @keyup.enter="saveEdit($index)"
          />
          <span v-else>{{ row.position || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="职责" align="center">
        <template #default="{ row, $index }">
          <el-input
            v-if="row.isEditing"
            v-model="row.responsibility"
            placeholder="请输入职责"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            size="small"
            @blur="saveEdit($index)"
            @keyup.enter="saveEdit($index)"
          />
          <div v-else class="responsibility-text">
            {{ row.responsibility || '-' }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="人员" width="200" align="center">
        <template #default="{ row }">
          <div v-if="row.users && row.users.length > 0" class="user-tags">
            <el-tag
              v-for="user in row.users"
              :key="user.id"
              closable
              size="small"
              @close="removeUserFromMember(row, user)"
              class="mr-1 mb-1"
            >
              {{ user.nickname }}
            </el-tag>
          </div>
          <el-button v-else type="primary" link @click="openUserSelect(row)">
            点击添加人员
          </el-button>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120" align="center">
        <template #default="{ row, $index }">
          <el-button
            v-if="!row.isEditing"
            link
            type="primary"
            size="small"
            @click="editMember($index)"
          >
            编辑
          </el-button>
          <el-button v-else link type="success" size="small" @click="saveEdit($index)">
            保存
          </el-button>
          <el-button
            v-if="row.users && row.users.length > 0"
            link
            type="primary"
            size="small"
            @click="openUserSelect(row)"
          >
            添加人员
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 用户选择弹窗 -->
    <UserSelectDialog ref="userSelectRef" :multiple="true" @confirm="handleUserConfirm" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import UserSelectDialog from './UserSelectDialog.vue'

defineOptions({ name: 'GroupMemberTable' })

interface UserInfo {
  id: number
  nickname: string
  mobile?: string
  deptName?: string
}

interface MemberInfo {
  id?: number
  position: string
  responsibility: string
  users: UserInfo[]
  isEditing?: boolean
}

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  modelValue: {
    type: Array as PropType<MemberInfo[]>,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits<{
  'update:modelValue': [value: MemberInfo[]]
}>()

const memberList = ref<MemberInfo[]>([])
const userSelectRef = ref()
const currentMemberRef = ref<MemberInfo | null>(null)

// 初始化数据
const initData = () => {
  memberList.value = [...props.modelValue]

  // 如果没有数据，添加默认行
  if (memberList.value.length === 0) {
    memberList.value = [
      { position: '组长', responsibility: '', users: [], isEditing: false },
      { position: '副组长', responsibility: '', users: [], isEditing: false },
      { position: '成员', responsibility: '', users: [], isEditing: false }
    ]
  }
}

// 编辑成员
const editMember = (index: number) => {
  memberList.value[index].isEditing = true
}

// 保存编辑
const saveEdit = (index: number) => {
  memberList.value[index].isEditing = false
  updateValue()
}

// 打开用户选择弹窗
const openUserSelect = (member: MemberInfo) => {
  if (props.disabled) return

  currentMemberRef.value = member
  const excludeIds = getAllSelectedUserIds().filter(
    (id) => !member.users.some((user) => user.id === id)
  )
  userSelectRef.value?.open(member.users || [])
}

// 获取所有已选择的用户ID
const getAllSelectedUserIds = (): number[] => {
  const ids: number[] = []
  memberList.value.forEach((member) => {
    if (member.users) {
      member.users.forEach((user) => {
        if (!ids.includes(user.id)) {
          ids.push(user.id)
        }
      })
    }
  })
  return ids
}

// 处理用户选择确认
const handleUserConfirm = (users: UserInfo[]) => {
  if (currentMemberRef.value) {
    currentMemberRef.value.users = users
    updateValue()
    ElMessage.success('添加成员成功')
  }
}

// 从成员中移除用户
const removeUserFromMember = (member: MemberInfo, user: UserInfo) => {
  if (props.disabled) return

  const index = member.users.findIndex((u) => u.id === user.id)
  if (index > -1) {
    member.users.splice(index, 1)
    updateValue()
    ElMessage.success('移除成员成功')
  }
}

// 更新v-model值
const updateValue = () => {
  emit('update:modelValue', memberList.value)
}

// 监听props变化
watch(
  () => props.modelValue,
  () => {
    initData()
  },
  { immediate: true }
)

// 组件挂载时初始化
onMounted(() => {
  initData()
})
</script>

<style scoped lang="scss">
.group-member-table {
  .user-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    justify-content: center;
  }

  .responsibility-text {
    padding: 8px;
    text-align: left;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .el-table {
    .el-table__cell {
      padding: 12px 0;
    }
  }
}
</style>
