/*
 * @Description: 普法知识库管理相关接口
 */
import request from '@/config/axios'

// 普法知识库数据类型
export interface PopularLawKnowledgeItem {
  id?: number
  title: string // 知识标题
  keywords: string // 关键字
  summary: string // 知识简介
  content: string // 知识内容
  contributor: string // 知识贡献者
  organization: string // 所属单位
  audioVideos?: string // 相关音视频
  documents?: string // 相关文档
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 普法知识库操作日志类型
export interface PopularLawKnowledgeActionLogItem {
  id: number
  time: string // 操作时间
  actionName: string // 操作人
  action: string // 操作类型
  remark?: string // 备注信息
}

// 普法知识库查询参数类型
export interface PopularLawKnowledgeQueryParams {
  title?: string
  keywords?: string
  contributor?: string
  organization?: string
  pageNum?: number
  pageSize?: number
}

// 普法知识库列表响应类型
export interface PopularLawKnowledgeListResponse {
  list: PopularLawKnowledgeItem[]
  total: number
}

// 获取普法知识库列表
export const getPopularLawKnowledgeListApi = (params: PopularLawKnowledgeQueryParams) => {
  return request.get<PopularLawKnowledgeListResponse>({
    url: '/law/knowledge-base/page',
    params
  })
}

// 获取普法知识库详情
export const getPopularLawKnowledgeDetailApi = (id: number) => {
  return request.get<PopularLawKnowledgeItem>({
    url: '/law/knowledge-base/get-detail',
    params: { id }
  })
}

// 删除普法知识库
export const deletePopularLawKnowledgeApi = (id: number) => {
  return request.delete({
    url: '/law/knowledge-base/delete',
    params: { id }
  })
}

// 新增普法知识库
export const addPopularLawKnowledgeApi = (data: Partial<PopularLawKnowledgeItem>) => {
  return request.post({
    url: '/law/knowledge-base/create',
    data
  })
}

// 编辑普法知识库
export const updatePopularLawKnowledgeApi = (
  id: number,
  data: Partial<PopularLawKnowledgeItem>
) => {
  return request.put({
    url: '/law/knowledge-base/update',
    data: { id, ...data }
  })
}

// 获取普法知识库操作日志
export const getPopularLawKnowledgeActionLog = (id: number) => {
  return request.get<PopularLawKnowledgeActionLogItem[]>({
    url: '/law/knowledge-base/action-log',
    params: { id }
  })
}
