import { MockMethod } from 'vite-plugin-mock'
import { SUCCESS_CODE } from '../public.constant'
// 普法知识库数据类型
interface PopularLawKnowledgeItem {
  id?: number
  title: string
  keywords: string
  summary: string
  content: string
  contributor: string
  organization: string
  audioVideo?: string
  documents?: string
  createTime?: string
  updateTime?: string
}

// 模拟数据
const mockData: PopularLawKnowledgeItem[] = [
  {
    id: 1,
    title: '企业合规管理基础知识',
    keywords: '合规管理,企业管理,法律风险',
    summary: '介绍企业合规管理的基本概念、重要性以及实施要点，帮助企业建立有效的合规体系。',
    content:
      '企业合规管理是指企业以有效防范合规风险为目标，以企业经营管理行为和员工履职行为为对象，开展包括制度制定、风险识别、合规审查、风险应对、责任追究、考核评价、合规培训等有组织、有计划的管理活动。合规管理体系的建立需要从顶层设计、组织架构、制度体系、运行机制等多个方面进行系统规划和实施。',
    contributor: '张法务',
    organization: '法务部',
    audioVideo: '企业合规管理培训视频.mp4',
    documents: '企业合规管理指导手册.pdf',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    title: '劳动合同法实务操作要点',
    keywords: '劳动合同,劳动法,人力资源',
    summary: '详细解读劳动合同法的核心条款，提供实务操作中的注意事项和风险防范措施。',
    content:
      '劳动合同法是规范用人单位与劳动者建立劳动关系的重要法律。在实务操作中，用人单位应当注意合同的签订时间、试用期约定、工资标准、工作时间、休息休假、社会保险等关键条款的设置。同时要重视合同的变更、解除和终止程序，避免因操作不当导致的法律风险。',
    contributor: '李HR',
    organization: '人力资源部',
    audioVideo: '劳动合同法解读讲座.mp4',
    documents: '劳动合同模板.doc,劳动法条文解读.pdf',
    createTime: '2024-01-02 10:00:00',
    updateTime: '2024-01-02 10:00:00'
  },
  {
    id: 3,
    title: '知识产权保护实务指南',
    keywords: '知识产权,专利保护,商标注册',
    summary: '全面介绍知识产权保护的法律框架，包括专利、商标、著作权等各类知识产权的申请和维护。',
    content:
      '知识产权保护是企业核心竞争力的重要组成部分。企业应当建立完善的知识产权管理体系，包括知识产权的创造、运用、保护和管理。在专利方面，要注重技术创新的及时申请和布局；在商标方面，要做好品牌保护和监控；在著作权方面，要规范作品的创作和使用。同时要加强对侵权行为的监控和维权。',
    contributor: '王知产',
    organization: '法务部',
    audioVideo: '',
    documents: '知识产权保护手册.pdf,专利申请流程图.png',
    createTime: '2024-01-03 10:00:00',
    updateTime: '2024-01-03 10:00:00'
  },
  {
    id: 4,
    title: '合同管理制度与风险防控',
    keywords: '合同管理,风险防控,法律审查',
    summary: '建立健全的合同管理制度，从合同的起草、审查、签订到履行全过程进行风险管控。',
    content:
      '合同管理是企业经营管理的重要环节。有效的合同管理制度应当包括合同分类管理、标准化模板、审批流程、履行监督、争议处理等各个环节。在合同风险防控方面，要重点关注主体资格审查、条款设置、履行监督、变更管理等关键节点，建立事前预防、事中控制、事后救济的全流程风险管控体系。',
    contributor: '赵律师',
    organization: '总经理办公室',
    audioVideo: '合同管理培训录音.mp3',
    documents: '合同管理制度.pdf,合同审查要点.doc',
    createTime: '2024-01-04 10:00:00',
    updateTime: '2024-01-04 10:00:00'
  },
  {
    id: 5,
    title: '数据保护与隐私合规',
    keywords: '数据保护,隐私合规,个人信息',
    summary: '解读数据保护法律法规要求，指导企业建立数据保护和隐私合规管理体系。',
    content:
      '随着数字化时代的到来，数据保护和隐私合规成为企业必须重视的合规领域。企业应当遵循《个人信息保护法》《数据安全法》等法律法规要求，建立数据分类分级保护制度，明确数据处理的合法性基础，履行个人信息保护义务，建立数据安全管理制度和技术保护措施。',
    contributor: '孙合规',
    organization: '合规部',
    audioVideo: '数据保护法解读视频.mp4',
    documents: '数据保护合规手册.pdf',
    createTime: '2024-01-05 10:00:00',
    updateTime: '2024-01-05 10:00:00'
  }
]

export default [
  // 获取普法知识库列表
  {
    url: '/admin-api/api/popular-law-knowledge/list',
    method: 'get',
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, title, keywords, contributor, organization } = query

      let filteredData = [...mockData]

      // 筛选条件
      if (title) {
        filteredData = filteredData.filter((item) => item.title.includes(title))
      }
      if (keywords) {
        filteredData = filteredData.filter((item) => item.keywords.includes(keywords))
      }
      if (contributor) {
        filteredData = filteredData.filter((item) => item.contributor.includes(contributor))
      }
      if (organization) {
        filteredData = filteredData.filter((item) => item.organization.includes(organization))
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)

      return {
        code: SUCCESS_CODE,
        message: 'success',
        data: {
          list,
          total: filteredData.length
        }
      }
    }
  },

  // 获取普法知识库详情
  {
    url: '/admin-api/api/popular-law-knowledge/detail',
    method: 'get',
    response: ({ query }) => {
      const { id } = query
      const item = mockData.find((item) => item.id === Number(id))

      if (item) {
        return {
          code: SUCCESS_CODE,
          message: 'success',
          data: item
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 新增普法知识库
  {
    url: '/admin-api/api/popular-law-knowledge/add',
    method: 'post',
    response: ({ body }) => {
      const newItem = {
        id: Date.now(),
        ...body,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }
      mockData.push(newItem)

      return {
        code: SUCCESS_CODE,
        message: '新增成功',
        data: newItem
      }
    }
  },

  // 编辑普法知识库
  {
    url: '/admin-api/api/popular-law-knowledge/update',
    method: 'put',
    response: ({ body }) => {
      const { id } = body
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData[index] = {
          ...mockData[index],
          ...body,
          updateTime: new Date().toLocaleString()
        }

        return {
          code: SUCCESS_CODE,
          message: '更新成功',
          data: mockData[index]
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 删除普法知识库
  {
    url: '/admin-api/api/popular-law-knowledge/delete',
    method: 'delete',
    response: ({ query }) => {
      const { id } = query
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData.splice(index, 1)
        return {
          code: SUCCESS_CODE,
          message: '删除成功'
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 获取普法知识库操作日志
  {
    url: '/admin-api/api/popular-law-knowledge/action-log',
    method: 'get',
    response: ({ query }) => {
      const { id } = query

      // 模拟操作日志数据
      const actionLogs = [
        {
          id: 1,
          time: '2024-01-15 14:30:00',
          actionName: '管理员',
          action: '创建记录',
          remark: '初始创建普法知识库条目'
        },
        {
          id: 2,
          time: '2024-01-20 09:15:00',
          actionName: '张法务',
          action: '编辑内容',
          remark: '更新知识内容和相关资源'
        },
        {
          id: 3,
          time: '2024-02-01 16:45:00',
          actionName: '李HR',
          action: '内容审核',
          remark: '审核通过知识内容更新'
        },
        {
          id: 4,
          time: '2024-02-15 11:20:00',
          actionName: '王知产',
          action: '资源上传',
          remark: '上传了相关培训视频和文档'
        },
        {
          id: 5,
          time: '2024-03-01 13:10:00',
          actionName: '赵律师',
          action: '关键词更新',
          remark: '优化了知识库的关键词标签'
        }
      ]

      return {
        code: SUCCESS_CODE,
        message: 'success',
        data: actionLogs
      }
    }
  }
] as MockMethod[]
