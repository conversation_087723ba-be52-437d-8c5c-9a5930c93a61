import request from '@/config/axios'

// 内控流程管理接口

/**
 * 流程节点数据结构
 */
export interface FlowNodeVO {
  id?: number
  nodeName: string // 节点名称
  nodeDescription: string // 节点描述
  nodeOrder: number // 排序
}

/**
 * 内控流程数据结构
 */
export interface FlowManagementVO {
  id?: number
  companyId: string // 所属企业
  processTypeId: string // 流程类型
  processName: string // 流程名称
  scope: string // 适用范围
  description: string // 流程描述
  processNodes: FlowNodeVO[] // 流程节点
  nodeCount?: number // 节点数量（只读）
  relatedDescription?: string // 相关描述
  createTime?: string
  updateTime?: string
}

/**
 * 查询参数
 */
export interface FlowManagementPageReqVO {
  pageNo: number
  pageSize: number
  companyId?: string // 所属企业
  processName?: string // 流程名称
  processTypeId?: string // 流程类型
}

/**
 * 获取内控流程分页列表
 */
export const getFlowManagementListApi = (params: FlowManagementPageReqVO) => {
  return request.get({
    url: 'ic/process/page',
    params
  })
}

/**
 * 获取内控流程详情
 */
export const getFlowManagementDetailApi = (id: number) => {
  return request.get({
    url: `/ic/process/get`,
    params: { id }
  })
}
//获取内控流程节点列表
export const getFlowManagementNodeListApi = (id: number) => {
  return request.get({
    url: `ic/process/process-node/list-by-process-id`,
    params: { processId: id }
  })
}

/**
 * 创建内控流程
 */
export const createFlowManagementApi = (data: FlowManagementVO) => {
  return request.post({
    url: 'ic/process/create',
    data
  })
}

/**
 * 更新内控流程
 */
export const updateFlowManagementApi = (data: FlowManagementVO) => {
  return request.put({
    url: 'ic/process/update',
    data
  })
}

/**
 * 删除内控流程
 */
export const deleteFlowManagementApi = (id: number) => {
  return request.delete({
    url: `/ic/process/delete`,
    params: { id }
  })
}

/**
 * 导出内控流程
 */
export const exportFlowManagementApi = (params: FlowManagementPageReqVO) => {
  return request.download({
    url: 'ic/process/export',
    params
  })
}
