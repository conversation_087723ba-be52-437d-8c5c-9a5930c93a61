export const useSelectFile = () => {
  //   选择文件
  const selectFile = (
    param?: {
      accept: string
      maxSize: number
      multiple: boolean
    } = {}
  ): Promise<File> => {
    const {
      maxSize = 5,
      accept = 'image/jpeg,image/jpg,image/png,image/svg+xml',
      multiple = false
    } = param

    return new Promise((resolve, reject) => {
      const uploader = document.createElement('input')
      uploader.multiple = multiple
      uploader.name = 'file'
      uploader.type = 'file'
      uploader.accept = accept || 'image/jpeg,image/jpg,image/png,image/svg+xml' // 同时支持jpg和png
      uploader.click() // 同时支持jpg和png
      uploader.style.display = 'none' // 同时支持jpg和png

      uploader.onchange = async () => {
        const files = uploader.files
        const file = files && files?.length >= 1 ? files[0] : undefined

        if (!file) {
          reject('未选择文件')
          return
        }
        if (!accept.includes(file.name.split('.').pop()?.toLowerCase() || '')) {
          reject('上传文件只支持' + accept + '格式')
          return
        }

        // max 512Mb
        if (file.size >= maxSize * 1024 * 1024) {
          reject(`上传的文件不能超过${maxSize}Mb`)
          return
        }
        // const { width, height } = await getImageSize(file)
        // console.log(width, height)
        resolve({
          file
          // width,
          // height
        })
      }

      uploader.remove()
    })
  }

  const getImageSize = (file) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    return new Promise((resolve, reject) => {
      reader.onload = function (e) {
        const img = new Image()
        img.onload = function () {
          const width = img.width
          const height = img.height
          resolve({ width, height })
        }
        img.onerror = reject
        img.src = e.target.result
      }
    })
  }

  return {
    selectFile
  }
}
