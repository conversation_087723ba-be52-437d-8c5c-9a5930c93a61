import { MockMethod } from 'vite-plugin-mock'

// 外聘律所数据类型
interface ExternalLawFirmItem {
  id?: number
  name: string
  creditCode: string
  address: string
  status: string
  specialties: string[]
  legalRepresentative: string
  contactPerson: string
  contactPhone: string
  organization: string
  remark?: string
  attachments?: string[]
  createTime?: string
  updateTime?: string
}

// 模拟数据
const mockData: ExternalLawFirmItem[] = [
  {
    id: 1,
    name: '北京德恒律师事务所',
    creditCode: '91110000100000001A',
    address: '北京市朝阳区建国门外大街甲14号',
    status: '存续',
    specialties: ['民商法', '知识产权法', '涉外法律'],
    legalRepresentative: '张德恒',
    contactPerson: '李律师',
    contactPhone: '010-12345678',
    organization: '总部',
    remark: '知名律师事务所，专业服务优质',
    attachments: ['营业执照.pdf', '资质证书.pdf'],
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    name: '上海金茂律师事务所',
    creditCode: '91310000200000002B',
    address: '上海市黄浦区南京东路100号',
    status: '存续',
    specialties: ['经济法', '劳动法', '仲裁调解'],
    legalRepresentative: '王金茂',
    contactPerson: '赵律师',
    contactPhone: '021-87654321',
    organization: '上海分部',
    remark: '经济法律服务专业',
    attachments: ['律所简介.pdf'],
    createTime: '2024-01-02 10:00:00',
    updateTime: '2024-01-02 10:00:00'
  },
  {
    id: 3,
    name: '广州华南律师事务所',
    creditCode: '91440000300000003C',
    address: '广州市天河区珠江新城华夏路1号',
    status: '存续',
    specialties: ['刑法', '行政法', '环境法'],
    legalRepresentative: '陈华南',
    contactPerson: '孙律师',
    contactPhone: '020-11223344',
    organization: '广州分部',
    remark: '刑事辩护经验丰富',
    attachments: ['成功案例.pdf', '团队介绍.doc'],
    createTime: '2024-01-03 10:00:00',
    updateTime: '2024-01-03 10:00:00'
  },
  {
    id: 4,
    name: '深圳前海律师事务所',
    creditCode: '91440000400000004D',
    address: '深圳市南山区前海深港合作区',
    status: '注销',
    specialties: ['涉外法律', '知识产权法'],
    legalRepresentative: '林前海',
    contactPerson: '周律师',
    contactPhone: '0755-55667788',
    organization: '深圳分部',
    remark: '已注销',
    attachments: [],
    createTime: '2024-01-04 10:00:00',
    updateTime: '2024-01-04 10:00:00'
  },
  {
    id: 5,
    name: '成都西部律师事务所',
    creditCode: '91510000500000005E',
    address: '成都市高新区天府大道中段',
    status: '存续',
    specialties: ['民商法', '劳动法', '其他'],
    legalRepresentative: '刘西部',
    contactPerson: '吴律师',
    contactPhone: '028-99887766',
    organization: '成都分部',
    remark: '西部地区优秀律所',
    attachments: ['荣誉证书.pdf'],
    createTime: '2024-01-05 10:00:00',
    updateTime: '2024-01-05 10:00:00'
  }
]

export default [
  // 获取外聘律所列表
  {
    url: '/admin-api/api/external-law-firm/list',
    method: 'get',
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, name, status, specialties, organization } = query

      let filteredData = [...mockData]

      // 筛选条件
      if (name) {
        filteredData = filteredData.filter((item) => item.name.includes(name))
      }
      if (status) {
        filteredData = filteredData.filter((item) => item.status === status)
      }
      if (specialties) {
        filteredData = filteredData.filter((item) =>
          item.specialties.some((specialty) => specialty.includes(specialties))
        )
      }
      if (organization) {
        filteredData = filteredData.filter((item) => item.organization.includes(organization))
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)

      return {
        code: 200,
        message: 'success',
        data: {
          list,
          total: filteredData.length
        }
      }
    }
  },

  // 获取外聘律所详情
  {
    url: '/admin-api/api/external-law-firm/detail',
    method: 'get',
    response: ({ query }) => {
      const { id } = query
      const item = mockData.find((item) => item.id === Number(id))

      if (item) {
        return {
          code: 200,
          message: 'success',
          data: item
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 新增外聘律所
  {
    url: '/admin-api/api/external-law-firm/add',
    method: 'post',
    response: ({ body }) => {
      const newItem = {
        id: Date.now(),
        ...body,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }
      mockData.push(newItem)

      return {
        code: 200,
        message: '新增成功',
        data: newItem
      }
    }
  },

  // 编辑外聘律所
  {
    url: '/admin-api/api/external-law-firm/update',
    method: 'put',
    response: ({ body }) => {
      const { id } = body
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData[index] = {
          ...mockData[index],
          ...body,
          updateTime: new Date().toLocaleString()
        }

        return {
          code: 200,
          message: '更新成功',
          data: mockData[index]
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 删除外聘律所
  {
    url: '/admin-api/api/external-law-firm/delete',
    method: 'delete',
    response: ({ query }) => {
      const { id } = query
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData.splice(index, 1)
        return {
          code: 200,
          message: '删除成功'
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 获取外聘律所操作日志
  {
    url: '/admin-api/api/external-law-firm/action-log',
    method: 'get',
    response: ({ query }) => {
      const { id } = query

      // 模拟操作日志数据
      const actionLogs = [
        {
          id: 1,
          time: '2024-01-15 14:30:00',
          actionName: '管理员',
          action: '创建记录',
          remark: '初始创建外聘律所信息'
        },
        {
          id: 2,
          time: '2024-01-20 09:15:00',
          actionName: '张三',
          action: '编辑信息',
          remark: '更新律所联系方式和地址'
        },
        {
          id: 3,
          time: '2024-02-01 16:45:00',
          actionName: '李四',
          action: '状态变更',
          remark: '律所状态从存续变更为注销'
        },
        {
          id: 4,
          time: '2024-02-15 11:20:00',
          actionName: '王五',
          action: '附件上传',
          remark: '上传了新的营业执照'
        },
        {
          id: 5,
          time: '2024-03-01 13:10:00',
          actionName: '赵六',
          action: '信息审核',
          remark: '审核通过律所信息变更申请'
        }
      ]

      return {
        code: 200,
        message: 'success',
        data: actionLogs
      }
    }
  }
] as MockMethod[]
