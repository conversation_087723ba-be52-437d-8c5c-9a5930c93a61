# 组件使用文档

本文档详细介绍了项目中所有二次封装的Vue组件，包括功能说明、使用方法和示例代码。

## 目录

- [基础组件](#基础组件)
- [表单组件](#表单组件)
- [数据展示组件](#数据展示组件)
- [交互组件](#交互组件)
- [业务组件](#业务组件)
- [编辑器组件](#编辑器组件)
- [工具组件](#工具组件)

---

## 基础组件

### 1. Icon - 图标组件
**路径**: `src/components/Icon`
**功能**: 统一的图标显示组件，支持本地SVG图标和Iconify图标库

**Props**:
- `icon` (String): 图标名称
- `color` (String): 图标颜色
- `size` (Number): 图标大小，默认16px
- `svgClass` (String): SVG类名

**使用示例**:
```vue
<template>
  <!-- 使用本地SVG图标 -->
  <Icon icon="svg-icon:user" :size="20" />
  
  <!-- 使用Iconify图标 -->
  <Icon icon="ep:user" color="#409EFF" :size="24" />
</template>
```

### 2. Button - 按钮组件
**路径**: `src/components/Button`
**功能**: 基于ElementPlus按钮的增强版本，支持防抖、提示等功能

**Props**:
- `type` (String): 按钮类型
- `size` (String): 按钮尺寸
- `tooltip` (Boolean): 是否显示提示
- `content` (String): 提示内容
- `loading` (Boolean): 加载状态
- `disabled` (Boolean): 禁用状态

**使用示例**:
```vue
<template>
  <Button type="primary" tooltip content="保存数据">保存</Button>
  <Button type="danger" :loading="loading">删除</Button>
</template>
```

### 3. Backtop - 返回顶部
**路径**: `src/components/Backtop`
**功能**: 页面返回顶部按钮，自动绑定到内容滚动区域

**使用示例**:
```vue
<template>
  <Backtop />
</template>
```

---

## 表单组件

### 4. Form - 动态表单
**路径**: `src/components/Form`
**功能**: 基于配置的动态表单组件，支持各种表单项类型

**Props**:
- `schema` (Array): 表单配置数组
- `model` (Object): 表单数据对象
- `isCol` (Boolean): 是否栅格布局，默认false
- `labelWidth` (String|Number): 标签宽度，默认'auto'
- `autoSetPlaceholder` (Boolean): 自动设置占位符

**Schema配置项**:
- `field`: 字段名
- `label`: 标签文本
- `component`: 组件类型 (Input, Select, DatePicker等)
- `required`: 是否必填
- `componentProps`: 组件属性
- `colProps`: 栅格属性
- `formItemProps`: 表单项属性

**主要方法**:
- `setValues(data)`: 设置表单值
- `setProps(props)`: 设置表单属性
- `addSchema(schema, index)`: 添加表单项
- `delSchema(field)`: 删除表单项
- `setSchema(schemaProps)`: 批量设置表单项

**支持的组件类型**:
- Input: 输入框
- InputNumber: 数字输入框
- Select: 选择器
- Radio: 单选框
- Checkbox: 复选框
- DatePicker: 日期选择器
- TimePicker: 时间选择器
- Switch: 开关
- Slider: 滑块
- Rate: 评分
- Cascader: 级联选择器
- Transfer: 穿梭框
- Upload: 上传
- Editor: 富文本编辑器
- JsonEditor: JSON编辑器

**使用示例**:
```vue
<template>
  <Form 
    ref="formRef" 
    :schema="formSchema" 
    v-model="formData"
    label-width="120px"
  />
</template>

<script setup>
const formData = ref({})
const formRef = ref()

const formSchema = [
  {
    field: 'name',
    label: '姓名',
    component: 'Input',
    required: true,
    colProps: { span: 12 }
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    componentProps: {
      type: 'email'
    },
    colProps: { span: 12 }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  }
]

// 设置表单值
const setFormData = () => {
  formRef.value?.setValues({
    name: '张三',
    email: '<EMAIL>',
    status: 1
  })
}
</script>
```

### 5. Search - 搜索表单
**路径**: `src/components/Search`
**功能**: 专门用于搜索条件的表单组件

**使用示例**:
```vue
<template>
  <Search :schema="searchSchema" @search="handleSearch" @reset="handleReset" />
</template>
```

### 6. InputPassword - 密码输入框
**路径**: `src/components/InputPassword`
**功能**: 带显示/隐藏功能的密码输入框

**使用示例**:
```vue
<template>
  <InputPassword v-model="password" placeholder="请输入密码" />
</template>
```

### 7. ColorInput - 颜色选择器
**路径**: `src/components/ColorInput`
**功能**: 颜色选择输入组件

### 8. InputWithColor - 带颜色的输入框
**路径**: `src/components/InputWithColor`
**功能**: 输入框与颜色选择器的组合组件

### 9. DeptSelectForm - 部门选择器
**路径**: `src/components/DeptSelectForm`
**功能**: 部门树形选择组件

**使用示例**:
```vue
<template>
  <DeptSelectForm v-model="selectedDept" />
</template>
```

### 10. UserSelectForm - 用户选择器
**路径**: `src/components/UserSelectForm`
**功能**: 用户选择组件

### 11. AppLinkInput - 应用链接输入
**路径**: `src/components/AppLinkInput`
**功能**: 应用链接选择输入组件

---

## 数据展示组件

### 12. Table - 数据表格
**路径**: `src/components/Table`
**功能**: 增强版表格组件，支持动态列配置、分页、选择等功能

**Props**:
- `columns` (Array): 列配置数组
- `data` (Array): 表格数据
- `loading` (Boolean): 加载状态
- `selection` (Boolean): 是否显示多选列
- `pagination` (Object): 分页配置
- `showOverflowTooltip` (Boolean): 超出显示省略号
- `expand` (Boolean): 是否支持展开行
- `reserveSelection` (Boolean): 是否保留选中状态
- `align` (String): 对齐方式，默认'center'
- `headerAlign` (String): 表头对齐方式

**Column配置项**:
- `field`: 字段名
- `label`: 列标题
- `width`: 列宽度
- `minWidth`: 最小宽度
- `fixed`: 固定列位置 ('left' | 'right')
- `sortable`: 是否可排序
- `formatter`: 格式化函数
- `type`: 列类型 ('selection' | 'index' | 'expand')
- `slots`: 插槽配置

**主要方法**:
- `setProps(props)`: 设置表格属性
- `setColumn(columns)`: 设置列配置
- `getSelections()`: 获取选中行数据
- `clearSelection()`: 清空选中状态
- `toggleRowSelection(row, selected)`: 切换行选中状态

**使用示例**:
```vue
<template>
  <Table
    ref="tableRef"
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :columns="columns"
    :data="tableData"
    :loading="loading"
    :pagination="pagination"
    selection
    @selection-change="handleSelectionChange"
    @page-change="handlePageChange"
  />
</template>

<script setup>
const tableRef = ref()
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const tableData = ref([])

const columns = [
  {
    field: 'id',
    label: 'ID',
    width: 80,
    type: 'index'
  },
  {
    field: 'name',
    label: '姓名',
    minWidth: 120
  },
  {
    field: 'email',
    label: '邮箱',
    minWidth: 160
  },
  {
    field: 'status',
    label: '状态',
    width: 100,
    formatter: (row) => row.status === 1 ? '启用' : '禁用',
    slots: {
      default: (scope) => (
        <el-tag type={scope.row.status === 1 ? 'success' : 'danger'}>
          {scope.row.status === 1 ? '启用' : '禁用'}
        </el-tag>
      )
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 150,
    fixed: 'right',
    slots: {
      default: (scope) => (
        <div>
          <el-button type="primary" size="small" onClick={() => handleEdit(scope.row)}>
            编辑
          </el-button>
          <el-button type="danger" size="small" onClick={() => handleDelete(scope.row)}>
            删除
          </el-button>
        </div>
      )
    }
  }
]

const pagination = {
  total: 0,
  pageSize: 10,
  currentPage: 1
}

const handleSelectionChange = (selections) => {
  console.log('选中的行:', selections)
}

const handlePageChange = ({ currentPage, pageSize }) => {
  // 处理分页变化
  fetchData()
}
</script>
```

### 13. Pagination - 分页组件
**路径**: `src/components/Pagination`
**功能**: 分页控制组件

**使用示例**:
```vue
<template>
  <Pagination
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :total="total"
    @change="handlePageChange"
  />
</template>
```

### 14. Descriptions - 描述列表
**路径**: `src/components/Descriptions`
**功能**: 描述信息展示组件

### 15. Card - 卡片组件
**路径**: `src/components/Card`
**功能**: 增强版卡片组件

### 16. SummaryCard - 汇总卡片
**路径**: `src/components/SummaryCard`
**功能**: 数据汇总展示卡片

**使用示例**:
```vue
<template>
  <SummaryCard
    title="总用户数"
    :value="1234"
    icon="ep:user"
    color="#409EFF"
  />
</template>
```

### 17. CountTo - 数字动画
**路径**: `src/components/CountTo`
**功能**: 数字滚动动画组件

### 18. DictTag - 字典标签
**路径**: `src/components/DictTag`
**功能**: 字典值标签显示组件

**使用示例**:
```vue
<template>
  <DictTag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="1" />
</template>
```

---

## 交互组件

### 19. Dialog - 对话框
**路径**: `src/components/Dialog`
**功能**: 增强版对话框组件

**使用示例**:
```vue
<template>
  <Dialog v-model="visible" title="编辑用户">
    <Form ref="formRef" :schema="formSchema" />
  </Dialog>
</template>
```

### 20. Draggable - 拖拽组件
**路径**: `src/components/Draggable`
**功能**: 拖拽排序组件

### 21. Tooltip - 提示框
**路径**: `src/components/Tooltip`
**功能**: 增强版提示框组件

### 22. ImageViewer - 图片查看器
**路径**: `src/components/ImageViewer`
**功能**: 图片预览查看组件

### 23. Qrcode - 二维码
**路径**: `src/components/Qrcode`
**功能**: 二维码生成显示组件

**使用示例**:
```vue
<template>
  <Qrcode :text="qrText" :size="200" />
</template>
```

### 24. Sticky - 粘性定位
**路径**: `src/components/Sticky`
**功能**: 粘性定位组件

---

## 业务组件

### 25. ContentWrap - 内容包装器
**路径**: `src/components/ContentWrap`
**功能**: 页面内容包装组件

**使用示例**:
```vue
<template>
  <ContentWrap title="用户管理">
    <Table :columns="columns" :data="tableData" />
  </ContentWrap>
</template>
```

### 26. ContentDetailWrap - 详情内容包装器
**路径**: `src/components/ContentDetailWrap`
**功能**: 详情页面内容包装组件

### 27. OperateLogV2 - 操作日志
**路径**: `src/components/OperateLogV2`
**功能**: 操作日志展示组件

### 28. RouterSearch - 路由搜索
**路径**: `src/components/RouterSearch`
**功能**: 路由搜索功能组件

### 29. DocAlert - 文档提醒
**路径**: `src/components/DocAlert`
**功能**: 文档链接提醒组件

### 30. Error - 错误页面
**路径**: `src/components/Error`
**功能**: 错误状态展示组件

### 31. Infotip - 信息提示
**路径**: `src/components/Infotip`
**功能**: 信息提示组件

### 32. ConfigGlobal - 全局配置
**路径**: `src/components/ConfigGlobal`
**功能**: 全局配置管理组件

---

## 编辑器组件

### 33. Editor - 富文本编辑器
**路径**: `src/components/Editor`
**功能**: 富文本编辑器组件

**使用示例**:
```vue
<template>
  <Editor v-model="content" :height="400" />
</template>
```

### 34. MarkdownView - Markdown查看器
**路径**: `src/components/MarkdownView`
**功能**: Markdown内容渲染组件

### 35. FormCreate - 表单设计器
**路径**: `src/components/FormCreate`
**功能**: 动态表单创建器

### 36. DiyEditor - DIY可视化编辑器
**路径**: `src/components/DiyEditor`
**功能**: 可视化页面编辑器，支持拖拽式页面搭建

**主要功能**:
- 组件库面板：提供各种预定义组件
- 画布区域：可视化页面编辑区域
- 属性面板：组件属性配置
- 预览功能：实时预览编辑效果
- 保存/加载：页面配置的保存和加载

**支持的组件类型**:
- 轮播图组件
- 菜单导航组件
- 优惠券组件
- 商品组件
- 文章组件
- 分割线组件
- 通知栏组件
- 导航栏组件
- 魔方布局组件

**Props**:
- `title` (String): 编辑器标题
- `modelValue` (Object): 页面配置数据
- `previewUrl` (String): 预览链接
- `libs` (Array): 可用组件库

**使用示例**:
```vue
<template>
  <DiyEditor
    v-model="pageConfig"
    title="首页装修"
    :preview-url="previewUrl"
    @save="handleSave"
  >
    <template #toolBarLeft>
      <el-button @click="handleImport">导入模板</el-button>
    </template>
  </DiyEditor>
</template>

<script setup>
const pageConfig = ref({
  components: [],
  style: {}
})

const previewUrl = ref('/preview')

const handleSave = (config) => {
  console.log('保存页面配置:', config)
  // 调用API保存
}

const handleImport = () => {
  // 导入模板逻辑
}
</script>
```

### 37. MagicCubeEditor - 魔方编辑器
**路径**: `src/components/MagicCubeEditor`
**功能**: 魔方布局编辑器

### 38. Cropper - 图片裁剪器
**路径**: `src/components/Cropper`
**功能**: 图片裁剪组件

### 39. bpmnProcessDesigner - BPMN流程设计器
**路径**: `src/components/bpmnProcessDesigner`
**功能**: 基于bpmn.js的流程设计器，支持完整的BPMN 2.0规范

**主要功能**:
- 流程图绘制：支持各种BPMN元素
- 属性配置：节点属性、连线属性配置
- 导入导出：支持BPMN XML格式
- 验证功能：流程图语法验证
- 预览模式：只读模式查看流程图

**支持的BPMN元素**:
- 开始事件、结束事件
- 用户任务、服务任务
- 排他网关、并行网关
- 顺序流、消息流
- 泳道、子流程

**Props**:
- `value` (String): BPMN XML字符串
- `translations` (Object): 国际化配置
- `additionalModel` (Array): 扩展模型
- `moddleExtension` (Object): 模型扩展
- `onlyCustomizeAddi` (Boolean): 仅自定义扩展
- `onlyCustomizeModdle` (Boolean): 仅自定义模型

**事件**:
- `element-click`: 元素点击事件
- `element-contextmenu`: 右键菜单事件
- `element-changed`: 元素变化事件

**使用示例**:
```vue
<template>
  <div class="process-designer">
    <bpmn-process-designer
      v-model="bpmnXml"
      :translations="translations"
      @element-click="handleElementClick"
      @element-changed="handleElementChanged"
    />
  </div>
</template>

<script setup>
const bpmnXml = ref(`<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL">
  <bpmn2:process id="process" isExecutable="true">
    <bpmn2:startEvent id="start" />
  </bpmn2:process>
</bpmn2:definitions>`)

const translations = {
  'Exclusive Gateway': '排他网关',
  'Parallel Gateway': '并行网关',
  'User Task': '用户任务'
}

const handleElementClick = (element) => {
  console.log('点击元素:', element)
}

const handleElementChanged = (element) => {
  console.log('元素变化:', element)
}
</script>
```

### 40. SimpleProcessDesignerV2 - 简单流程设计器
**路径**: `src/components/SimpleProcessDesignerV2`
**功能**: 简化版流程设计器

### 41. Tinyflow - 微流程
**路径**: `src/components/Tinyflow`
**功能**: 轻量级流程组件

---

## 工具组件

### 42. UploadFile - 文件上传
**路径**: `src/components/UploadFile`
**功能**: 文件上传组件集合，支持多种上传场景

**主要组件**:

#### UploadFile - 通用文件上传
**Props**:
- `modelValue` (Array): 文件列表
- `limit` (Number): 最大上传数量
- `fileSize` (Number): 文件大小限制(MB)
- `fileType` (Array): 允许的文件类型
- `disabled` (Boolean): 是否禁用
- `drag` (Boolean): 是否支持拖拽上传
- `autoUpload` (Boolean): 是否自动上传
- `isShowTip` (Boolean): 是否显示提示信息

#### UploadImg - 单图片上传
**Props**:
- `modelValue` (String): 图片URL
- `width` (String): 预览宽度
- `height` (String): 预览高度
- `disabled` (Boolean): 是否禁用

#### UploadImgs - 多图片上传
**Props**:
- `modelValue` (Array): 图片URL数组
- `limit` (Number): 最大上传数量
- `disabled` (Boolean): 是否禁用

**使用示例**:
```vue
<template>
  <!-- 通用文件上传 -->
  <UploadFile 
    v-model="fileList" 
    :limit="3"
    :file-size="10"
    :file-type="['doc', 'docx', 'pdf']"
    @upload-success="handleUploadSuccess"
  />
  
  <!-- 单图片上传 -->
  <UploadImg 
    v-model="imageUrl" 
    width="120px"
    height="120px"
  />
  
  <!-- 多图片上传 -->
  <UploadImgs 
    v-model="imageList" 
    :limit="5"
  />
</template>

<script setup>
const fileList = ref([])
const imageUrl = ref('')
const imageList = ref([])

const handleUploadSuccess = (response, file) => {
  console.log('上传成功:', response, file)
}
</script>
```

**上传配置**:
- 支持前端直传和后端代理上传
- 自动压缩图片
- 支持拖拽上传
- 文件类型和大小验证
- 上传进度显示
- 错误处理和重试机制

### 43. Echart - 图表组件
**路径**: `src/components/Echart`
**功能**: ECharts图表封装组件

**使用示例**:
```vue
<template>
  <Echart :options="chartOptions" :height="400" />
</template>
```

### 44. Highlight - 代码高亮
**路径**: `src/components/Highlight`
**功能**: 代码语法高亮组件

### 45. IFrame - 内嵌框架
**路径**: `src/components/IFrame`
**功能**: IFrame内嵌页面组件

### 46. Crontab - 定时任务
**路径**: `src/components/Crontab`
**功能**: Cron表达式编辑器

### 47. Verifition - 验证组件
**路径**: `src/components/Verifition`
**功能**: 验证码组件

**子组件**:
- `VerifySlide`: 滑动验证
- `VerifyPoints`: 点选验证

### 48. ShortcutDateRangePicker - 快捷日期选择器
**路径**: `src/components/ShortcutDateRangePicker`
**功能**: 带快捷选项的日期范围选择器

**使用示例**:
```vue
<template>
  <ShortcutDateRangePicker v-model="dateRange" />
</template>
```

### 49. VerticalButtonGroup - 垂直按钮组
**路径**: `src/components/VerticalButtonGroup`
**功能**: 垂直排列的按钮组

### 50. XButton - 扩展按钮
**路径**: `src/components/XButton`
**功能**: 扩展功能按钮组件

**子组件**:
- `XButton`: 基础扩展按钮
- `XTextButton`: 文本按钮

---

## 使用注意事项

### 1. 组件导入
大部分组件已经在 `src/components/index.ts` 中进行了全局注册，可以直接使用：

```vue
<template>
  <Table :columns="columns" :data="data" />
  <Form :schema="schema" />
</template>
```

### 2. 类型支持
组件提供了完整的TypeScript类型支持，使用时会有智能提示。

### 3. 主题适配
所有组件都支持项目的主题系统，会自动适配当前主题色。

### 4. 响应式设计
组件支持响应式布局，在不同屏幕尺寸下会自动调整。

### 5. 国际化
支持的组件都内置了国际化功能，会根据当前语言显示对应文本。

---

## 完整示例

下面是一个使用多个组件构建完整页面的示例：

```vue
<template>
  <ContentWrap title="用户管理">
    <!-- 搜索表单 -->
    <Search
      :schema="searchSchema"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 操作栏 -->
    <div class="mb-4">
      <Button type="primary" @click="handleAdd">
        <Icon icon="ep:plus" class="mr-1" />
        新增用户
      </Button>
      <Button 
        type="danger" 
        :disabled="!selections.length"
        @click="handleBatchDelete"
      >
        批量删除
      </Button>
    </div>
    
    <!-- 数据表格 -->
    <Table
      ref="tableRef"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      selection
      @selection-change="handleSelectionChange"
      @page-change="handlePageChange"
    />
    
    <!-- 编辑对话框 -->
    <Dialog v-model="dialogVisible" title="用户信息" width="600px">
      <Form
        ref="formRef"
        :schema="formSchema"
        v-model="formData"
        label-width="100px"
      />
      
      <template #footer>
        <Button @click="dialogVisible = false">取消</Button>
        <Button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </Button>
      </template>
    </Dialog>
  </ContentWrap>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 表格相关
const tableRef = ref()
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const tableData = ref([])
const selections = ref([])

// 表单相关
const formRef = ref()
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formData = ref({})

// 搜索条件
const searchParams = reactive({})

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
})

// 搜索表单配置
const searchSchema = [
  {
    field: 'username',
    label: '用户名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名'
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  }
]

// 表格列配置
const columns = [
  {
    field: 'selection',
    type: 'selection',
    width: 50
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    width: 80
  },
  {
    field: 'avatar',
    label: '头像',
    width: 80,
    slots: {
      default: (scope) => (
        <el-avatar src={scope.row.avatar} />
      )
    }
  },
  {
    field: 'username',
    label: '用户名',
    minWidth: 120
  },
  {
    field: 'nickname',
    label: '昵称',
    minWidth: 120
  },
  {
    field: 'email',
    label: '邮箱',
    minWidth: 160
  },
  {
    field: 'status',
    label: '状态',
    width: 100,
    slots: {
      default: (scope) => (
        <DictTag 
          type="system_user_status" 
          value={scope.row.status} 
        />
      )
    }
  },
  {
    field: 'createTime',
    label: '创建时间',
    width: 180,
    formatter: (row) => formatDate(row.createTime)
  },
  {
    field: 'action',
    label: '操作',
    width: 200,
    fixed: 'right',
    slots: {
      default: (scope) => (
        <div>
          <Button 
            type="primary" 
            size="small" 
            link
            onClick={() => handleEdit(scope.row)}
          >
            编辑
          </Button>
          <Button 
            type="danger" 
            size="small" 
            link
            onClick={() => handleDelete(scope.row)}
          >
            删除
          </Button>
        </div>
      )
    }
  }
]

// 表单配置
const formSchema = [
  {
    field: 'username',
    label: '用户名',
    component: 'Input',
    required: true,
    colProps: { span: 12 }
  },
  {
    field: 'nickname',
    label: '昵称',
    component: 'Input',
    required: true,
    colProps: { span: 12 }
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    componentProps: {
      type: 'email'
    },
    colProps: { span: 12 }
  },
  {
    field: 'mobile',
    label: '手机号',
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    field: 'deptId',
    label: '部门',
    component: 'DeptSelectForm',
    colProps: { span: 12 }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Radio',
    value: 1,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    colProps: { span: 12 }
  },
  {
    field: 'avatar',
    label: '头像',
    component: 'UploadImg',
    colProps: { span: 24 }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'Input',
    componentProps: {
      type: 'textarea',
      rows: 3
    },
    colProps: { span: 24 }
  }
]

// 方法定义
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      ...searchParams
    }
    const { data } = await getUserPage(params)
    tableData.value = data.list
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = (values) => {
  Object.assign(searchParams, values)
  currentPage.value = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    delete searchParams[key]
  })
  currentPage.value = 1
  fetchData()
}

const handlePageChange = ({ currentPage: page, pageSize: size }) => {
  currentPage.value = page
  pageSize.value = size
  fetchData()
}

const handleSelectionChange = (vals) => {
  selections.value = vals
}

const handleAdd = () => {
  formData.value = {}
  dialogVisible.value = true
}

const handleEdit = (row) => {
  formData.value = { ...row }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  try {
    submitLoading.value = true
    if (formData.value.id) {
      await updateUser(formData.value)
      ElMessage.success('更新成功')
    } else {
      await createUser(formData.value)
      ElMessage.success('创建成功')
    }
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
      type: 'warning'
    })
    await deleteUser(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的${selections.value.length}条记录吗？`, '提示', {
      type: 'warning'
    })
    const ids = selections.value.map(item => item.id)
    await batchDeleteUser(ids)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>
```

---

## 开发建议

1. **统一使用封装组件**: 优先使用项目封装的组件，避免直接使用第三方组件
2. **遵循命名规范**: 组件名称采用PascalCase格式
3. **合理使用插槽**: 充分利用组件提供的插槽功能进行定制
4. **性能优化**: 对于大数据量的组件使用虚拟滚动等优化手段
5. **可访问性**: 注意组件的可访问性，支持键盘导航和屏幕阅读器

---

## 常见问题解答

### Q1: 如何自定义表单组件？
A: 在Form组件的schema中，可以通过以下方式自定义：
```javascript
{
  field: 'custom',
  label: '自定义组件',
  component: 'slot', // 使用插槽
  slots: {
    default: (formModel) => {
      return <YourCustomComponent v-model={formModel.custom} />
    }
  }
}
```

### Q2: 表格如何实现复杂的自定义列？
A: 使用slots配置：
```javascript
{
  field: 'complex',
  label: '复杂列',
  slots: {
    default: (scope) => {
      const { row, column, $index } = scope
      return (
        <div>
          <el-image src={row.image} />
          <span>{row.name}</span>
        </div>
      )
    }
  }
}
```

### Q3: 如何处理文件上传的自定义逻辑？
A: UploadFile组件支持自定义上传函数：
```vue
<UploadFile
  :http-request="customUpload"
  @upload-success="handleSuccess"
/>

<script>
const customUpload = async (options) => {
  const { file, onProgress, onSuccess, onError } = options
  try {
    // 自定义上传逻辑
    const response = await uploadFile(file, {
      onUploadProgress: (progressEvent) => {
        const percent = (progressEvent.loaded / progressEvent.total) * 100
        onProgress({ percent })
      }
    })
    onSuccess(response)
  } catch (error) {
    onError(error)
  }
}
</script>
```

### Q4: 如何实现表格的虚拟滚动？
A: 对于大数据量，可以使用Table组件的虚拟滚动特性：
```vue
<Table
  :columns="columns"
  :data="largeData"
  virtual-scroll
  :item-height="50"
  :visible-count="20"
/>
```

### Q5: 如何自定义Dialog的样式？
A: 通过CSS变量或deep选择器：
```vue
<Dialog
  v-model="visible"
  title="自定义对话框"
  class="custom-dialog"
>
  <YourContent />
</Dialog>

<style scoped>
.custom-dialog :deep(.el-dialog) {
  border-radius: 8px;
}

.custom-dialog :deep(.el-dialog__header) {
  background: linear-gradient(45deg, #409EFF, #67C23A);
  color: white;
}
</style>
```

### Q6: 如何处理表单验证？
A: Form组件内置了验证功能：
```javascript
const formSchema = [
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    required: true,
    rules: [
      { required: true, message: '请输入邮箱' },
      { type: 'email', message: '邮箱格式不正确' },
      {
        validator: (rule, value, callback) => {
          // 自定义验证逻辑
          if (value && !value.endsWith('@company.com')) {
            callback(new Error('必须使用公司邮箱'))
          } else {
            callback()
          }
        }
      }
    ]
  }
]
```

### Q7: 如何实现组件的国际化？
A: 组件支持i18n，使用useI18n hook：
```vue
<template>
  <Button>{{ t('common.save') }}</Button>
</template>

<script setup>
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()
</script>
```

### Q8: 如何处理表格的导出功能？
A: 可以结合Table组件实现：
```vue
<template>
  <div>
    <Button @click="handleExport" :loading="exportLoading">
      导出Excel
    </Button>
    <Table ref="tableRef" :columns="columns" :data="data" />
  </div>
</template>

<script setup>
const handleExport = async () => {
  exportLoading.value = true
  try {
    const tableData = tableRef.value.data
    await exportToExcel(tableData, '用户列表.xlsx')
  } finally {
    exportLoading.value = false
  }
}
</script>
```

---

*本文档持续更新中，如有疑问请联系开发团队。*
