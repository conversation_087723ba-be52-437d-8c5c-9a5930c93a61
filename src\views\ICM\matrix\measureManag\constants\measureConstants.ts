/**
 * 内控措施管理相关常量
 */

import { SelectItemType } from '@/api/public/public.types'

/**
 * 措施类型枚举
 */
export enum MEASURE_TYPE_ENUM {
  SYSTEM = 'system', // 制度措施
  PROCESS = 'process', // 流程措施
  OPERATION = 'operation', // 操作措施
  MANAGEMENT = 'management', // 管理措施
  CONTROL = 'control', // 控制措施
  MONITORING = 'monitoring' // 监控措施
}

/**
 * 措施类型选项列表
 */
export const MEASURE_TYPE_LIST: SelectItemType[] = [
  { label: '制度措施', value: MEASURE_TYPE_ENUM.SYSTEM },
  { label: '流程措施', value: MEASURE_TYPE_ENUM.PROCESS },
  { label: '操作措施', value: MEASURE_TYPE_ENUM.OPERATION },
  { label: '管理措施', value: MEASURE_TYPE_ENUM.MANAGEMENT },
  { label: '控制措施', value: MEASURE_TYPE_ENUM.CONTROL },
  { label: '监控措施', value: MEASURE_TYPE_ENUM.MONITORING }
]

/**
 * 风险等级枚举
 */
export enum RISK_LEVEL_ENUM {
  HIGH = 'high', // 高风险
  MEDIUM = 'medium', // 中风险
  LOW = 'low' // 低风险
}

/**
 * 风险等级选项列表
 */
export const RISK_LEVEL_LIST: SelectItemType[] = [
  { label: '高风险', value: RISK_LEVEL_ENUM.HIGH },
  { label: '中风险', value: RISK_LEVEL_ENUM.MEDIUM },
  { label: '低风险', value: RISK_LEVEL_ENUM.LOW }
]

/**
 * 风险类型选项列表
 */
export const RISK_TYPE_LIST: SelectItemType[] = [
  {
    label: '一级风险',
    value: 1,
    riskLevel: '高',
    riskProbability: '极高',
    riskDesc: '这是一级风险'
  },
  {
    label: '二级风险',
    value: 2,
    riskLevel: '中',
    riskProbability: '极高',
    riskDesc: '这是二级风险'
  },
  {
    label: '三级风险',
    value: 3,
    riskLevel: '低',
    riskProbability: '极高',
    riskDesc: '这是三级风险'
  },
  {
    label: '四级风险',
    value: 4,
    riskLevel: '低',
    riskProbability: '极高',
    riskDesc: '这是四级风险'
  },
  {
    label: '五级风险',
    value: 5,
    riskLevel: '低',
    riskProbability: '极高',
    riskDesc: '这是五级风险'
  },
  {
    label: '六级风险',
    value: 6,
    riskLevel: '低',
    riskProbability: '极高',
    riskDesc: '这是六级风险'
  }
]

/**
 * 支持的文件类型
 */
export const SUPPORTED_FILE_TYPES = '.doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx'

/**
 * 文件上传最大大小（50MB）
 */
export const MAX_FILE_SIZE = 50 * 1024 * 1024
