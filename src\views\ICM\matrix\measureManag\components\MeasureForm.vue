<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="drawer-header">
        <div class="header-content">
          <Back
            :title="title"
            hasEmit
            showCancel
            showSave
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSave"
          />
        </div>
      </div>

      <ElScrollbar class="drawer-content">
        <div class="form-container">
          <!-- 基础信息 -->
          <div class="form-section">
            <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
            <div class="mt-12px">
              <Form
                ref="basicFormRef"
                :schema="basicInfoSchema"
                @register="registerBasic"
                isCol
                :rules="formRules"
                :model="formData"
              />
            </div>
          </div>

          <!-- 控制信息 -->
          <div class="form-section">
            <div class="bg-#f1f2fe lh-40px pl-5">控制信息</div>
            <div class="mt-12px">
              <Form
                ref="controlFormRef"
                :schema="controlInfoSchema"
                @register="registerControl"
                isCol
                :rules="formRules"
                :model="formData"
              />
            </div>
          </div>

          <!-- 风险信息 -->
          <div class="form-section">
            <div class="bg-#f1f2fe lh-40px pl-5">风险</div>
            <div class="mt-12px">
              <Form
                ref="riskFormRef"
                :schema="riskInfoSchema"
                @register="registerRisk"
                isCol
                :rules="formRules"
                :model="formData"
              >
                <template #riskLevel>
                  <el-input disabled v-model="riskInfo.riskLevel" />
                </template>
                <template #riskProbability>
                  <el-input v-model="riskInfo.riskProbability" />
                </template>
                <template #riskDesc>
                  <el-input disabled v-model="riskInfo.riskDesc" />
                </template>
              </Form>
            </div>
          </div>

          <!-- 附件 -->
          <div class="form-section">
            <div class="bg-#f1f2fe lh-40px pl-5">附件</div>
            <div class="mt-12px">
              <AttachmentManager
                v-model="formData.attachments"
                :mode="mode"
                @change="handleAttachmentChange"
              />
            </div>
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getBasicInfoSchemas,
  getControlInfoSchemas,
  getRiskInfoSchemas,
  getMeasureFormRules
} from '../schemas/measureSchemas'
import { getMeasureDetailApi, createMeasureApi, updateMeasureApi } from '@/api/ICM/measure'
import { useMessage } from '@/hooks/web/useMessage'
import AttachmentManager from './AttachmentManager.vue'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle } = useLayout()
import { RISK_TYPE_LIST } from '../constants/measureConstants'
// 使用 CRUD Schemas 生成表单配置
// 获取分组Schema
const basicInfoSchema = useCrudSchemas(getBasicInfoSchemas()).allSchemas.formSchema
const controlInfoSchema = useCrudSchemas(getControlInfoSchemas()).allSchemas.formSchema
const riskInfoSchema = useCrudSchemas(getRiskInfoSchemas()).allSchemas.formSchema

const message = useMessage()

// 发送事件
const emit = defineEmits(['save', 'close'])

// 响应式数据
const visible = ref(false)
const mode = ref('add') // add 或 edit
const measureId = ref<string | null>(null)
const formData = ref<any>({
  attachments: []
})

// 标题计算属性
const title = computed(() => {
  return mode.value === 'add' ? '新增措施' : '编辑措施'
})

// 表单验证规则
const formRules = reactive(getMeasureFormRules())

// 使用表单Hook
const { register: registerBasic, methods: basicMethods } = useForm()
const { register: registerControl, methods: controlMethods } = useForm()
const { register: registerRisk, methods: riskMethods } = useForm()

// 设置表单数据
const setValue = async () => {
  if (mode.value === 'edit' && measureId.value) {
    try {
      const res = await getMeasureDetailApi(measureId.value)
      const data = res.data

      formData.value = {
        ...data,
        attachments: data.attachments || []
      }

      // 设置各个表单的值
      const basicValues = {}
      const controlValues = {}
      const riskValues = {}

      basicInfoSchema.forEach((item) => {
        basicValues[item.field] = data[item.field]
      })
      controlInfoSchema.forEach((item) => {
        controlValues[item.field] = data[item.field]
      })
      riskInfoSchema.forEach((item) => {
        riskValues[item.field] = data[item.field]
      })

      basicMethods.setValues(basicValues)
      controlMethods.setValues(controlValues)
      riskMethods.setValues(riskValues)
    } catch (error) {
      console.error('加载措施详情失败:', error)
      message.error('加载措施详情失败')
    }
  } else {
    // 新增模式，重置表单
    formData.value = {
      attachments: []
    }
    // 重置表单值
    basicMethods.setValues({})
    controlMethods.setValues({})
    riskMethods.setValues({})
  }
}

// 打开弹窗
const open = async (type: string, id: string | null = null) => {
  mode.value = type
  measureId.value = id
  visible.value = true

  await setValue()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  measureId.value = null
  formData.value = {
    attachments: []
  }
  emit('close')
}

// 附件变化处理
const handleAttachmentChange = (attachments: any[]) => {
  formData.value.attachments = attachments
}

// 保存
const handleSave = async () => {
  try {
    // 验证所有表单
    const basicValid = await basicMethods.validate()
    const controlValid = await controlMethods.validate()
    const riskValid = await riskMethods.validate()

    if (!basicValid || !controlValid || !riskValid) return

    // 获取所有表单数据
    const basicValues = await basicMethods.getFormData()
    const controlValues = await controlMethods.getFormData()
    const riskValues = await riskMethods.getFormData()

    // 合并表单数据和附件数据
    const submitData: any = {
      ...basicValues,
      ...controlValues,
      ...riskValues,
      attachments: formData.value.attachments
    }

    if (mode.value === 'add') {
      await createMeasureApi(submitData)
      message.success('新增成功')
    } else {
      submitData.id = measureId.value
      await updateMeasureApi(submitData)
      message.success('编辑成功')
    }

    handleClose()
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  }
}
const riskFormRef = ref()
const riskInfo = computed(() => {
  const risk = RISK_TYPE_LIST.find((item) => item.value === riskFormRef.value?.formModel.riskName)
  return {
    riskLevel: risk?.riskLevel,
    riskProbability: risk?.riskProbability,
    riskDesc: risk?.riskDesc
  }
})

// 对外暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.measure-form-drawer {
  position: fixed;
  top: 50px;
  right: 0;
  width: calc(100% - 200px);
  height: calc(100vh - 50px);
  background: white;
  z-index: 990;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);

  .drawer-header {
    height: 50px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;

    .header-content {
      width: 100%;
      display: flex;
      align-items: center;
    }
  }

  .drawer-content {
    height: calc(100% - 50px);
    padding: 0;

    .form-container {
      padding: 20px;
    }
  }

  .form-section {
    margin-bottom: 30px;

    .section-title {
      background-color: #f1f2fe;
      line-height: 40px;
      padding-left: 20px;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
    }

    .section-content {
      padding: 0 20px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .measure-form-drawer {
    width: 100%;
    left: 0;
  }
}
</style>
