import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { DICT_TYPE, getStrDictOptions, getDictLabel } from '@/utils/dict'
import dayjs from 'dayjs'
import { dateFormatter2 } from '@/utils/formatTime'
import { getLawyerTeamApi } from '@/api/law/case'
/**
 * 案件管理 - 统一Schema配置
 * 通过 isSearch、isTable、isForm、isDetail 控制字段在不同场景下的显示
 */
export const caseSchemas: CrudSchema[] = [
  // ==================== 序号列 ====================
  {
    field: 'index',
    label: '序号',
    type: 'index',
    isTable: true
  },

  // ==================== 基础信息 ====================
  {
    field: 'caseName',
    label: '案件名称',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入案件名称'
      },
      colProps: {
        span: 8
      }
    },
    table: {
      width: 180
    },
    form: {
      component: 'Input',
      componentProps: {
        style: {
          width: '300px'
        },
        maxlength: 50,
        clearable: true
      }
    }
  },
  {
    field: 'caseCode',
    label: '案件编号',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 160
    },
    form: {
      component: 'Input',
      componentProps: {
        style: {
          width: '300px'
        },
        disabled: true,
        placeholder: '系统自动生成'
      }
    }
  },
  {
    field: 'caseType',
    label: '案件类型',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.CASE_TYPE,
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      componentProps: {
        style: {
          width: '300px'
        },
        clearable: true
      }
    }
  },
  {
    field: 'caseProcess',
    label: '案件程序',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 120,
      formatter: (row: any) => {
        return getCaseProcedureOptions(row.caseType).find((item) => item.value === row.caseProcess)
          ?.label
      }
    },
    detail: {
      formatter: (key, row: any) => {
        return getCaseProcedureOptions(row.caseType).find((item) => item.value === row.caseProcess)
          ?.label
      }
    },
    form: {
      component: 'Select',
      componentProps: {
        style: {
          width: '300px'
        },
        options: [],
        clearable: true
      }
    }
  },
  {
    field: 'involvedAmount',
    label: '涉案金额(元)',
    isTable: false, // 不在列表中显示
    isForm: true,
    isDetail: true,
    table: {
      width: 130
    },
    form: {
      component: 'InputNumber',
      value: 0,
      componentProps: {
        style: {
          width: '300px'
        },
        clearable: true,
        controlsPosition: 'right',
        precision: null,
        min: 0
      }
    }
  },
  {
    field: 'amountRemark',
    label: '金额备注',
    isForm: true,
    isDetail: true,
    isTable: false,
    form: {
      component: 'Input',
      componentProps: {
        style: {
          width: '300px'
        },
        maxlength: 100,
        clearable: true
      }
    }
  },
  {
    field: 'isMajor',
    label: '是否重大',
    isForm: true,
    isDetail: true,
    isTable: false,
    dictType: DICT_TYPE.YES_NO_LIST,
    form: {
      component: 'Select',
      componentProps: {
        style: {
          width: '300px'
        },
        clearable: true
      }
    }
  },
  {
    field: 'isForeign',
    label: '是否涉外',
    isForm: true,
    isDetail: true,
    isTable: false,
    dictType: DICT_TYPE.YES_NO_LIST,
    form: {
      component: 'Select',
      componentProps: {
        style: {
          width: '300px'
        },
        clearable: true
      }
    }
  },
  {
    field: 'belongUnitId',
    label: '所属单位',
    isSearch: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请选择',
        style: {
          width: '120px'
        }
      }
    },
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: {
          width: '300px'
        },
        clearable: true
      }
    },
    table: {
      formatter: (row: any) => {
        return row.belongUnitName
      }
    },
    detail: {
      formatter: (key, row: any) => {
        return row.belongUnitName
      }
    }
  },

  // ==================== 诉讼信息 ====================
  {
    field: 'caseReason',
    label: '案由',
    isForm: true,
    isDetail: true,
    isTable: false,
    form: {
      component: 'Input',
      colProps: {
        span: 24
      },
      componentProps: {
        maxlength: 10000,
        clearable: true,
        type: 'textarea',
        rows: 4
      }
    }
  },
  {
    field: 'filingDate',
    label: '立案日期',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 120,
      formatter: dateFormatter2
    },
    detail: {
      dateFormat: 'YYYY-MM-DD'
    },
    form: {
      component: 'DatePicker',
      componentProps: {
        style: {
          width: '300px'
        },
        clearable: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'x'
      }
    }
  },
  {
    field: 'ourStatus',
    label: '诉讼地位',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.LITIGATION_POSITION,
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      componentProps: {
        style: {
          width: '300px'
        },
        options: [],
        clearable: true
      }
    }
  },
  {
    field: 'lawyerInfo',
    label: '代理律师',
    isTable: false,
    isForm: true,
    isDetail: true,
    table: {
      width: 120
    },
    form: {
      component: 'Cascader',
      api: async () => {
        const res = await getLawyerTeamApi()
        return [
          {
            label: '外部律师',
            value: 'EXTERNAL',
            children:
              res.externalLawyerList?.map((item) => ({
                label: item.lawyerName,
                value: item.id
              })) || []
          },
          {
            label: '内部律师',
            value: 'INTERNAL',
            children:
              res.internalTeamList?.map((item) => ({
                label: item.lawyerName,
                value: item.id
              })) || []
          }
        ]
      },
      componentProps: {
        style: {
          width: '300px'
        },
        clearable: true,
        filterable: true
      }
    }
  },
  {
    field: 'caseStatus',
    label: '案件状态',
    isTable: true,
    isForm: true,
    dictType: DICT_TYPE.CASE_STATUS,
    isDetail: true,
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      componentProps: {
        style: {
          width: '300px'
        },
        options: [],
        clearable: true
      }
    }
  },
  {
    field: 'status',
    label: '审批状态',
    isTable: true,
    isForm: false,
    dictType: DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS,
    isDetail: true,
    table: {
      width: 120
    }
  },
  {
    field: 'acceptOrg',
    label: '受理机构',
    isForm: true,
    isDetail: true,
    isTable: false,
    form: {
      component: 'Input',
      componentProps: {
        style: {
          width: '300px'
        },
        maxlength: 50,
        clearable: true
      }
    }
  },
  {
    field: 'acceptOrgType',
    label: '受理机构类型',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.ACCEPTANCE_AGENCY_TYPE,
    form: {
      component: 'Select',
      componentProps: {
        style: {
          width: '300px'
        },
        options: [],
        clearable: true
      }
    }
  },
  {
    field: 'acceptOrgContact',
    label: '受理机构经办人',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: {
          width: '300px'
        },
        maxlength: 50,
        clearable: true
      }
    }
  },
  {
    field: 'contactPhone',
    label: '联系电话',
    isForm: true,
    isDetail: true,
    isTable: false,
    form: {
      component: 'Input',
      componentProps: {
        style: {
          width: '300px'
        },
        maxlength: 11,
        clearable: true
      }
    }
  },
  {
    field: 'attachment',
    label: '附件',
    isForm: true,
    isTable: false,
    form: {
      component: 'UploadFiles',
      colProps: {
        span: 24
      }
    }
  },

  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isTable: true,
    table: {
      width: 200,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于表单分组显示）
 */
export const getBasicSchemas = (): CrudSchema[] => {
  return caseSchemas.filter((schema) =>
    [
      'caseName',
      'caseCode',
      'caseType',
      'caseProcess',
      'involvedAmount',
      'amountRemark',
      'isMajor',
      'isForeign',
      'belongUnitId'
    ].includes(schema.field)
  )
}

/**
 * 获取诉讼信息字段（用于表单分组显示）
 */
export const getLitigationSchemas = (): CrudSchema[] => {
  return caseSchemas.filter((schema) =>
    [
      'caseReason',
      'filingDate',
      'ourStatus',
      'lawyerInfo',
      'caseStatus',
      'acceptOrg',
      'acceptOrgType',
      'acceptOrgContact',
      'contactPhone',
      'attachment'
    ].includes(schema.field)
  )
}

/**
 * 根据案件类型获取案件程序选项
 */
export const getCaseProcedureOptions = (caseType: string) => {
  if (!caseType) return []

  // 根据案件类型返回对应的程序选项
  switch (caseType) {
    case '1':
      return getStrDictOptions('case_process_two') // 民事案件
    case '2':
      return getStrDictOptions('case_process_tre') // 刑事案件
    case '3': // 行政案件
      return getStrDictOptions('case_process_one')
    default:
      return []
  }
}

export const caseFormRules = {
  // ==================== 基础信息验证规则 ====================

  // 案件名称：仅允许中英文字符和数字，字符数限制50
  caseName: [
    { required: true, message: '', trigger: 'blur' },
    { max: 50, message: '案件名称不能超过50个字符', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s]*$/, message: '请输入中英文字符和数字', trigger: 'blur' }
  ],

  // 涉案金额：仅允许阿拉伯数字，保留整数
  involvedAmount: [
    { required: true, message: '', trigger: 'blur' },
    { pattern: /^\d*$/, message: '请输入数字', trigger: 'blur' }
  ],

  // 金额备注：仅允许中英文字符和数字，字数限制100
  amountRemark: [
    { required: true, message: '', trigger: 'blur' },
    { max: 100, message: '金额备注不能超过100个字符', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s]*$/, message: '请输入中英文字符和数字', trigger: 'blur' }
  ],

  // ==================== 诉讼信息验证规则 ====================

  // 案由：仅允许中英文字符和数字，字符数限制10000
  caseReason: [
    { required: true, message: '', trigger: 'blur' },
    { max: 10000, message: '案由不能超过10000个字符', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s]*$/, message: '请输入中英文字符和数字', trigger: 'blur' }
  ],

  // 受理机构：仅允许中英文字符和数字，字符数限制50
  acceptOrg: [
    { required: true, message: '', trigger: 'blur' },
    { max: 50, message: '受理机构不能超过50个字符', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9\s]*$/, message: '请输入中英文字符和数字', trigger: 'blur' }
  ],

  // 受理机构经办人：仅允许中英文文字，字数限制50
  acceptOrgContact: [
    { required: true, message: '', trigger: 'blur' },
    { max: 50, message: '受理机构经办人不能超过50个字符', trigger: 'blur' },
    { pattern: /^[\u4e00-\u9fa5a-zA-Z\s]*$/, message: '请输入中英文文字', trigger: 'blur' }
  ],

  // 联系电话：仅允许阿拉伯数字，字数限制11
  contactPhone: [
    { required: true, message: '', trigger: 'blur' },
    { max: 11, message: '联系电话不能超过11位数字', trigger: 'blur' },
    { pattern: /^\d*$/, message: '请输入数字', trigger: 'blur' }
  ],
  //其他字段都是   { required: true, message: '', trigger: 'blur' },
  // 案由：仅允许中英文字符和数字，字符数限制10000
  caseType: [{ required: true, message: '', trigger: 'change' }],
  caseProcess: [{ required: true, message: '', trigger: 'change' }],
  ourStatus: [{ required: true, message: '', trigger: 'change' }],
  caseStatus: [{ required: true, message: '', trigger: 'change' }],
  acceptOrgType: [{ required: true, message: '', trigger: 'change' }],
  isMajor: [{ required: true, message: '', trigger: 'change' }],
  isForeign: [{ required: true, message: '', trigger: 'change' }],
  filingDate: [{ required: true, message: '', trigger: 'change' }],
  lawyerInfo: [{ required: true, message: '', trigger: 'change' }],
  // attachment: [{ required: true, message: '', trigger: 'change' }],
  belongUnitId: [{ required: true, message: '', trigger: 'change' }]
}
