<template>
  <Dialog v-model="dialogVisible" title="选择用户" width="900px">
    <div class="flex items-start gap-4" v-loading="formLoading">
      <!-- 搜索区域 -->
      <div class="w-full mb-4">
        <div class="flex items-center gap-4 mb-4">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入姓名"
            clearable
            style="width: 200px"
          />
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 用户表格 -->
    <div class="flex gap-4">
      <!-- 可选用户列表 -->
      <div class="flex-1">
        <div class="mb-2 font-medium">可选用户</div>
        <el-table
          ref="userTableRef"
          :data="userList"
          height="400px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column label="姓名" prop="nickname" width="120" />
          <el-table-column label="电话" prop="mobile" width="140" />
          <el-table-column label="部门" prop="deptName" />
        </el-table>

        <!-- 分页 -->
        <div class="mt-4">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 已选用户列表 -->
      <div class="w-80">
        <div class="mb-2 font-medium">已选用户 ({{ selectedUsers.length }})</div>
        <div class="border rounded p-2 bg-gray-50" style="height: 400px; overflow-y: auto">
          <div
            v-for="user in selectedUsers"
            :key="user.id"
            class="flex items-center justify-between p-2 mb-2 bg-white rounded border"
          >
            <div class="flex-1">
              <div class="font-medium">{{ user.nickname }}</div>
              <div class="text-sm text-gray-500">{{ user.deptName }}</div>
            </div>
            <el-button type="danger" link @click="removeUser(user)"> × </el-button>
          </div>
          <div v-if="selectedUsers.length === 0" class="text-center text-gray-400 mt-8">
            暂无选择用户
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button
        :disabled="formLoading || selectedUsers.length === 0"
        type="primary"
        @click="submitForm"
      >
        确 定
      </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { Dialog } from '@/components'
import { ElMessage } from 'element-plus'
import * as UserApi from '@/api/system/user'

defineOptions({ name: 'UserSelectDialog' })

interface UserInfo {
  id: number
  nickname: string
  mobile?: string
  deptName?: string
  deptId?: number
}

const emit = defineEmits<{
  confirm: [users: UserInfo[]]
}>()

const props = defineProps({
  multiple: {
    type: Boolean,
    default: true
  },
  excludeIds: {
    type: Array as PropType<number[]>,
    default: () => []
  }
})

const dialogVisible = ref(false)
const formLoading = ref(false)
const userTableRef = ref()

// 搜索表单
const searchForm = reactive({
  name: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 用户列表
const userList = ref<UserInfo[]>([])
const selectedUsers = ref<UserInfo[]>([])
const tempSelectedUsers = ref<UserInfo[]>([]) // 临时选中的用户

// 获取用户列表
const getUserList = async () => {
  formLoading.value = true
  try {
    const params = {
      pageNo: pagination.page,
      pageSize: pagination.size,
      nickname: searchForm.name || undefined
    }

    const { list, total } = await UserApi.getUserPage(params)

    // 过滤掉排除的用户
    userList.value = list.filter((user) => !props.excludeIds.includes(user.id))
    pagination.total = total

    // 设置已选择用户的选中状态
    setTimeout(() => {
      const selectedIds = selectedUsers.value.map((user) => user.id)
      userList.value.forEach((user) => {
        if (selectedIds.includes(user.id)) {
          userTableRef.value?.toggleRowSelection(user, true)
        }
      })
    }, 100)
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    formLoading.value = false
  }
}

// 处理选择变化
const handleSelectionChange = (selection: UserInfo[]) => {
  tempSelectedUsers.value = selection

  if (!props.multiple && selection.length > 1) {
    // 单选模式，只保留最后选择的
    const lastSelected = selection[selection.length - 1]
    userTableRef.value?.clearSelection()
    userTableRef.value?.toggleRowSelection(lastSelected, true)
    tempSelectedUsers.value = [lastSelected]
  }

  // 更新选中用户列表
  const existingUsers = selectedUsers.value.filter(
    (user) => !userList.value.some((u) => u.id === user.id)
  )
  selectedUsers.value = [...existingUsers, ...tempSelectedUsers.value]
}

// 移除用户
const removeUser = (user: UserInfo) => {
  selectedUsers.value = selectedUsers.value.filter((u) => u.id !== user.id)
  // 如果该用户在当前页面，取消选择
  const userInCurrentPage = userList.value.find((u) => u.id === user.id)
  if (userInCurrentPage) {
    userTableRef.value?.toggleRowSelection(userInCurrentPage, false)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getUserList()
}

// 重置
const handleReset = () => {
  searchForm.name = ''
  pagination.page = 1
  getUserList()
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  getUserList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  getUserList()
}

// 提交选择
const submitForm = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择用户')
    return
  }

  emit('confirm', selectedUsers.value)
  dialogVisible.value = false
  ElMessage.success('选择用户成功')
}

// 打开弹窗
const open = (preSelectedUsers: UserInfo[] = []) => {
  selectedUsers.value = [...preSelectedUsers]
  tempSelectedUsers.value = []
  searchForm.name = ''
  pagination.page = 1
  dialogVisible.value = true
  getUserList()
}

// 重置表单
const resetForm = () => {
  selectedUsers.value = []
  tempSelectedUsers.value = []
  searchForm.name = ''
  pagination.page = 1
  userList.value = []
  if (userTableRef.value) {
    userTableRef.value.clearSelection()
  }
}

defineExpose({
  open,
  resetForm
})
</script>

<style scoped lang="scss">
.el-table {
  .el-table__body-wrapper {
    height: 400px;
    overflow-y: auto;
  }
}
</style>
