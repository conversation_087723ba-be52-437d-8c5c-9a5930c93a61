<template>
  <div class="authorization-info">
    <Descriptions
      title="授权基本信息"
      :data="authorizationData"
      :columns="2"
      :schema="basicAllSchemas.detailSchema"
      :collapse="false"
    />

    <Descriptions
      title="授权人信息"
      :data="authorizationData"
      :columns="2"
      :schema="authorizerAllSchemas.detailSchema"
      :collapse="false"
    />

    <Descriptions
      title="被授权人信息"
      :data="authorizationData"
      :columns="2"
      :schema="authorizedAllSchemas.detailSchema"
      :collapse="false"
    />

    <Descriptions
      title="授权详情"
      :data="authorizationData"
      :columns="2"
      :schema="authorizationDetailAllSchemas.detailSchema"
      :collapse="false"
    />
  </div>
</template>

<script setup>
import { Descriptions } from '@/components'
import { getDictLabel, DICT_TYPE } from '@/utils/dict'
import { defineProps, watch, ref, onMounted } from 'vue'
import { getAuthorizationApplication } from '@/api/law/authorization'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getBasicInfoSchemas,
  getAuthorizerSchemas,
  getAuthorizedSchemas,
  getAuthorizationDetailSchemas
} from '../../schemas/authorizationSchemas'

// 使用 CRUD Schemas 生成详情配置
const basicSchemas = getBasicInfoSchemas()
const authorizerSchemas = getAuthorizerSchemas()
const authorizedSchemas = getAuthorizedSchemas()
const authorizationDetailSchemas = getAuthorizationDetailSchemas()

const { allSchemas: basicAllSchemas } = useCrudSchemas(basicSchemas)
const { allSchemas: authorizerAllSchemas } = useCrudSchemas(authorizerSchemas)
const { allSchemas: authorizedAllSchemas } = useCrudSchemas(authorizedSchemas)
const { allSchemas: authorizationDetailAllSchemas } = useCrudSchemas(authorizationDetailSchemas)

const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  }
})

const authorizationData = ref({})

const getAuthorizationDetail = async (id) => {
  if (!id) return
  try {
    const res = await getAuthorizationApplication(id)
    authorizationData.value = res || {}
  } catch (error) {
    console.error('获取授权委托详情失败:', error)
  }
}
onMounted(() => {
  getAuthorizationDetail(props.id)
})
</script>
