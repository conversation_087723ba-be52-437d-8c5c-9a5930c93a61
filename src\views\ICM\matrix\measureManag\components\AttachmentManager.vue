<template>
  <div class="attachment-manager">
    <div class="upload-section" v-if="mode !== 'view'">
      <div class="section-title">上传附件</div>
      <Button type="primary" @click="handleUpload">
        <Icon icon="ep:upload-filled" />
        上传文件
      </Button>
    </div>

    <div class="file-list-section">
      <div class="section-title">相关附件</div>
      <Table :data="attachments" :columns="columns" style="width: 100%">
        <template #fileName="{ row }">
          <div class="file-name">
            <Icon :icon="getFileIcon(row.fileName)" class="file-icon" />
            <span>{{ row.fileName }}</span>
          </div>
        </template>

        <template #fileSize="{ row }">
          {{ formatFileSize(row.fileSize) }}
        </template>

        <template #isActive="{ row }">
          <el-switch
            v-model="row.isActive"
            @change="handleVersionChange(row)"
            :disabled="mode === 'view'"
          />
        </template>

        <template #action="{ row, $index }">
          <el-button link type="primary" @click="handlePreview(row)"> 预览 </el-button>
          <el-button link type="danger" @click="handleDelete(row, $index)" v-if="mode !== 'view'">
            删除
          </el-button>
        </template>
      </Table>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Button, Icon, Table } from '@/components'
import { SUPPORTED_FILE_TYPES } from '../constants/measureConstants'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSelectFile } from '@/components/UploadFile/src/useSelectFile'
import { useUpload } from '@/components/UploadFile/src/useUpload'

const { selectFile } = useSelectFile()
const { uploadUrl, httpRequest } = useUpload()

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'edit' // edit, view
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const fileList = ref([])
const attachments = ref([])

const columns = ref([
  {
    label: '文件名称',
    prop: 'fileName',
    width: 200
  },
  {
    label: '上传人',
    prop: 'uploader',
    width: 100
  },
  {
    label: '上传时间',
    prop: 'uploadTime',
    width: 160
  },
  {
    label: '版本号',
    prop: 'version',
    width: 100
  },
  {
    label: '文件大小',
    prop: 'fileSize',
    width: 120
  },
  {
    label: '版本应用',
    prop: 'isActive',
    width: 100
  },
  {
    label: '操作',
    prop: 'action',
    width: 120,
    fixed: 'right'
  }
])

// 上传文件
const handleUpload = async () => {
  try {
    const { file } = await selectFile({
      maxSize: 50, // 最大50MB
      accept: SUPPORTED_FILE_TYPES
    })
    const res = await httpRequest({
      file: file
    })

    // 创建新的附件对象
    const newAttachment = {
      id: Date.now(),
      fileName: file.name,
      fileUrl: res.data?.url || URL.createObjectURL(file),
      fileSize: file.size || 0,
      uploadTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
      uploader: '当前用户',
      version: 'V1.0',
      isActive: true
    }

    attachments.value.push(newAttachment)
    updateModelValue()
    ElMessage.success('上传成功')
  } catch (error) {
    ElMessage.error('文件上传失败')
  }
}

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    attachments.value = newVal && Array.isArray(newVal) ? [...newVal] : []

    // 将已有附件转换为文件列表格式
    if (attachments.value.length > 0) {
      fileList.value = attachments.value.map((item) => ({
        name: item.fileName,
        url: item.fileUrl,
        size: item.fileSize
      }))
    }
  },
  { immediate: true }
)

// 删除附件
const handleDelete = (row, index) => {
  ElMessageBox.confirm('确定要删除该附件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    attachments.value.splice(index, 1)
    // 同步更新文件列表
    fileList.value = fileList.value.filter((file) => file.name !== row.fileName)
    updateModelValue()
    ElMessage.success('删除成功')
  })
}

// 预览文件
const handlePreview = (row) => {
  if (row.fileUrl) {
    window.open(row.fileUrl, '_blank')
  } else {
    ElMessage.warning('暂无预览链接')
  }
}

// 版本切换
const handleVersionChange = (row) => {
  if (row.isActive) {
    // 将其他版本设为非激活
    attachments.value.forEach((item) => {
      if (item.id !== row.id) {
        item.isActive = false
      }
    })
  }
  updateModelValue()
}

// 更新v-model
const updateModelValue = () => {
  emit('update:modelValue', attachments.value)
  emit('change', attachments.value)
}

// 获取文件图标
const getFileIcon = (fileName) => {
  const ext = fileName.split('.').pop().toLowerCase()
  const iconMap = {
    doc: 'ep:document',
    docx: 'ep:document',
    pdf: 'ep:document',
    xls: 'ep:document',
    xlsx: 'ep:document',
    ppt: 'ep:document',
    pptx: 'ep:document'
  }
  return iconMap[ext] || 'ep:document'
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + ' MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
}

// 暴露方法
defineExpose({
  getFileList: () => fileList.value,
  getAttachments: () => attachments.value
})
</script>

<style lang="scss" scoped>
.attachment-manager {
  .upload-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #303133;
    }
  }

  .file-list-section {
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #303133;
    }

    .file-name {
      display: flex;
      align-items: center;

      .file-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }
}
</style>
