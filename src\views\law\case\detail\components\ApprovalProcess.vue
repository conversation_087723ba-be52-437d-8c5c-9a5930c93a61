<template>
  <div class="">
    <Title title="审批流程">
      <div class="flex my-8px">
        <div class="mx-8px">{{ info.time }}</div>
        <div class="mx-8px">{{ info.name }} </div>
        <div class="mx-8px">{{ info.event }} </div>
      </div>
      <Table :columns="tableColumns" :data="tableData"></Table>
    </Title>
  </div>
</template>

<script setup>
import { Back, Form, Descriptions, Title, Table } from '@/components'
import { defineProps, watch, ref, onMounted } from 'vue'
import { getCaseApprovalProcessApi } from '@/api/law/case'

const props = defineProps({
  id: {
    type: [Number, String],
    required: false,
    default: undefined
  }
})

const tableData = ref([])

const tableColumns = [
  {
    label: '时间',
    field: 'approvalTime'
  },
  {
    label: '审批人',
    field: 'approverName'
  },
  {
    label: '审批结果与反馈',
    field: 'approvalResult',
    formatter: (row) => {
      return `${row.approvalResult} （${row.approvalComment}）`
    }
  }
]

const info = ref({})

const getApprovalProcess = async (id) => {
  if (!id) return
  try {
    const res = await getCaseApprovalProcessApi(id)
    tableData.value = res.data || []
    info.value = res.info || {}
  } catch (error) {
    console.error('获取审批流程失败:', error)
    tableData.value = []
    info.value = {}
  }
}

// 监听id变化
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      getApprovalProcess(newId)
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped></style>
