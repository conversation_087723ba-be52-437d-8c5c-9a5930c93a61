/*
 * @Description: 案件管理Mock数据
 */
import Mock from 'mockjs'
import { SUCCESS_CODE } from '../public.constant'

const timeout = 1000

// 案件类型
const caseTypes = [1, 2, 3]
// 案件程序
const caseProcedures = [1, 2, 3, 4, 5, 6, 7]
// 诉讼地位
const litigationPositions = ['1', '2', '3', '4', '5']
// 案件状态
const caseStatuses = ['1', '2', '3', '4', '5', '6']
// 代理律师
const lawyers = ['张律师', '李律师', '王律师', '刘律师', '陈律师']
// 受理机构类型
const acceptanceAgencyTypes = ['1', '2', '3']
// 是否选项
const yesNoOptions = ['1', '0']

// 生成当事人数据
const generatePartyData = (count: number): any[] => {
  const parties: any[] = []
  const companies = ['北京科技有限公司', '上海贸易有限公司', '深圳投资有限公司', '广州制造有限公司']
  const names = ['张三', '李四', '王五', '赵六', '钱七']

  for (let i = 0; i < count; i++) {
    parties.push({
      name: Mock.Random.pick(companies),
      legalRepresentative: Mock.Random.pick(names),
      contact: Mock.Random.pick(names),
      phone: Mock.Random.integer(13000000000, 18999999999).toString(),
      address: Mock.Random.county(true)
    })
  }
  return parties
}

// 生成Mock数据
const generateCaseData = (count: number): any[] => {
  const list: any[] = []
  for (let i = 1; i <= count; i++) {
    const caseType = Mock.Random.pick(caseTypes)
    list.push({
      id: i,
      caseName: `${Mock.Random.pick(['合同纠纷', '侵权纠纷', '债务纠纷', '劳动争议', '知识产权纠纷'])}案`,
      caseNo: `AJ${new Date().getFullYear()}${Mock.Random.integer(1000, 9999)}${Mock.Random.integer(1000, 9999)}`,
      caseType,
      caseProcedure: Mock.Random.pick(caseProcedures),
      involvedAmount: Mock.Random.integer(10000, 10000000),
      caseRemark: Mock.Random.pick(['合同约定金额', '实际损失金额', '预估金额', '争议金额']),
      isMajor: Mock.Random.pick(yesNoOptions),
      isForeign: Mock.Random.pick(yesNoOptions),
      litigationReason: Mock.Random.pick([
        '合同纠纷',
        '侵权责任纠纷',
        '债权债务纠纷',
        '劳动争议',
        '知识产权纠纷'
      ]),
      litigationDate: Mock.Random.date('yyyy-MM-dd'),
      litigationPosition: Mock.Random.pick(litigationPositions),
      lawyer: Mock.Random.pick(lawyers),
      caseStatus: Mock.Random.pick(caseStatuses),
      acceptanceAgency: Mock.Random.pick([
        '北京市朝阳区人民法院',
        '上海市浦东新区人民法院',
        '深圳市南山区人民法院',
        '广州市天河区人民法院'
      ]),
      acceptanceAgencyType: Mock.Random.pick(acceptanceAgencyTypes),
      acceptanceHandler: Mock.Random.cname(),
      acceptanceHandlerPhone: Mock.Random.integer(13000000000, 18999999999).toString(),
      plaintiffs: generatePartyData(Mock.Random.integer(1, 3)),
      defendants: generatePartyData(Mock.Random.integer(1, 2)),
      thirdParties: generatePartyData(Mock.Random.integer(0, 1)),
      attachments: ['案件材料.pdf', '证据材料.pdf'],
      createTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    })
  }
  return list
}

// 生成100条测试数据
const caseList: any[] = generateCaseData(100)

export default [
  // 获取案件列表
  {
    url: '/admin-api/law/case-info/page',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        caseName,
        caseType,
        caseStatus,
        litigationPosition
      } = query

      let filteredList = [...caseList]

      // 根据案件名称筛选
      if (caseName) {
        filteredList = filteredList.filter((item: any) => item.caseName.includes(caseName))
      }

      // 根据案件类型筛选
      if (caseType) {
        filteredList = filteredList.filter((item: any) => item.caseType.includes(caseType))
      }

      // 根据案件状态筛选
      if (caseStatus) {
        filteredList = filteredList.filter((item: any) => item.caseStatus === caseStatus)
      }

      // 根据诉讼地位筛选
      if (litigationPosition) {
        filteredList = filteredList.filter(
          (item: any) => item.litigationPosition === litigationPosition
        )
      }

      // 分页处理
      const start = (pageNum - 1) * pageSize
      const end = start + parseInt(pageSize)
      const pageData = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list: pageData,
          total: filteredList.length
        },
        msg: '查询成功'
      }
    }
  },

  // 获取案件详情
  {
    url: '/admin-api//law/case-info/get-detail',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const caseItem = caseList.find((item: any) => item.id == id)
      if (caseItem) {
        return {
          code: SUCCESS_CODE,
          data: caseItem,
          msg: '查询成功'
        }
      }
      return {
        code: 500,
        data: null,
        msg: '案件不存在'
      }
    }
  },

  // 删除案件
  {
    url: '/admin-api/law/case-info/delete',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const index = caseList.findIndex((item: any) => item.id == id)
      if (index > -1) {
        caseList.splice(index, 1)
        return {
          code: SUCCESS_CODE,
          data: null,
          msg: '删除成功'
        }
      }
      return {
        code: 1002006003,
        data: null,
        msg: '案件不存在'
      }
    }
  },

  // 新增案件
  {
    url: '/admin-api//law/case-info/create',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const newCase = {
        id: caseList.length + 1,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        ...body
      }
      caseList.push(newCase)
      return {
        code: SUCCESS_CODE,
        data: newCase,
        msg: '新增成功'
      }
    }
  },

  // 编辑案件
  {
    url: '/admin-api/api/case/update',
    method: 'put',
    timeout,
    response: ({ body }) => {
      const { id } = body
      const index = caseList.findIndex((item: any) => item.id == id)
      if (index > -1) {
        caseList[index] = {
          ...caseList[index],
          ...body,
          updateTime: new Date().toISOString()
        }
        return {
          code: SUCCESS_CODE,
          data: caseList[index],
          msg: '更新成功'
        }
      }
      return {
        code: 1002006003,
        data: null,
        msg: '案件不存在'
      }
    }
  },
  {
    url: '/admin-api/api/case/approval-process',
    method: 'get',
    timeout,
    response: ({ body }) => {
      return {
        code: SUCCESS_CODE,
        data: {
          data: [
            {
              approvalTime: '2023-10-01 10:00:00',
              approverName: '张律师',
              approvalResult: '同意',
              approvalComment: '案件符合审批要求，已通过审批'
            },
            {
              approvalTime: '2023-10-02 11:00:00',
              approverName: '李律师',
              approvalResult: '拒绝',
              approvalComment: '案件存在争议，需要进一步核实'
            }
          ],

          info: {
            time: '2023-10-01 10:00:00',
            name: '藏海 ',
            event: '新建'
          }
        },
        msg: '更新成功'
      }
    }
  },
  {
    url: '/admin-api/api/case/action-log',
    method: 'get',
    timeout,
    response: ({ body }) => {
      return {
        code: SUCCESS_CODE,
        data: [
          {
            time: '2023-10-01 10:00:00',
            actionName: '张律师',
            action: '审批通过'
          },
          {
            time: '2023-10-01 10:00:00',
            actionName: '张律师',
            action: '审批通过'
          },
          {
            time: '2023-10-01 10:00:00',
            actionName: '张律师',
            action: '审批通过'
          },
          {
            time: '2023-10-01 10:00:00',
            actionName: '张律师',
            action: '审批通过'
          },
          {
            time: '2023-10-01 10:00:00',
            actionName: '张律师',
            action: '审批通过'
          }
        ],
        msg: '更新成功'
      }
    }
  },
  //审批案件
  {
    url: '/admin-api/api/case/approve',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const { id, action, comment } = body
      const caseItem = caseList.find((item: any) => item.id == id)
      if (caseItem) {
        return {
          code: SUCCESS_CODE,
          data: null,
          msg: `案件已${action}，备注：${comment}`
        }
      }
      return {
        code: 1002006003,
        data: null,
        msg: '案件不存在'
      }
    }
  }
]
