import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { dateFormatter } from '@/utils/formatTime'
import { createSimpleLinkage, defaultOptionMapper } from '@/utils/formLinkage'
import { getTypeManagListApi } from '@/api/ICM/matrix/typeManag'
/**
 * 内控流程管理 - 统一Schema配置
 */
export const flowSchemas: CrudSchema[] = [
  // ==================== 基础信息组 ====================
  {
    field: 'companyId',
    label: '所属企业',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      linkage: createSimpleLinkage('companyId', 'processTypeId', async (companyId) => {
        const res = await getTypeManagListApi({
          pageNo: 1,
          pageSize: 10,
          companyId: companyId
        })
        return res.list.map((item) => ({
          label: item.processTypeName,
          value: item.id
        }))
      }),

      componentProps: {
        style: { width: '200px' },
        placeholder: '请选择所属企业'
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'DeptTreeSelect',
      linkage: createSimpleLinkage('companyId', 'processTypeId', async (companyId) => {
        const res = await getTypeManagListApi({
          pageNo: 1,
          pageSize: 10,
          companyId: companyId
        })
        return res.list.map((item) => ({
          label: item.processTypeName,
          value: item.id
        }))
      }),
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择所属企业',
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'processTypeId',
    label: '流程类型',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择流程类型',
        options: [],
        style: { width: '200px' }
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择流程类型',
        'no-data-text': '未选择所属企业，或者该企业下没有流程类型',
        options: [],
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'processName',
    label: '流程名称',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入流程名称'
      }
    },
    table: {
      width: 150
    },
    form: {
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入流程名称',
        style: { width: '100%' },
        maxlength: 100,
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'nodeCount',
    label: '内控节点数量',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 120
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'scope',
    label: '适用范围',
    isSearch: false,
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 150
    },
    form: {
      component: 'DeptTreeSelect',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择适用范围',
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },

  {
    field: 'description',
    label: '相关描述',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 200
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'createTime',
    label: '最近更新时间',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      formatter: dateFormatter
    },
    detail: {
      span: 1,
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'description',
    label: '流程描述',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入流程描述',
        type: 'textarea',
        rows: 3,
        maxlength: 500,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  // ==================== 时间信息组 ====================
  // {
  //   field: 'createTime',
  //   label: '创建时间',
  //   isSearch: false,
  //   isTable: false,
  //   isForm: false,
  //   isDetail: true,
  //   detail: {
  //     span: 1,
  //     dateFormat: 'YYYY-MM-DD HH:mm:ss'
  //   }
  // },
  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {
      width: 200,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于详情页面分组显示）
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return flowSchemas.filter((schema) =>
    [
      'companyId',
      'processTypeId',
      'processName',
      'scope',
      'description',
      'nodeCount',
      'relatedDescription',
      'createTime',
      'updateTime'
    ].includes(schema.field)
  )
}

/**
 * 获取表单验证规则
 */
export const getFlowFormRules = () => {
  return {
    companyId: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
    processTypeId: [{ required: true, message: '请选择流程类型', trigger: 'change' }],
    processName: [
      { required: true, message: '请输入流程名称', trigger: 'blur' }
      // { min: 2, max: 100, message: '流程名称长度为 2 到 100 个字符', trigger: 'blur' }
    ],
    scope: [{ required: true, message: '请选择适用范围', trigger: 'change' }],
    description: [
      { required: true, message: '请输入流程描述', trigger: 'blur' }
      // { min: 10, max: 500, message: '流程描述长度为 10 到 500 个字符', trigger: 'blur' }
    ],
    nodes: [
      { required: true, message: '请至少添加一个流程节点', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (!value || value.length === 0) {
            callback(new Error('请至少添加一个流程节点'))
          } else {
            // 检查每个节点是否完整
            const invalidNodes = value.filter((node) => !node.name || !node.description)
            if (invalidNodes.length > 0) {
              callback(new Error('请完善所有节点的名称和描述'))
            } else {
              callback()
            }
          }
        },
        trigger: 'change'
      }
    ]
  }
}
