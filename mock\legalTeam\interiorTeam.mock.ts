import { MockMethod } from 'vite-plugin-mock'

// 内部队伍数据类型
interface InteriorTeamItem {
  id?: number
  name: string
  organization: string
  birthDate: string
  gender: string
  nation: string
  position: string
  workType: string
  specialty: string
  politicalStatus: string
  workStatus: string
  contact: string
  practiceYear: string
  remark?: string
  attachments?: string[]
  createTime?: string
  updateTime?: string
}

// 模拟数据
const mockData: InteriorTeamItem[] = [
  {
    id: 1,
    name: '张三',
    organization: '法务部',
    birthDate: '1985-06',
    gender: '男',
    nation: '汉族',
    position: '法务主管',
    workType: '全职',
    specialty: '民商法',
    politicalStatus: '中共党员',
    workStatus: '在岗',
    contact: '13800138001',
    practiceYear: '2010',
    remark: '擅长合同纠纷处理',
    attachments: ['证书1.pdf', '简历.doc'],
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    name: '李四',
    organization: '总经理办公室',
    birthDate: '1990-03',
    gender: '女',
    nation: '汉族',
    position: '法律顾问',
    workType: '全职',
    specialty: '劳动法',
    politicalStatus: '群众',
    workStatus: '在岗',
    contact: '13800138002',
    practiceYear: '2015',
    remark: '专业处理劳动争议',
    attachments: ['资格证书.pdf'],
    createTime: '2024-01-02 10:00:00',
    updateTime: '2024-01-02 10:00:00'
  },
  {
    id: 3,
    name: '王五',
    organization: '法务部',
    birthDate: '1982-12',
    gender: '男',
    nation: '汉族',
    position: '法务经理',
    workType: '全职',
    specialty: '知识产权法',
    politicalStatus: '中共党员',
    workStatus: '在岗',
    contact: '13800138003',
    practiceYear: '2008',
    remark: '知识产权保护专家',
    attachments: ['专利证书.pdf', '论文.doc'],
    createTime: '2024-01-03 10:00:00',
    updateTime: '2024-01-03 10:00:00'
  },
  {
    id: 4,
    name: '赵六',
    organization: '人力资源部',
    birthDate: '1988-09',
    gender: '女',
    nation: '汉族',
    position: '法务专员',
    workType: '兼职',
    specialty: '公司法',
    politicalStatus: '共青团员',
    workStatus: '离岗',
    contact: '13800138004',
    practiceYear: '2012',
    remark: '公司治理方面经验丰富',
    attachments: [],
    createTime: '2024-01-04 10:00:00',
    updateTime: '2024-01-04 10:00:00'
  },
  {
    id: 5,
    name: '孙七',
    organization: '法务部',
    birthDate: '1992-07',
    gender: '男',
    nation: '回族',
    position: '初级法务',
    workType: '全职',
    specialty: '刑法',
    politicalStatus: '群众',
    workStatus: '在岗',
    contact: '13800138005',
    practiceYear: '2018',
    remark: '刑事法律风险防控',
    attachments: ['毕业证书.pdf'],
    createTime: '2024-01-05 10:00:00',
    updateTime: '2024-01-05 10:00:00'
  }
]

export default [
  // 获取内部队伍列表
  {
    url: '/admin-api/api/interior-team/list',
    method: 'get',
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        name,
        organization,
        specialty,
        workType,
        workStatus
      } = query

      let filteredData = [...mockData]

      // 筛选条件
      if (name) {
        filteredData = filteredData.filter((item) => item.name.includes(name))
      }
      if (organization) {
        filteredData = filteredData.filter((item) => item.organization.includes(organization))
      }
      if (specialty) {
        filteredData = filteredData.filter((item) => item.specialty.includes(specialty))
      }
      if (workType) {
        filteredData = filteredData.filter((item) => item.workType === workType)
      }
      if (workStatus) {
        filteredData = filteredData.filter((item) => item.workStatus === workStatus)
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)

      return {
        code: 200,
        message: 'success',
        data: {
          list,
          total: filteredData.length
        }
      }
    }
  },

  // 获取内部队伍详情
  {
    url: '/admin-api/api/interior-team/detail',
    method: 'get',
    response: ({ query }) => {
      const { id } = query
      const item = mockData.find((item) => item.id === Number(id))

      if (item) {
        return {
          code: 200,
          message: 'success',
          data: item
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 新增内部队伍
  {
    url: '/admin-api/api/interior-team/add',
    method: 'post',
    response: ({ body }) => {
      const newItem = {
        id: Date.now(),
        ...body,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }
      mockData.push(newItem)

      return {
        code: 200,
        message: '新增成功',
        data: newItem
      }
    }
  },

  // 编辑内部队伍
  {
    url: '/admin-api/api/interior-team/update',
    method: 'put',
    response: ({ body }) => {
      const { id } = body
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData[index] = {
          ...mockData[index],
          ...body,
          updateTime: new Date().toLocaleString()
        }

        return {
          code: 200,
          message: '更新成功',
          data: mockData[index]
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 删除内部队伍
  {
    url: '/admin-api/api/interior-team/delete',
    method: 'delete',
    response: ({ query }) => {
      const { id } = query
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData.splice(index, 1)
        return {
          code: 200,
          message: '删除成功'
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 获取内部队伍操作日志
  {
    url: '/admin-api/api/interior-team/action-log',
    method: 'get',
    response: ({ query }) => {
      const { id } = query

      // 模拟操作日志数据
      const actionLogs = [
        {
          id: 1,
          time: '2024-01-15 14:30:00',
          actionName: '管理员',
          action: '创建记录',
          remark: '初始创建内部队伍成员信息'
        },
        {
          id: 2,
          time: '2024-01-20 09:15:00',
          actionName: '张三',
          action: '编辑信息',
          remark: '更新联系方式和专业领域'
        },
        {
          id: 3,
          time: '2024-02-01 16:45:00',
          actionName: '李四',
          action: '状态变更',
          remark: '工作状态从在岗变更为离岗'
        },
        {
          id: 4,
          time: '2024-02-15 11:20:00',
          actionName: '王五',
          action: '附件上传',
          remark: '上传了新的资格证书'
        },
        {
          id: 5,
          time: '2024-03-01 13:10:00',
          actionName: '赵六',
          action: '信息审核',
          remark: '审核通过个人信息变更申请'
        }
      ]

      return {
        code: 200,
        message: 'success',
        data: actionLogs
      }
    }
  }
] as MockMethod[]
