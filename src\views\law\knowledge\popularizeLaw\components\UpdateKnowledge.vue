<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getPopularLawKnowledgeDetailApi,
  addPopularLawKnowledgeApi,
  updatePopularLawKnowledgeApi
} from '@/api/law/knowledge/popularLaw'
import { knowledgeSchemas, getKnowledgeFormRules } from '../schemas/knowledgeSchemas'
import { useMessage } from '@/hooks/web/useMessage'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle, layoutSize, isCollapsed } = useLayout()
// 使用 CRUD Schemas 生成表单配置
const { allSchemas } = useCrudSchemas(knowledgeSchemas)

const message = useMessage()

// 发送事件
const emit = defineEmits(['save', 'close'])

// 响应式数据
const visible = ref(false)
const mode = ref('add') // add 或 edit
const knowledgeId = ref(null)

// 标题计算属性
const title = computed(() => {
  return mode.value === 'add' ? '新增知识' : '编辑知识'
})

// 表单验证规则
const formRules = reactive(getKnowledgeFormRules())

// 使用表单Hook
const { register, methods } = useForm()

// // 设置表单数据
// const setValue = async () => {
//   if (mode.value === 'edit' && knowledgeId.value) {
//     try {
//       const res = await getPopularLawKnowledgeDetailApi(knowledgeId.value)
//       console.log(res)

//       // if (res.code === 200) {
//       const formData = {}
//       allSchemas.formSchema.forEach((item) => {
//         formData[item.field] = res[item.field]
//       })
//       methods.setValues(formData)
//       // }
//     } catch (error) {
//       console.error('加载知识详情失败:', error)
//       message.error('加载知识详情失败')
//     }
//   }
// }

// 打开弹窗
const open = async (type, data) => {
  mode.value = type
  knowledgeId.value = data.id
  visible.value = true

  await nextTick()

  if (type === 'edit' && data.id) {
    const formData = {}
    allSchemas.formSchema.forEach((item) => {
      formData[item.field] = data[item.field]
    })
    methods.setValues(formData)
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  knowledgeId.value = null
  emit('close')
}

// 保存
const handleSave = async () => {
  try {
    const valid = await methods.validate()
    if (!valid) return

    const formData = await methods.getFormData()

    if (mode.value === 'add') {
      await addPopularLawKnowledgeApi(formData)
      message.success('新增成功')
    } else {
      await updatePopularLawKnowledgeApi(knowledgeId.value, formData)
      message.success('编辑成功')
    }

    handleClose()
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  }
}

// 对外暴露方法
defineExpose({
  open
})
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            showSave
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSave"
          />
        </div>
      </div>
      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
          <div class="mt-12px">
            <Form :schema="allSchemas.formSchema" @register="register" isCol :rules="formRules" />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
.bg-\#f1f2fe {
  background-color: #f1f2fe;
}

.lh-40px {
  line-height: 40px;
}

.pl-5 {
  padding-left: 20px;
}

.mt-12px {
  margin-top: 12px;
}
</style>
