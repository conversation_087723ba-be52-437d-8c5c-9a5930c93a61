<template>
  <div class="external-law-firm-list">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="allSchemas.searchSchema"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
        />
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button type="primary" @click="handleExport">导出</Button>
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="allSchemas.tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <!-- 专业领域列 -->
        <template #specialties="{ row }">
          <div class="flex flex-wrap gap-1">
            <el-tag
              v-for="(specialty, index) in row.specialties.slice(0, 3)"
              :key="index"
              size="small"
              type="info"
            >
              {{ specialty }}
            </el-tag>
            <el-tag v-if="row.specialties.length > 3" size="small" type="warning">
              +{{ row.specialties.length - 3 }}
            </el-tag>
          </div>
        </template>

        <!-- 企业状态列 -->
        <template #status="{ row }">
          <el-tag :type="row.status === '存续' ? 'success' : 'danger'" size="small">
            {{ row.status }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleView(row)">查看详情</Button>
          <Button link type="primary" @click="handleEdit(row)">编辑</Button>
          <Button link type="danger" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>
    </ContentWrap>

    <!-- 新增/编辑弹窗 -->
    <UpdateLawFirm ref="updateRef" @close="handleUpdateClose" @save="handleSaveSuccess" />

    <!-- 详情弹窗 -->
    <LawFirmDetail ref="detailRef" @close="handleDetailClose" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getExternalLawFirmListApi,
  deleteExternalLawFirmApi,
  exportExternalLawFirmApi
} from '@/api/law/legalTeam/externalLawFirm'
import { lawFirmSchemas } from './schemas/lawFirmSchemas'
import { ElMessage, ElMessageBox } from 'element-plus'
import UpdateLawFirm from './components/UpdateLawFirm.vue'
import LawFirmDetail from './components/LawFirmDetail.vue'
import { useMessage } from '@/hooks/web/useMessage'
const { prompt } = useMessage()
// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(lawFirmSchemas)

// 搜索组件引用
const searchRef = ref()

// 搜索参数
const searchParams = ref({})

// 使用表格hook
const {
  tableObject,
  tableMethods: { getList: getTableList, setSearchParams, exportList },
  tableRegister
} = useTable({
  getListApi: getExternalLawFirmListApi,
  immediate: true,
  exportListApi: exportExternalLawFirmApi
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 弹窗状态
const updateRef = ref()
const detailRef = ref()

// 导出
const handleExport = () => {
  prompt('请输入导出文件名', '批量导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    inputValidator: (value) => {
      if (!value) {
        return '请输入导出文件名'
      }
      return true
    }
  }).then((fileName) => {
    exportList(fileName.value, false)
  })
}

// 新建
const handleAdd = () => {
  updateRef.value.open('add')
}

// 关闭新增/编辑弹窗
const handleUpdateClose = () => {
  // 弹窗关闭逻辑
}

// 保存成功回调
const handleSaveSuccess = () => {
  tableMethods.getList()
}

// 查看详情
const handleView = (row) => {
  detailRef.value.open(row.id)
}

// 关闭详情弹窗
const handleDetailClose = () => {
  // 详情弹窗关闭逻辑
}

// 编辑
const handleEdit = (row) => {
  updateRef.value.open('edit', row.id)
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除律所"${row.name}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteExternalLawFirmApi(row.id)
      ElMessage.success('删除成功')
      getTableList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.external-law-firm-list {
  height: 100%;
}
</style>
