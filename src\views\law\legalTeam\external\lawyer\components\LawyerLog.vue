<template>
  <div class="">
    <Title title="操作日志">
      <Table
        :columns="tableColumns"
        :data="tableList"
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="280px"
        v-model:currentPage="currentPage"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
        @refresh="tableMethods.getList"
      ></Table>
    </Title>
  </div>
</template>

<script setup>
import { Back, Form, Descriptions, Title, Table } from '@/components'
import { defineProps, watch, ref, onMounted, toRefs } from 'vue'
import { getCaseActionLog } from '@/api/law/case'
import { useTable } from '@/hooks/web/useTable'
import { dateFormatter } from '@/utils/formatTime'
const props = defineProps({
  id: {
    type: [Number, String],
    required: false,
    default: undefined
  }
})

const tableColumns = [
  {
    label: '时间',
    field: 'createTime',
    formatter: dateFormatter
  },
  {
    label: '操作人',
    field: 'creatorName'
  },
  {
    label: '操作',
    field: 'operation'
  }
]

const getListApi = async () => {
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    dataId: props.id,
    type: 4
  }
  const res = await getCaseActionLog(params)
  return res
}
// 使用表格hook
const { tableRegister, tableMethods, tableObject } = useTable({
  getListApi,
  immediate: true
})
// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)
// 监听id变化
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      tableMethods.getList()
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped></style>
