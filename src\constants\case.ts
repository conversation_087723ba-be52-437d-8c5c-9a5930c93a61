/*
 * @Author: fcc
 * @Date: 2025-07-03 10:00:00
 * @LastEditors: fcc
 * @LastEditTime: 2025-07-03 10:00:00
 * @FilePath: /RiskCompliance/src/constants/case.ts
 * @Description: 案件相关常量定义
 */

import { SelectItemType } from "@/api/public/public.types"

/**
 * 案件类型枚举
 */
export enum CASE_TYPE_ENUM {
  CIVIL = '民事案件',
  CRIMINAL = '刑事案件',
  ADMINISTRATIVE = '行政案件'
}

/**
 * 案件类型选项列表
 */
export const CASE_TYPE_LIST: SelectItemType[] = [
  { label: '民事案件', value: CASE_TYPE_ENUM.CIVIL },
  { label: '刑事案件', value: CASE_TYPE_ENUM.CRIMINAL },
  { label: '行政案件', value: CASE_TYPE_ENUM.ADMINISTRATIVE }
]

/**
 * 民事案件程序枚举
 */
export enum CIVIL_PROCEDURE_ENUM {
  ARBITRATION = '仲裁',
  FIRST_TRIAL = '一审',
  SECOND_TRIAL = '二审',
  RETRIAL_FIRST = '重审-一审',
  RETRIAL_SECOND = '重审-二审',
  APPEAL_FIRST = '再审-一审',
  APPEAL_SECOND = '再审-二审',
  EXECUTION = '执行'
}

/**
 * 民事案件程序选项列表
 */
export const CIVIL_PROCEDURE_LIST: SelectItemType[] = [
  { label: '仲裁', value: CIVIL_PROCEDURE_ENUM.ARBITRATION },
  { label: '一审', value: CIVIL_PROCEDURE_ENUM.FIRST_TRIAL },
  { label: '二审', value: CIVIL_PROCEDURE_ENUM.SECOND_TRIAL },
  { label: '重审-一审', value: CIVIL_PROCEDURE_ENUM.RETRIAL_FIRST },
  { label: '重审-二审', value: CIVIL_PROCEDURE_ENUM.RETRIAL_SECOND },
  { label: '再审-一审', value: CIVIL_PROCEDURE_ENUM.APPEAL_FIRST },
  { label: '再审-二审', value: CIVIL_PROCEDURE_ENUM.APPEAL_SECOND },
  { label: '执行', value: CIVIL_PROCEDURE_ENUM.EXECUTION }
]

/**
 * 刑事案件程序枚举
 */
export enum CRIMINAL_PROCEDURE_ENUM {
  INVESTIGATION = '侦察',
  PROSECUTION = '起诉',
  FIRST_TRIAL = '一审',
  SECOND_TRIAL = '二审',
  RETRIAL_FIRST = '重审-一审',
  RETRIAL_SECOND = '重审-二审',
  APPEAL_FIRST = '再审-一审',
  APPEAL_SECOND = '再审-二审',
  EXECUTION = '执行'
}

/**
 * 刑事案件程序选项列表
 */
export const CRIMINAL_PROCEDURE_LIST: SelectItemType[] = [
  { label: '侦察', value: CRIMINAL_PROCEDURE_ENUM.INVESTIGATION },
  { label: '起诉', value: CRIMINAL_PROCEDURE_ENUM.PROSECUTION },
  { label: '一审', value: CRIMINAL_PROCEDURE_ENUM.FIRST_TRIAL },
  { label: '二审', value: CRIMINAL_PROCEDURE_ENUM.SECOND_TRIAL },
  { label: '重审-一审', value: CRIMINAL_PROCEDURE_ENUM.RETRIAL_FIRST },
  { label: '重审-二审', value: CRIMINAL_PROCEDURE_ENUM.RETRIAL_SECOND },
  { label: '再审-一审', value: CRIMINAL_PROCEDURE_ENUM.APPEAL_FIRST },
  { label: '再审-二审', value: CRIMINAL_PROCEDURE_ENUM.APPEAL_SECOND },
  { label: '执行', value: CRIMINAL_PROCEDURE_ENUM.EXECUTION }
]

/**
 * 行政案件程序枚举
 */
export enum ADMINISTRATIVE_PROCEDURE_ENUM {
  ADMINISTRATIVE_REVIEW = '行政复议',
  FIRST_TRIAL = '一审',
  SECOND_TRIAL = '二审',
  RETRIAL_FIRST = '重审-一审',
  RETRIAL_SECOND = '重审-二审',
  APPEAL_FIRST = '再审-一审',
  APPEAL_SECOND = '再审-二审',
  EXECUTION = '执行'
}

/**
 * 行政案件程序选项列表
 */
export const ADMINISTRATIVE_PROCEDURE_LIST: SelectItemType[] = [
  { label: '行政复议', value: ADMINISTRATIVE_PROCEDURE_ENUM.ADMINISTRATIVE_REVIEW },
  { label: '一审', value: ADMINISTRATIVE_PROCEDURE_ENUM.FIRST_TRIAL },
  { label: '二审', value: ADMINISTRATIVE_PROCEDURE_ENUM.SECOND_TRIAL },
  { label: '重审-一审', value: ADMINISTRATIVE_PROCEDURE_ENUM.RETRIAL_FIRST },
  { label: '重审-二审', value: ADMINISTRATIVE_PROCEDURE_ENUM.RETRIAL_SECOND },
  { label: '再审-一审', value: ADMINISTRATIVE_PROCEDURE_ENUM.APPEAL_FIRST },
  { label: '再审-二审', value: ADMINISTRATIVE_PROCEDURE_ENUM.APPEAL_SECOND },
  { label: '执行', value: ADMINISTRATIVE_PROCEDURE_ENUM.EXECUTION }
]

/**
 * 案件程序选项映射
 */
export const CASE_PROCEDURE_OPTIONS = {
  [CASE_TYPE_ENUM.CIVIL]: CIVIL_PROCEDURE_LIST,
  [CASE_TYPE_ENUM.CRIMINAL]: CRIMINAL_PROCEDURE_LIST,
  [CASE_TYPE_ENUM.ADMINISTRATIVE]: ADMINISTRATIVE_PROCEDURE_LIST
}

/**
 * 是否类型枚举（用于是否重大、是否涉外等）
 */
export enum YES_NO_ENUM {
  YES = '是',
  NO = '否'
}

/**
 * 是否选项列表
 */
export const YES_NO_LIST: SelectItemType[] = [
  { label: '是', value: YES_NO_ENUM.YES },
  { label: '否', value: YES_NO_ENUM.NO }
]

/**
 * 诉讼地位枚举
 */
export enum LITIGATION_POSITION_ENUM {
  PLAINTIFF = '原告/申请人',
  DEFENDANT = '被告/被申请人',
  THIRD_PARTY = '第三人'
}

/**
 * 诉讼地位选项列表
 */
export const LITIGATION_POSITION_LIST: SelectItemType[] = [
  { label: '原告/申请人', value: LITIGATION_POSITION_ENUM.PLAINTIFF },
  { label: '被告/被申请人', value: LITIGATION_POSITION_ENUM.DEFENDANT },
  { label: '第三人', value: LITIGATION_POSITION_ENUM.THIRD_PARTY }
]

/**
 * 律师枚举（示例数据，实际应从API获取）
 */
export enum LAWYER_ENUM {
  ZHANG_SAN = '张三',
  LI_SI = '李四'
}

/**
 * 律师选项列表（示例数据，实际应从API获取）
 */
export const LAWYER_LIST: SelectItemType[] = [
  { label: '张三', value: LAWYER_ENUM.ZHANG_SAN },
  { label: '李四', value: LAWYER_ENUM.LI_SI }
]

/**
 * 案件状态枚举
 */
export enum CASE_STATUS_ENUM {
  INVESTIGATION_REVIEW = '侦查中/行政复议中',
  FILED = '已立案',
  IN_TRIAL = '审理中',
  CLOSED = '已结案',
  ARCHIVED = '已归档'
}

/**
 * 案件状态选项列表
 */
export const CASE_STATUS_LIST: SelectItemType[] = [
  { label: '侦查中/行政复议中', value: CASE_STATUS_ENUM.INVESTIGATION_REVIEW },
  { label: '已立案', value: CASE_STATUS_ENUM.FILED },
  { label: '审理中', value: CASE_STATUS_ENUM.IN_TRIAL },
  { label: '已结案', value: CASE_STATUS_ENUM.CLOSED },
  { label: '已归档', value: CASE_STATUS_ENUM.ARCHIVED }
]

/**
 * 受理机构类型枚举
 */
export enum ACCEPTANCE_AGENCY_TYPE_ENUM {
  BASIC_COURT = '基层法院',
  INTERMEDIATE_COURT = '中级法院',
  HIGH_COURT = '高级法院',
  SUPREME_COURT = '最高法院',
  ARBITRATION = '仲裁机构'
}

/**
 * 受理机构类型选项列表
 */
export const ACCEPTANCE_AGENCY_TYPE_LIST: SelectItemType[] = [
  { label: '基层法院', value: ACCEPTANCE_AGENCY_TYPE_ENUM.BASIC_COURT },
  { label: '中级法院', value: ACCEPTANCE_AGENCY_TYPE_ENUM.INTERMEDIATE_COURT },
  { label: '高级法院', value: ACCEPTANCE_AGENCY_TYPE_ENUM.HIGH_COURT },
  { label: '最高法院', value: ACCEPTANCE_AGENCY_TYPE_ENUM.SUPREME_COURT },
  { label: '仲裁机构', value: ACCEPTANCE_AGENCY_TYPE_ENUM.ARBITRATION }
]

/**
 * 当事人类型枚举
 */
export enum PARTY_TYPE_ENUM {
  PLAINTIFF = 'plaintiff',
  DEFENDANT = 'defendant',
  THIRD_PARTY = 'thirdParty'
}

/**
 * 当事人类型映射
 */
export const PARTY_TYPE_MAP = {
  [PARTY_TYPE_ENUM.PLAINTIFF]: '原告/申请人',
  [PARTY_TYPE_ENUM.DEFENDANT]: '被告/被申请人',
  [PARTY_TYPE_ENUM.THIRD_PARTY]: '第三人'
}

/**
 * 文件上传支持的格式
 */
export const SUPPORTED_FILE_TYPES = ['.doc', '.docx', '.pdf']

/**
 * 文件上传最大大小（10MB）
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024

/**
 * 案件编号生成前缀
 */
export const CASE_NO_PREFIX = 'AJ'

/**
 * 表单验证正则表达式
 */
export const FORM_REGEX = {
  // 中英文字符和数字
  CHINESE_ENGLISH_NUMBER: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
  // 中英文字符（不包含数字）
  CHINESE_ENGLISH: /^[\u4e00-\u9fa5a-zA-Z]+$/,
  // 数字
  NUMBER: /^\d+$/
}
