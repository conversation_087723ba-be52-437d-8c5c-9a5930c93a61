import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import * as UserApi from '@/api/system/user'
/**
 * 普法知识库管理 - 统一Schema配置
 * 通过 isSearch、isTable、isForm、isDetail 控制字段在不同场景下的显示
 */
export const knowledgeSchemas: CrudSchema[] = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    isTable: true,
    isSearch: false,
    isForm: false
  },
  // ==================== 基础信息组 ====================
  {
    field: 'knowledgeTitle',
    label: '知识标题',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入知识标题'
      }
    },
    table: {
      // width: 200
    },
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入知识标题',
        style: { width: '100%' },
        maxlength: 200,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'keywords',
    label: '关键字',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入关键词'
      }
    },
    table: {
      // width: 150
    },
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入关键词，多关键词请用英文逗号（,）隔开，此关键词可用于快速检索知识',
        style: { width: '100%' },
        maxlength: 200,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'knowledgeIntro',
    label: '知识简介',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      // width: 200
    },
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '用于在普法库中展示，用户可通过改内容快速了解知识梗概',
        type: 'textarea',
        rows: 3,
        maxlength: 500,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'belongUnitId',
    label: '所属单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请选择所属单位',
        width: 300
      }
    },
    table: {
      formatter: (row: any) => {
        return row.belongUnitName
      }
    },
    detail: {
      formatter: (field, row: any) => {
        return row.belongUnitName
      }
    },
    form: {
      component: 'DeptTreeSelect',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请选择所属单位',
        style: { width: '100%' },
        maxlength: 100,
        clearable: true
      }
    }
  },
  {
    field: 'contributorId',
    label: '知识贡献者',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      colProps: { span: 24 },
      api: () => UserApi.getSimpleUserList() as any,
      componentProps: {
        placeholder: '请选择知识贡献者',
        style: { width: '100%' },
        optionsAlias: {
          labelField: 'nickname',
          valueField: 'id'
        },
        maxlength: 50,
        clearable: true
      }
    }
  },
  // ==================== 内容信息组 ====================

  {
    field: 'knowledgeContent',
    label: '知识内容',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面单独处理展示
    form: {
      component: 'Editor',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入知识内容',
        clearable: true,
        height: '200px'
      }
    }
  },

  // ==================== 资源信息组 ====================
  {
    field: 'audioVideo',
    label: '相关音视频',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面单独处理展示
    form: {
      component: 'UploadFiles',
      colProps: { span: 24 },
      componentProps: {
        // placeholder: '请输入相关音视频链接或文件名',
        style: { width: '300px' },
        maxlength: 500,
        clearable: true
      }
    }
  },
  {
    field: 'relatedDocs',
    label: '相关文档',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面单独处理展示
    form: {
      component: 'UploadFiles',
      colProps: { span: 24 },
      componentProps: {
        // placeholder: '请输入相关文档链接或文件名',
        style: { width: '300px' },
        maxlength: 500,
        clearable: true
      }
    }
  },

  // ==================== 时间信息组 ====================
  {
    field: 'createTime',
    label: '创建时间',
    isTable: false, // 在原代码中被注释掉了
    isDetail: true,
    isForm: false,
    table: {
      width: 160
    },
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'updateTime',
    label: '更新时间',
    isTable: false,
    isDetail: true,
    isForm: false,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },

  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isTable: true,
    isForm: false,
    table: {
      width: 200,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于详情页面分组显示）
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return knowledgeSchemas.filter((schema) =>
    [
      'knowledgeTitle',
      'knowledgeKeywords',
      'knowledgeIntro',
      'contributorId',
      'belongUnitId',
      'createTime',
      'updateTime'
    ].includes(schema.field)
  )
}

/**
 * 获取表单验证规则
 */
export const getKnowledgeFormRules = () => {
  return {
    knowledgeTitle: [
      { required: true, message: '请输入知识标题', trigger: 'blur' },
      { min: 2, max: 200, message: '知识标题长度为 2 到 200 个字符', trigger: 'blur' }
    ],
    keywords: [
      { required: true, message: '请输入关键字', trigger: 'blur' },
      { max: 200, message: '关键字长度不能超过 200 个字符', trigger: 'blur' }
    ],
    knowledgeIntro: [
      { required: true, message: '请输入知识简介', trigger: 'blur' },
      { min: 10, max: 500, message: '知识简介长度为 10 到 500 个字符', trigger: 'blur' }
    ],
    knowledgeContent: [
      { required: true, message: '请输入知识内容', trigger: 'blur' },
      { min: 20, message: '知识内容长度不能少于 20 个字符', trigger: 'blur' }
    ],
    contributorId: [
      { required: true, message: '请选择知识贡献者', trigger: 'blur' }
      // { max: 50, message: '知识贡献者长度不能超过 50 个字符', trigger: 'blur' }
    ],
    belongUnitId: [{ required: true, message: '请选择所属单位', trigger: 'blur' }]
    // audioVideo: [{ max: 500, message: '相关音视频长度不能超过 500 个字符', trigger: 'blur' }],
    // relatedDocs: [{ max: 500, message: '相关文档长度不能超过 500 个字符', trigger: 'blur' }]
  }
}
