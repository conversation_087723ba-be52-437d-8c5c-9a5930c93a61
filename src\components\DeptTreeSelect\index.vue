<template>
  <el-tree-select
    ref="treeSelectRef"
    v-model="selectedValue"
    :data="deptTree"
    :props="defaultProps"
    :multiple="multiple"
    :check-strictly="checkStrictly"
    :clearable="clearable"
    :filterable="filterable"
    :placeholder="placeholder"
    :disabled="disabled"
    :loading="loading"
    node-key="id"
    :empty-text="emptyText"
    @change="handleChange"
    @clear="handleClear"
    @focus="handleFocus"
    @blur="handleBlur"
  />
</template>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'

defineOptions({ name: 'DeptTreeSelect' })

const emit = defineEmits<{
  'update:modelValue': [value: any]
  change: [value: any, node: any]
  clear: []
  focus: []
  blur: []
}>()

const props = defineProps({
  // 绑定值
  modelValue: {
    type: [String, Number, Array],
    default: undefined
  },
  // 是否严格的遵循父子不互相关联
  checkStrictly: {
    type: Boolean,
    default: false
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 是否可清空
  clearable: {
    type: Boolean,
    default: true
  },
  // 是否可筛选
  filterable: {
    type: Boolean,
    default: true
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请选择部门'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 空数据时显示的文本
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  // 是否在组件挂载时自动加载数据
  autoLoad: {
    type: Boolean,
    default: true
  }
})

const treeSelectRef = ref()
const deptTree = ref<Tree[]>([]) // 部门树形结构
const loading = ref(false) // 加载状态

// 使用 useVModel 处理双向绑定
const selectedValue = useVModel(props, 'modelValue', emit)

// 监听选中值变化，向父组件发送事件
watch(
  () => selectedValue.value,
  (newVal) => {
    emit('change', newVal, getSelectedNodes())
  }
)

/** 加载部门数据 */
const loadDeptData = async () => {
  loading.value = true
  try {
    const deptData = await DeptApi.getSimpleDeptList()
    deptTree.value = handleTree(deptData)
  } finally {
    loading.value = false
  }
}

/** 获取选中的节点数据 */
const getSelectedNodes = () => {
  if (!treeSelectRef.value) return null

  if (props.multiple) {
    return treeSelectRef.value.getCheckedNodes()
  } else {
    return treeSelectRef.value.getCurrentNode()
  }
}

/** 获取选中的节点 key */
const getSelectedKeys = () => {
  if (!treeSelectRef.value) return []

  if (props.multiple) {
    return treeSelectRef.value.getCheckedKeys()
  } else {
    const currentNode = treeSelectRef.value.getCurrentNode()
    return currentNode ? [currentNode.id] : []
  }
}

/** 设置选中的节点 */
const setSelectedKeys = (keys: number[]) => {
  if (!treeSelectRef.value) return

  if (props.multiple) {
    treeSelectRef.value.setCheckedKeys(keys)
  } else {
    selectedValue.value = keys.length > 0 ? keys[0] : undefined
  }
}

/** 清空选择 */
const clearSelection = () => {
  selectedValue.value = props.multiple ? [] : undefined
}

/** 处理选择变化 */
const handleChange = () => {
  // el-tree-select 的 change 事件会自动更新 v-model 绑定的值
  // 通过 watch 监听 selectedValue 变化来触发 change 事件
}

/** 处理清空事件 */
const handleClear = () => {
  emit('clear')
}

/** 处理获取焦点事件 */
const handleFocus = () => {
  emit('focus')
}

/** 处理失去焦点事件 */
const handleBlur = () => {
  emit('blur')
}

/** 刷新数据 */
const refresh = async () => {
  await loadDeptData()
}

// 组件挂载时自动加载数据
onMounted(async () => {
  if (props.autoLoad) {
    await loadDeptData()
  }
})

// 暴露方法供外部调用
defineExpose({
  loadDeptData,
  getSelectedNodes,
  getSelectedKeys,
  setSelectedKeys,
  clearSelection,
  refresh
})
</script>
