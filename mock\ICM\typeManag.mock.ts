import Mock from 'mockjs'
import { SUCCESS_CODE } from '../public.constant'

const timeout = 1000

// 模拟数据
const typeManagList = Mock.mock({
  'list|50': [
    {
      'id|+1': 1,
      type: () =>
        Mock.Random.pick([
          '制度文件',
          '流程文件',
          '操作指南',
          '管理办法',
          '实施细则',
          '工作规程',
          '业务规范',
          '操作手册'
        ]),
      typeCode: () => Mock.Random.string('upper', 2) + Mock.Random.string('number', 4),
      belongUnitId: () => Mock.Random.integer(1, 10),
      belongUnitName: () =>
        Mock.Random.pick([
          '总行',
          '风险管理部',
          '合规部',
          '法律事务部',
          '运营管理部',
          '信息科技部',
          '人力资源部',
          '财务会计部'
        ]),
      'status|1': [0, 1],
      description: () => Mock.Random.cparagraph(1, 3),
      createTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    }
  ]
}).list

export default [
  // 获取内控文件类型分页列表
  {
    url: '/admin-api/api/ICM/file/typeManag/page',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, type, belongUnitId, status } = query

      let filteredList = [...typeManagList]

      // 筛选条件
      if (type) {
        filteredList = filteredList.filter((item) => item.type.includes(type))
      }

      if (belongUnitId) {
        filteredList = filteredList.filter((item) => item.belongUnitId === Number(belongUnitId))
      }

      if (status !== undefined && status !== '') {
        filteredList = filteredList.filter((item) => item.status === Number(status))
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + Number(pageSize)
      const list = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list,
          total: filteredList.length
        }
      }
    }
  },

  // 获取内控文件类型详情
  {
    url: '/admin-api/api/ICM/file/typeManag/get',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const item = typeManagList.find((item) => item.id === Number(id))

      return {
        code: SUCCESS_CODE,
        data: item || null
      }
    }
  },

  // 创建内控文件类型
  {
    url: '/admin-api/api/ICM/file/typeManag/create',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const newItem = {
        ...body,
        id: typeManagList.length + 1,
        createTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
        updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
      }

      // 根据belongUnitId设置belongUnitName
      const unitNames = {
        1: '总行',
        2: '风险管理部',
        3: '合规部',
        4: '法律事务部',
        5: '运营管理部',
        6: '信息科技部',
        7: '人力资源部',
        8: '财务会计部'
      }
      newItem.belongUnitName = unitNames[newItem.belongUnitId] || '其他部门'

      typeManagList.push(newItem)

      return {
        code: SUCCESS_CODE,
        data: newItem.id
      }
    }
  },

  // 更新内控文件类型
  {
    url: '/admin-api/api/ICM/file/typeManag/update',
    method: 'put',
    timeout,
    response: ({ body }) => {
      const index = typeManagList.findIndex((item) => item.id === body.id)

      if (index !== -1) {
        // 根据belongUnitId设置belongUnitName
        const unitNames = {
          1: '总行',
          2: '风险管理部',
          3: '合规部',
          4: '法律事务部',
          5: '运营管理部',
          6: '信息科技部',
          7: '人力资源部',
          8: '财务会计部'
        }
        body.belongUnitName = unitNames[body.belongUnitId] || '其他部门'
        body.updateTime = new Date().toISOString().replace('T', ' ').substr(0, 19)

        typeManagList[index] = { ...typeManagList[index], ...body }

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '更新失败，记录不存在'
      }
    }
  },

  // 删除内控文件类型
  {
    url: '/admin-api/api/ICM/file/typeManag/delete',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const index = typeManagList.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        typeManagList.splice(index, 1)

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '删除失败，记录不存在'
      }
    }
  },

  // 导出内控文件类型
  {
    url: '/admin-api/api/ICM/file/typeManag/export',
    method: 'get',
    timeout,
    response: ({ query }) => {
      // 这里应该返回文件流，mock中简单返回成功
      return {
        code: SUCCESS_CODE,
        data: '导出成功'
      }
    }
  }
]
