import request from '@/config/axios'

// 内控文件管理接口

/**
 * 附件数据结构
 */
export interface AttachmentVO {
  id?: number
  fileName: string // 文件名称
  fileUrl: string // 文件URL
  fileSize: number // 文件大小
  uploadTime: string // 上传时间
  uploader: string // 上传人
  version: string // 版本号
  isActive: boolean // 是否启用
}

/**
 * 内控文件数据结构
 */
export interface ICMFileVO {
  id?: number
  title: string // 内控文件标题
  fileNo: string // 发文号
  department: string // 发文部门
  fileType: string // 文件类型
  belongCompany: string // 所属企业
  issueDate: string // 文件印发时间
  expireDate: string // 文件失效时间
  description: string // 文件描述
  status: number // 文件状态（0-草稿，1-生效，2-失效）
  attachments?: AttachmentVO[] // 相关附件
  version: string // 版本号
  currentVersion: boolean // 是否当前版本
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

/**
 * 查询参数
 */
export interface ICMFilePageReqVO {
  pageNum: number
  pageSize: number
  title?: string // 内控文件标题
  fileNo?: string // 发文号
  department?: string // 发文部门
  status?: number // 文件状态
  fileType?: string // 文件类型
}

/**
 * 获取内控文件分页列表
 */
export const getICMFileListApi = (params: ICMFilePageReqVO) => {
  return request.get({
    url: '/api/ICM/file/page',
    params
  })
}

/**
 * 获取内控文件详情
 */
export const getICMFileDetailApi = (id: number) => {
  return request.get({
    url: `/api/ICM/file/get`,
    params: { id }
  })
}

/**
 * 创建内控文件
 */
export const createICMFileApi = (data: ICMFileVO) => {
  return request.post({
    url: '/api/ICM/file/create',
    data
  })
}

/**
 * 更新内控文件
 */
export const updateICMFileApi = (data: ICMFileVO) => {
  return request.put({
    url: '/api/ICM/file/update',
    data
  })
}

/**
 * 删除内控文件
 */
export const deleteICMFileApi = (id: number) => {
  return request.delete({
    url: `/api/ICM/file/delete`,
    params: { id }
  })
}

/**
 * 导出内控文件
 */
export const exportICMFileApi = (params: ICMFilePageReqVO) => {
  return request.download({
    url: '/api/ICM/file/export',
    params
  })
}
