<template>
  <ContentWrap>
    <div class="flex items-start">
      <Search
        ref="searchRef"
        :schema="allSchemas.searchSchema"
        :show-search="true"
        :show-reset="true"
        :expand="false"
        layout="inline"
        @search="handleSearch"
        @reset="handleReset"
        :model="searchParams"
      />
      <Button type="primary" @click="handleAdd">新增+</Button>
      <Button type="primary" @click="handleExport">导出</Button>
    </div>
  </ContentWrap>

  <!-- 表格区域 -->
  <ContentWrap>
    <Table
      ref="tableRef"
      v-model:pageSize="pageSize"
      height="calc(70vh)"
      maxHeight="calc(70vh)"
      v-model:currentPage="currentPage"
      :columns="allSchemas.tableColumns"
      :data="tableList"
      :loading="loading"
      :pagination="{
        total: total
      }"
      @register="register"
      @refresh="tableMethods.getList"
    >
      <!-- 状态列 -->
      <!-- <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template> -->

      <!-- 操作列 -->
      <template #action="{ row }">
        <Button link type="primary" @click="handleConfig(row)">配置</Button>
        <Button link type="success" @click="handleConfigComplete(row)">配置完成</Button>
        <Button link type="info" @click="handleViewProgress(row)">查看进度</Button>
      </template>
    </Table>
  </ContentWrap>

  <!-- 任务配置弹窗 -->
  <TaskConfigDetail ref="taskConfigRef" @close="handleConfigClose" @save="handleConfigSave" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getEvaluationPlanListApi,
  deleteEvaluationPlanApi,
  exportEvaluationPlanApi
} from '@/api/ICM/evaluate'
import { taskSchemas } from './schemas/taskSchemas'
import { ElMessage, ElMessageBox } from 'element-plus'
import TaskConfigDetail from './detail/index.vue'

defineOptions({ name: 'ICMEvaluatePlan' })

// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(taskSchemas)

// 搜索组件引用
const searchRef = ref()

// 搜索参数
const searchParams = ref({})

// 获取列表数据的API函数
const getListApi = async (params: any) => {
  const requestParams = {
    pageNum: params.pageNo,
    pageSize: params.pageSize,
    ...searchParams.value
  }

  const res = await getEvaluationPlanListApi(requestParams)
  return res
}

// 使用表格hook
const { register, tableMethods, tableObject } = useTable({
  getListApi
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 搜索
const handleSearch = (searchData: any) => {
  searchParams.value = { ...searchData }
  tableMethods.getList()
}

// 重置
const handleReset = () => {
  searchParams.value = {}
  tableMethods.getList()
}

// 弹窗状态
const taskConfigRef = ref()

// 获取状态类型
// const getStatusType = (status: number) => {
//   return PLAN_STATUS_COLOR_MAP[status] || 'info'
// }

// // 获取状态文本
// const getStatusText = (status: number) => {
//   return PLAN_STATUS_MAP[status] || '未知'
// }

// 导出
const handleExport = async () => {
  try {
    const params = {
      ...searchParams.value
    }

    await exportEvaluationPlanApi(params)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 新建
const handleAdd = () => {
  ElMessage.info('新增功能开发中...')
}

// 关闭配置弹窗
const handleConfigClose = () => {
  // 弹窗关闭逻辑
}

// 配置保存成功
const handleConfigSave = () => {
  tableMethods.getList()
  ElMessage.success('配置保存成功')
}

// 保存成功
const handleSaveSuccess = () => {
  // 刷新列表
  tableMethods.getList()
}

// 配置任务
const handleConfig = (row: any) => {
  taskConfigRef.value?.open('config', row.id)
}

// 配置完成
const handleConfigComplete = (row: any) => {
  ElMessageBox.confirm(`确定要标记任务"${row.controlFunctionName}"为配置完成吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 调用配置完成API
      console.log('配置完成:', row)
      ElMessage.success('配置完成')
      tableMethods.getList()
    })
    .catch(() => {
      ElMessage.info('已取消操作')
    })
}

// 查看进度
const handleViewProgress = (row: any) => {
  // 查看任务进度
  console.log('查看进度:', row)
  ElMessage.info('查看进度功能开发中...')
}

onMounted(() => {
  tableMethods.getList()
})
</script>

<style scoped lang="scss"></style>
