<script setup lang="tsx">
import { computed, defineExpose, nextTick } from 'vue'
import { Back } from '@/components'
import InteriorTeamInfo from './InteriorTeamInfo.vue'
import InteriorTeamLog from './InteriorTeamLog.vue'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle, layoutSize, isCollapsed } = useLayout()
import { LogList } from '@/components/LogList'
const emit = defineEmits(['close'])

const visible = ref(false)
const editId = ref<number | null>(null)
const activeTab = ref('基本信息')

const title = computed(() => '查看详情')

// Tab列表配置
const tabList = [
  {
    name: '基本信息',
    component: InteriorTeamInfo
  },
  {
    name: '操作日志',
    component: LogList
  }
]

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  editId.value = null
  activeTab.value = '基本信息'
  emit('close')
}

// 打开弹窗
const open = async (id: number) => {
  visible.value = true
  editId.value = id
  activeTab.value = '基本信息'
  await nextTick()
}

defineExpose({
  open
})
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            :showSave="false"
            @back="handleClose"
            @cancel="handleClose"
          />
        </div>
      </div>

      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <ElTabs type="border-card" v-model="activeTab">
          <ElTabPane v-for="item in tabList" :key="item.name" :label="item.name" :name="item.name">
            <component :is="item.component" :id="editId!" v-if="editId" :type="4" />
          </ElTabPane>
        </ElTabs>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
