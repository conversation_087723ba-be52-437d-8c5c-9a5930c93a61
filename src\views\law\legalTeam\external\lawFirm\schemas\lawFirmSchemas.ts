import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { DICT_TYPE } from '@/utils/dict'
/**
 * 外聘律所管理 - 统一Schema配置
 * 通过 isSearch、isTable、isForm、isDetail 控制字段在不同场景下的显示
 */

// 专业领域选项
export const lawFirmSchemas: CrudSchema[] = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    isTable: true,
    isSearch: false,
    isForm: false
  },
  // ==================== 基础信息组 ====================
  {
    field: 'firmName',
    label: '律所名称',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入律所名称'
      }
    },
    table: {},
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'creditCode',
    label: '企业信用代码',
    isTable: false,
    isForm: true,
    isDetail: true,
    table: {},
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 18,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'address',
    label: '律所地址',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {},
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 200,
        clearable: true
      },
      colProps: { span: 12 }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'firmStatus',
    label: '企业状态',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.FIRM_STATUS,
    table: {
      width: 100
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'professionalFields',
    label: '专业领域',
    isTable: true,
    isForm: true,
    isDetail: false, // 详情页面特殊处理
    table: {
      width: 450
    },
    dictType: DICT_TYPE.PROFESSIONAL_FIELDS,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        multiple: true,
        clearable: true,
        collapseTags: true,
        maxCollapseTags: 2
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'legalRepresentative',
    label: '创建人（法定代表人）',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'contactPerson',
    label: '联系人',
    isTable: false,
    isForm: true,
    isDetail: true,
    table: {
      width: 100
    },
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'contactPhone',
    label: '联系方式',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 120
    },
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'belongUnitId',
    label: '所属单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请输入所属单位'
      }
    },
    table: {
      width: 120,
      formatter: (row: any) => {
        return row.belongUnitName
      }
    },
    detail: {
      formatter: (key, row: any) => {
        return row.belongUnitName
      }
    },
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'remark',
    label: '备注',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 4,
        maxlength: 500,
        clearable: true
      },
      colProps: { span: 24 }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'attachment',
    label: '附件',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面特殊处理
    form: {
      component: 'UploadFiles',
      componentProps: {},
      colProps: { span: 24 }
    }
  },

  // ==================== 时间信息组 ====================
  {
    field: 'createTime',
    label: '创建时间',
    isTable: false,
    isDetail: true,
    isForm: false,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'updateTime',
    label: '更新时间',
    isTable: false,
    isDetail: true,
    isForm: false,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },

  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isTable: true,
    isForm: false,
    table: {
      width: 240,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于详情页面分组显示）
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return lawFirmSchemas.filter((schema) =>
    [
      'firmName',
      'creditCode',
      'address',
      'firmStatus',
      'professionalFields',
      'contactPerson',
      'contactPhone',
      'belongUnitId',
      'remark',
      'createTime',
      'updateTime'
    ].includes(schema.field)
  )
}

/**
 * 获取表单验证规则
 */
export const getLawFirmFormRules = () => {
  return {
    firmName: [{ required: true, message: '请输入律所名称', trigger: 'blur' }],
    creditCode: [
      { required: true, message: '请输入企业信用代码', trigger: 'blur' }
      // {
      //   pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
      //   message: '请输入正确的企业信用代码格式',
      //   trigger: 'blur'
      // }
    ],
    address: [{ required: true, message: '请输入律所地址', trigger: 'blur' }],
    firmStatus: [{ required: true, message: '请选择企业状态', trigger: 'change' }],
    professionalFields: [{ required: true, message: '请选择专业领域', trigger: 'change' }],
    legalRepresentative: [{ required: true, message: '请输入法定代表人', trigger: 'blur' }],
    contactPerson: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    contactPhone: [
      { required: true, message: '请输入联系方式', trigger: 'blur' },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码格式',
        trigger: 'blur'
      }
    ],
    belongUnitId: [{ required: true, message: '请选择所属单位', trigger: 'blur' }]
  }
}
