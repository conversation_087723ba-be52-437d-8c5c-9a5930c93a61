import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { RISK_LEVEL_LIST, RISK_TYPE_LIST } from '../constants/measureConstants'
import { DICT_TYPE } from '@/utils/dict'
import { getTypeManagListApi } from '@/api/ICM/matrix/typeManag'
import { createSimpleLinkage, defaultOptionMapper, customFieldMapper } from '@/utils/formLinkage'
import { getFlowManagementListApi, getFlowManagementNodeListApi } from '@/api/ICM/matrix/flowManag'
/**
 * 内控措施管理 - 统一Schema配置
 */
export const measureSchemas: CrudSchema[] = [
  // {
  //   "companyId": "所属企业ID",
  //   "processTypeId": "流程类型ID",
  //   "processId": "关联流程ID",
  //   "nodeId": "关联节点ID",
  //   "departmentId": "责任部门ID",
  //   "measureName": "控制措施名称",
  //   "measureLevel": "控制级别",
  //   "measureType": "控制类型",
  //   "measureDescription": "措施描述",
  //   "riskName": "风险点名称",
  //   "riskLevel": "风险等级",
  //   "riskProbability": "发生概率",
  //   "riskDescription": "风险描述",
  //   "status": "1"
  // }
  // ==================== 基础信息组 ====================
  {
    field: 'companyId',
    label: '所属企业',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请选择所属企业',
        style: { width: '200px' }
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'DeptTreeSelect',
      colProps: { span: 12 },
      linkage: createSimpleLinkage(
        'companyId',
        'processTypeId',
        async (companyId) => {
          return await getTypeManagListApi({
            pageNo: 1,
            pageSize: 10,
            companyId
          })
        },
        customFieldMapper('processTypeName', 'id')
      ),
      componentProps: {
        placeholder: '请选择所属企业',
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'processTypeId',
    label: '内控措施类型',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择措施类型',
        options: [],
        style: { width: '200px' }
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      linkage: createSimpleLinkage(
        'processTypeId',
        'processId',
        async (processTypeId) => {
          return await getFlowManagementListApi({
            pageNo: 1,
            pageSize: 10,
            processTypeId
          })
        },
        customFieldMapper('processName', 'id')
      ),
      componentProps: {
        placeholder: '请选择内控措施类型',
        options: [],
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'processId',
    label: '内控流程名称',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择内控流程名称',
        options: [],
        style: { width: '200px' }
      }
    },
    table: {
      width: 200
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      linkage: createSimpleLinkage(
        'processId',
        'nodeId',
        async (processId) => {
          return await getFlowManagementNodeListApi(processId)
        },
        customFieldMapper('nodeName', 'id')
      ),
      componentProps: {
        placeholder: '请选择内控流程名称',
        style: { width: '100%' },
        maxlength: 200,
        options: [],
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'nodeId',
    label: '内控节点名称',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择内控节点名称',
        options: [],
        style: { width: '200px' }
      }
    },
    table: {
      width: 200
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择内控节点名称',
        style: { width: '100%' },
        maxlength: 200,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'departmentId',
    label: '责任部门',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请选择责任部门',
        style: { width: '200px' }
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'DeptTreeSelect',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择责任部门',
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  // {
  //   field: 'measureCode',
  //   label: '内控措施编号',
  //   isSearch: true,
  //   isTable: true,
  //   isForm: true,
  //   isDetail: true,
  //   search: {
  //     component: 'Input',
  //     componentProps: {
  //       placeholder: '请输入措施编号'
  //     }
  //   },
  //   table: {
  //     width: 150
  //   },
  //   form: {
  //     component: 'Input',
  //     colProps: { span: 12 },
  //     componentProps: {
  //       placeholder: '请输入内控措施编号',
  //       style: { width: '100%' },
  //       maxlength: 100,
  //       clearable: true
  //     }
  //   },
  //   detail: {
  //     span: 1
  //   }
  // },

  // ==================== 控制措施组 ====================
  {
    field: 'measureName',
    label: '控制措施名称',
    isSearch: false,
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 150
    },
    form: {
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入控制措施名称',
        style: { width: '100%' },
        maxlength: 200,
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'measureLevel',
    label: '控制措施级别',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.CONTROL_MEASURE_LEVEL,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择控制措施级别'
      }
    },
    table: {
      width: 100
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择控制措施级别'
      }
    }
  },
  {
    field: 'measureType',
    label: '控制措施类型',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.CONTROL_MEASURE_TYPE,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择控制措施类型'
      }
    },
    table: {
      width: 100
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择控制措施类型'
      }
    }
  },

  {
    field: 'measureDescription',
    label: '控制措施描述',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入控制措施描述',
        type: 'textarea',
        rows: 3,
        maxlength: 500,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },

  // ==================== 风险信息组 ====================
  {
    field: 'riskName',
    label: '风险点名称',
    isSearch: true,
    isTable: false,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择关联风险点',
        options: RISK_TYPE_LIST
      }
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择关联风险点',
        options: RISK_TYPE_LIST,
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'riskLevel',
    label: '风险等级',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: false,
    table: {
      width: 100
    },
    form: {
      component: 'Input', // 添加组件类型
      colProps: { span: 12 },
      componentProps: {
        disabled: true, // 设置为只读
        style: { width: '100%' }
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'riskProbability',
    label: '风险发生概率',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择风险类型',
        style: { width: '100%' },
        clearable: true,
        disabled: true //禁用选择riskName的时候自动带入riskLevel
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'riskDesc',
    label: '风险点描述',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入风险点描述',
        type: 'textarea',
        rows: 3,
        maxlength: 500,
        clearable: true,
        disabled: true, //禁用选择riskName的时候自动带入riskLevel
        style: { width: '100%' }
      }
    },
    detail: {
      span: 2
    }
  },

  // ==================== 时间信息组 ====================
  {
    field: 'createTime',
    label: '创建时间',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 160
    },
    detail: {
      span: 1,
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'updateTime',
    label: '更新时间',
    isSearch: false,
    isTable: false,
    isForm: false,
    isDetail: true,
    detail: {
      span: 1,
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },

  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {
      width: 200,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段Schema
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return measureSchemas.filter((schema) =>
    ['companyId', 'processTypeId', 'processId', 'nodeId', 'departmentId'].includes(schema.field)
  )
}

/**
 * 获取控制信息字段Schema
 */
export const getControlInfoSchemas = (): CrudSchema[] => {
  return measureSchemas.filter((schema) =>
    ['measureName', 'measureLevel', 'measureType', 'measureDescription'].includes(schema.field)
  )
}

/**
 * 获取风险信息字段Schema
 */
export const getRiskInfoSchemas = (): CrudSchema[] => {
  return measureSchemas.filter((schema) =>
    ['riskName', 'riskLevel', 'riskProbability', 'riskDesc'].includes(schema.field)
  )
}

/**
 * 获取表单验证规则
 */
export const getMeasureFormRules = () => {
  return {
    companyId: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
    processTypeId: [{ required: true, message: '请选择内控措施类型', trigger: 'change' }],
    processId: [{ required: true, message: '请选择内控流程名称', trigger: 'change' }],
    nodeId: [{ required: true, message: '请选择内控节点名称', trigger: 'change' }],
    departmentId: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
    measureName: [{ required: true, message: '请输入控制措施名称', trigger: 'blur' }],
    measureLevel: [{ required: true, message: '请选择控制措施级别', trigger: 'change' }],
    measureType: [{ required: true, message: '请选择控制措施类型', trigger: 'change' }],
    measureDescription: [{ required: true, message: '请输入控制措施描述', trigger: 'blur' }],
    riskName: [{ required: true, message: '请选择关联风险点', trigger: 'change' }]
    // riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
    // riskProbability: [{ required: true, message: '请选择风险类型', trigger: 'change' }],
    // riskDesc: [{ required: true, message: '请输入风险点描述', trigger: 'blur' }]
  }
}
