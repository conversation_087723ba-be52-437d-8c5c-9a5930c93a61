<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            :showSave="mode !== 'view'"
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSave"
          />
        </div>
      </div>
      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
          <div class="mt-12px">
            <Form
              :schema="allSchemas.formSchema"
              @register="register"
              isCol
              :rules="formRules"
              :model="formData"
              :disabled="mode === 'view'"
            />
          </div>

          <div class="bg-#f1f2fe lh-40px pl-5 mt-20px">流程节点</div>
          <div class="mt-12px">
            <FlowNodeManager
              v-model="formData.processNodes"
              :mode="mode"
              ref="flowNodeManagerRef"
              @change="handleNodeChange"
            />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getFlowManagementDetailApi,
  createFlowManagementApi,
  updateFlowManagementApi,
  getFlowManagementNodeListApi,
  type FlowManagementVO
} from '@/api/ICM/matrix/flowManag'
import { flowSchemas, getFlowFormRules } from '../schemas/flowSchemas'
import { useMessage } from '@/hooks/web/useMessage'
import FlowNodeManager from './FlowNodeManager.vue'
import { DEFAULT_FLOW_NODE } from '../constants/flowConstants'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle } = useLayout()
// 使用 CRUD Schemas 生成表单配置
const { allSchemas } = useCrudSchemas(flowSchemas)

const message = useMessage()

// 发送事件
const emit = defineEmits(['save', 'close'])

// 响应式数据
const visible = ref(false)
const mode = ref('add') // add 或 edit
const flowId = ref<number | null>(null)
const flowNodeManagerRef = ref()

// 表单数据
const formData = ref<FlowManagementVO>({
  companyId: '',
  processTypeId: '',
  processName: '',
  scope: '',
  description: '',
  processNodes: [{ ...DEFAULT_FLOW_NODE }]
})

// 标题计算属性
const title = computed(() => {
  if (mode.value === 'add') return '新增内控流程'
  if (mode.value === 'edit') return '编辑内控流程'
  return '查看内控流程'
})

// 表单验证规则
const formRules = reactive(getFlowFormRules())

// 使用表单Hook
const { register, methods } = useForm()

// 设置表单数据
const setValue = async () => {
  if (mode.value === 'edit' && flowId.value) {
    try {
      const res = await getFlowManagementDetailApi(flowId.value)
      const nodesdata = await getFlowManagementNodeListApi(flowId.value)
      const data = res

      formData.value = {
        ...data,
        processNodes: nodesdata
      }

      // 设置表单值
      const formValues: any = {}
      allSchemas.formSchema.forEach((item) => {
        formValues[item.field] = data[item.field]
      })
      methods.setEditData(formValues)
    } catch (error) {
      console.error('加载流程详情失败:', error)
      message.error('加载流程详情失败')
    }
  } else {
    // 新增模式，重置表单
    formData.value = {
      companyId: '',
      processTypeId: '',
      processName: '',
      scope: '',
      description: '',
      processNodes: [{ ...DEFAULT_FLOW_NODE }]
    }
    methods.setValues({})
  }
}

// 打开全屏页面
const open = async (type: string, id: number | null = null) => {
  mode.value = type
  flowId.value = id
  visible.value = true

  await nextTick()
  await setValue()
}

// 关闭全屏页面
const handleClose = () => {
  visible.value = false
  flowId.value = null
  formData.value = {
    companyId: '',
    processTypeId: '',
    processName: '',
    scope: '',
    description: '',
    processNodes: [{ ...DEFAULT_FLOW_NODE }]
  }
  emit('close')
}

// 流程节点变化处理
const handleNodeChange = (processNodes: any[]) => {
  formData.value.processNodes = processNodes
}

// 保存
const handleSave = async () => {
  try {
    // 验证基础表单
    const valid = await methods.validate()
    if (!valid) return

    // 验证流程节点
    if (!flowNodeManagerRef.value?.validateNodes()) {
      message.error('请完善所有流程节点的名称和描述')
      return
    }

    if (!formData.value.processNodes || formData.value.processNodes.length === 0) {
      message.error('请至少添加一个流程节点')
      return
    }

    const formValues = await methods.getFormData()

    // 合并表单数据和节点数据
    const submitData: FlowManagementVO = {
      ...formValues,
      processNodes: formData.value.processNodes
    }

    if (mode.value === 'add') {
      await createFlowManagementApi(submitData)
      message.success('新增成功')
    } else {
      submitData.id = flowId.value!
      await updateFlowManagementApi(submitData)
      message.success('编辑成功')
    }

    handleClose()
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  }
}

// 对外暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.bg-\#f1f2fe {
  background-color: #f1f2fe;
}

.lh-40px {
  line-height: 40px;
}

.pl-5 {
  padding-left: 20px;
}

.mt-12px {
  margin-top: 12px;
}

.mt-20px {
  margin-top: 20px;
}
</style>
