<template>
  <div class="case-list">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="searchConfig"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
          :model="searchParams"
        />
        <!-- <Button type="primary" @click="handleAdd">新建案件</Button>
        <Button type="primary" @click="handleExport">导出</Button> -->
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <!-- 操作列 -->
        <template #status="{ row }">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="row.status" />
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleView(row)">查看详情</Button>
          <!-- <Button link type="primary" @click="handleApproval(row)">审批</Button> -->
          <!-- <Button link type="primary" @click="handleEdit(row)">编辑</Button>
          <Button link type="danger" @click="handleDelete(row)">删除</Button> -->
        </template>
      </Table>
    </ContentWrap>

    <!-- <CaseDetail ref="caseDetailRef" @close="handleCaseDetailClose" @save="handleCaseDetailSave" /> -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { getCaseListApi, deleteCaseApi, getCaseDetailApi } from '@/api/law/case'
import { ElMessage, ElMessageBox } from 'element-plus'
import CaseDetail from '../detail/index.vue'
import { getDictLabel, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter2 } from '@/utils/formatTime'
import { useRouter } from 'vue-router'
import { getCaseProcedureOptions } from '../schemas/caseSchemas'
const router = useRouter()
// 搜索组件引用
const searchRef = ref()

// 搜索参数
const searchParams = ref({})

// 搜索配置
const searchConfig = ref([
  {
    field: 'caseName',
    label: '案件名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入案件名称'
    },
    colProps: {
      span: 8 // 控制宽度，24为满宽，8表示占1/3宽度
    }
  },
  {
    field: 'belongUnitId',
    label: '所属单位',
    component: 'DeptTreeSelect',
    componentProps: {
      placeholder: '请选择',
      style: {
        width: '120px' // 直接设置宽度
      }
    }
  },
  {
    field: 'approvalType',
    label: '审批状态',
    component: 'Select',
    options: getStrDictOptions(DICT_TYPE.APPROVAL_TYPE),
    componentProps: {
      placeholder: '请选择',
      style: {
        width: '120px' // 直接设置宽度
      }
    }
  }
])

// 表格列配置
const tableColumns = ref([
  {
    field: 'index',
    label: '序号',
    type: 'index'
  },
  {
    field: 'caseName',
    label: '案件名称',
    minWidth: 180
  },
  {
    field: 'caseCode',
    label: '案件编号',
    minWidth: 160
  },
  {
    field: 'caseType',
    label: '案件类型',
    minWidth: 120,
    formatter: (row) => {
      return getDictLabel(DICT_TYPE.CASE_TYPE, row.caseType) || row.caseType
    }
  },
  {
    field: 'caseProcess',
    label: '案件程序',
    minWidth: 120,
    formatter: (row) => {
      return getCaseProcedureOptions(row.caseType).find((item) => item.value === row.caseProcess)
        ?.label
    }
  },
  {
    field: 'ourStatus',
    label: '案件地位',
    minWidth: 120,
    formatter: (row) => {
      return getDictLabel(DICT_TYPE.LITIGATION_POSITION, row.ourStatus) || row.caseType
    }
  },
  {
    field: 'filingDate',
    label: '立案日期',
    minWidth: 120,
    formatter: dateFormatter2
  },
  {
    field: 'status',
    label: '审批状态',
    minWidth: 120
  },
  {
    field: 'action',
    label: '操作',
    width: 200,
    fixed: 'right'
  }
])

// 使用表格hook
const {
  tableRegister,
  tableMethods: { getList: getTableList, setSearchParams },
  tableObject
} = useTable({
  getListApi: getCaseListApi,
  immediate: true
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

const caseDetailRef = ref()
// 查看详情
const handleView = async (row) => {
  router.push({
    path: '/bpm/process-instance/detail',
    query: {
      id: row.processInstanceId
    }
  })
}
const handleApproval = async (row) => {
  router.push({
    path: '/bpm/process-instance/detail',
    query: {
      id: row.processInstanceId,
      taskId: row.taskId
    }
  })
}
// 关闭案件详情弹窗
const handleCaseDetailClose = () => {}
// 保存案件详情
const handleCaseDetailSave = () => {}

onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.case-list {
  height: 100%;
}
</style>
