import { Random } from 'mockjs'

const SUCCESS_CODE = 200
const timeout = 1000

// 模拟数据
const flowManagementList = [
  {
    id: 1,
    belongCompany: 'XXXX公司',
    flowType: '财务管理',
    flowName: '申报流程',
    applicableScope: '全体员工',
    description: '申报流程的详细描述，包含各个环节的具体要求和注意事项',
    nodes: [
      { id: 1, name: '申请发起', description: '员工发起申请', sort: 1 },
      { id: 2, name: '部门审核', description: '部门领导审核', sort: 2 },
      { id: 3, name: '财务审批', description: '财务部门审批', sort: 3 },
      { id: 4, name: '最终确认', description: '最终确认并执行', sort: 4 },
      { id: 5, name: '归档存档', description: '完成后归档', sort: 5 },
      { id: 6, name: '结果反馈', description: '结果反馈给申请人', sort: 6 },
      { id: 7, name: '流程结束', description: '流程正式结束', sort: 7 }
    ],
    nodeCount: 7,
    relatedDescription: 'XXXXXXXXXXXX',
    createTime: '2023-04-23 19:48:36',
    updateTime: '2023-04-23 19:48:36'
  },
  {
    id: 2,
    belongCompany: 'XXXX公司',
    flowType: '财务管理',
    flowName: '核对流程',
    applicableScope: '全体员工',
    description: '核对流程的详细描述，确保数据准确性',
    nodes: [
      { id: 1, name: '数据收集', description: '收集需要核对的数据', sort: 1 },
      { id: 2, name: '初步核对', description: '进行初步核对', sort: 2 },
      { id: 3, name: '复核确认', description: '再次复核确认', sort: 3 },
      { id: 4, name: '异常处理', description: '处理发现的异常', sort: 4 },
      { id: 5, name: '最终确认', description: '最终确认结果', sort: 5 },
      { id: 6, name: '报告生成', description: '生成核对报告', sort: 6 },
      { id: 7, name: '存档备案', description: '存档备案处理', sort: 7 },
      { id: 8, name: '通知相关人员', description: '通知相关人员', sort: 8 },
      { id: 9, name: '流程完成', description: '流程正式完成', sort: 9 }
    ],
    nodeCount: 9,
    relatedDescription: 'XXXXXXXXXXXX',
    createTime: '2023-04-23 19:48:36',
    updateTime: '2023-04-23 19:48:36'
  },
  {
    id: 3,
    belongCompany: 'XXXX公司',
    flowType: '业务活动',
    flowName: '分配流程',
    applicableScope: 'XX部门、XX部门',
    description: '分配流程的详细描述，涉及资源分配和任务分配',
    nodes: [
      { id: 1, name: '需求分析', description: '分析分配需求', sort: 1 },
      { id: 2, name: '资源评估', description: '评估可用资源', sort: 2 },
      { id: 3, name: '分配方案', description: '制定分配方案', sort: 3 },
      { id: 4, name: '方案审批', description: '审批分配方案', sort: 4 },
      { id: 5, name: '执行分配', description: '执行资源分配', sort: 5 },
      { id: 6, name: '效果跟踪', description: '跟踪分配效果', sort: 6 }
    ],
    nodeCount: 6,
    relatedDescription: 'XXXXXXXXXXXX',
    createTime: '2023-04-23 19:48:36',
    updateTime: '2023-04-23 19:48:36'
  },
  {
    id: 4,
    belongCompany: 'XXXX公司',
    flowType: '支持性',
    flowName: '系统控制流程',
    applicableScope: 'XX部门',
    description: '系统控制流程的详细描述，确保系统运行稳定',
    nodes: [
      { id: 1, name: '系统监控', description: '监控系统状态', sort: 1 },
      { id: 2, name: '异常识别', description: '识别系统异常', sort: 2 },
      { id: 3, name: '问题分析', description: '分析问题原因', sort: 3 },
      { id: 4, name: '解决方案', description: '制定解决方案', sort: 4 },
      { id: 5, name: '方案执行', description: '执行解决方案', sort: 5 }
    ],
    nodeCount: 5,
    relatedDescription: 'XXXXXXXXXXXX',
    createTime: '2023-04-23 19:48:36',
    updateTime: '2023-04-23 19:48:36'
  },
  {
    id: 5,
    belongCompany: 'XXXX公司',
    flowType: '合规与风险管理',
    flowName: '监管流程',
    applicableScope: '全体员工',
    description: '监管流程的详细描述，确保合规运营',
    nodes: [
      { id: 1, name: '合规检查', description: '进行合规检查', sort: 1 },
      { id: 2, name: '风险识别', description: '识别潜在风险', sort: 2 },
      { id: 3, name: '风险评估', description: '评估风险等级', sort: 3 },
      { id: 4, name: '整改措施', description: '制定整改措施', sort: 4 },
      { id: 5, name: '措施执行', description: '执行整改措施', sort: 5 },
      { id: 6, name: '效果验证', description: '验证整改效果', sort: 6 }
    ],
    nodeCount: 6,
    relatedDescription: 'XXXXXXXXXXXX',
    createTime: '2023-04-23 19:48:36',
    updateTime: '2023-04-23 19:48:36'
  },
  {
    id: 6,
    belongCompany: 'XXXX公司',
    flowType: '跨职能综合',
    flowName: '报告流程',
    applicableScope: 'XX部门',
    description: '报告流程的详细描述，涉及多部门协作',
    nodes: [
      { id: 1, name: '数据收集', description: '收集报告数据', sort: 1 },
      { id: 2, name: '数据整理', description: '整理分析数据', sort: 2 },
      { id: 3, name: '报告编写', description: '编写报告内容', sort: 3 },
      { id: 4, name: '内容审核', description: '审核报告内容', sort: 4 },
      { id: 5, name: '格式校对', description: '校对报告格式', sort: 5 },
      { id: 6, name: '最终审批', description: '最终审批报告', sort: 6 },
      { id: 7, name: '报告发布', description: '发布正式报告', sort: 7 }
    ],
    nodeCount: 7,
    relatedDescription: 'XXXXXXXXXXXX',
    createTime: '2023-04-23 19:48:36',
    updateTime: '2023-04-23 19:48:36'
  }
]

export default [
  // 获取内控流程分页列表
  {
    url: '/admin-api/api/ICM/flowManagement/page',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, belongCompany, flowName, flowType } = query
      let filteredList = [...flowManagementList]

      // 筛选条件
      if (belongCompany && belongCompany !== '全部') {
        filteredList = filteredList.filter((item) => item.belongCompany === belongCompany)
      }

      if (flowName) {
        filteredList = filteredList.filter((item) =>
          item.flowName.toLowerCase().includes(flowName.toLowerCase())
        )
      }

      if (flowType) {
        filteredList = filteredList.filter((item) =>
          item.flowType.toLowerCase().includes(flowType.toLowerCase())
        )
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + Number(pageSize)
      const list = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list,
          total: filteredList.length
        }
      }
    }
  },

  // 获取内控流程详情
  {
    url: '/admin-api/api/ICM/flowManagement/get',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const item = flowManagementList.find((item) => item.id === Number(id))

      return {
        code: SUCCESS_CODE,
        data: item || null
      }
    }
  },

  // 创建内控流程
  {
    url: '/admin-api/api/ICM/flowManagement/create',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const newItem = {
        ...body,
        id: flowManagementList.length + 1,
        nodeCount: body.nodes ? body.nodes.length : 0,
        relatedDescription: body.description || 'XXXXXXXXXXXX',
        createTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
        updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
      }

      flowManagementList.push(newItem)

      return {
        code: SUCCESS_CODE,
        data: newItem.id
      }
    }
  },

  // 更新内控流程
  {
    url: '/admin-api/api/ICM/flowManagement/update',
    method: 'put',
    timeout,
    response: ({ body }) => {
      const index = flowManagementList.findIndex((item) => item.id === body.id)

      if (index !== -1) {
        body.updateTime = new Date().toISOString().replace('T', ' ').substr(0, 19)
        body.nodeCount = body.nodes ? body.nodes.length : 0
        body.relatedDescription = body.description || 'XXXXXXXXXXXX'
        flowManagementList[index] = { ...flowManagementList[index], ...body }

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '更新失败，记录不存在'
      }
    }
  },

  // 删除内控流程
  {
    url: '/admin-api/api/ICM/flowManagement/delete',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const index = flowManagementList.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        flowManagementList.splice(index, 1)

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '删除失败，记录不存在'
      }
    }
  },

  // 导出内控流程
  {
    url: '/admin-api/api/ICM/flowManagement/export',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: SUCCESS_CODE,
        data: '导出成功'
      }
    }
  }
]
