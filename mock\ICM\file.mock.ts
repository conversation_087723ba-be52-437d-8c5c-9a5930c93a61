import Mock from 'mockjs'
import { SUCCESS_CODE } from '../public.constant'

const timeout = 1000

// 模拟数据
const icmFileList = Mock.mock({
  'list|100': [
    {
      'id|+1': 1,
      title: () => Mock.Random.ctitle(10, 30),
      fileNo: () => Mock.Random.string('upper', 2) + Mock.Random.string('number', 8),
      department: () =>
        Mock.Random.pick([
          '总行',
          '风险管理部',
          '合规部',
          '法律事务部',
          '运营管理部',
          '信息科技部',
          '人力资源部',
          '财务会计部'
        ]),
      fileType: () =>
        Mock.Random.pick([
          '制度文件',
          '流程文件',
          '操作指南',
          '管理办法',
          '实施细则',
          '工作规程',
          '业务规范',
          '操作手册'
        ]),
      belongCompany: () => Mock.Random.pick(['全部', 'XXX集团公司']),
      issueDate: () => Mock.Random.date('yyyy-MM-dd'),
      expireDate: () => Mock.Random.date('yyyy-MM-dd'),
      description: () => Mock.Random.cparagraph(2, 5),
      'status|1': [0, 1, 2],
      version: () => 'V' + Mock.Random.float(1, 5, 1, 1),
      'currentVersion|1': true,
      attachments: () =>
        Mock.mock({
          'list|0-5': [
            {
              'id|+1': 1000,
              fileName: () =>
                Mock.Random.ctitle(5, 15) +
                Mock.Random.pick(['.doc', '.docx', '.pdf', '.xls', '.xlsx']),
              fileUrl: () => Mock.Random.url('http'),
              fileSize: () => Mock.Random.integer(1024, 10485760),
              uploadTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
              uploader: () => Mock.Random.cname(),
              version: () => 'V' + Mock.Random.float(1, 5, 1, 1),
              'isActive|1': true
            }
          ]
        }).list,
      createTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    }
  ]
}).list

export default [
  // 获取内控文件分页列表
  {
    url: '/admin-api/api/ICM/file/page',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, title, fileNo, department, status, fileType } = query

      let filteredList = [...icmFileList]

      // 筛选条件
      if (title) {
        filteredList = filteredList.filter((item) => item.title.includes(title))
      }

      if (fileNo) {
        filteredList = filteredList.filter((item) => item.fileNo.includes(fileNo))
      }

      if (department) {
        filteredList = filteredList.filter((item) => item.department === department)
      }

      if (fileType) {
        filteredList = filteredList.filter((item) => item.fileType === fileType)
      }

      if (status !== undefined && status !== '') {
        filteredList = filteredList.filter((item) => item.status === Number(status))
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + Number(pageSize)
      const list = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list,
          total: filteredList.length
        }
      }
    }
  },

  // 获取内控文件详情
  {
    url: '/admin-api/api/ICM/file/get',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const item = icmFileList.find((item) => item.id === Number(id))

      return {
        code: SUCCESS_CODE,
        data: item || null
      }
    }
  },

  // 创建内控文件
  {
    url: '/admin-api/api/ICM/file/create',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const newItem = {
        ...body,
        id: icmFileList.length + 1,
        status: 0, // 新建时默认为草稿状态
        version: 'V1.0',
        currentVersion: true,
        attachments: body.attachments || [],
        createTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
        updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
      }

      icmFileList.push(newItem)

      return {
        code: SUCCESS_CODE,
        data: newItem.id
      }
    }
  },

  // 更新内控文件
  {
    url: '/admin-api/api/ICM/file/update',
    method: 'put',
    timeout,
    response: ({ body }) => {
      const index = icmFileList.findIndex((item) => item.id === body.id)

      if (index !== -1) {
        body.updateTime = new Date().toISOString().replace('T', ' ').substr(0, 19)
        icmFileList[index] = { ...icmFileList[index], ...body }

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '更新失败，记录不存在'
      }
    }
  },

  // 删除内控文件
  {
    url: '/admin-api/api/ICM/file/delete',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const index = icmFileList.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        icmFileList.splice(index, 1)

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '删除失败，记录不存在'
      }
    }
  },

  // 导出内控文件
  {
    url: '/admin-api/api/ICM/file/export',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: SUCCESS_CODE,
        data: '导出成功'
      }
    }
  }
]
