<template>
  <div class="authorization-approval">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="searchConfig"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
        />
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <!-- 状态列 -->
        <template #status="{ row }">
          <dict-tag :type="DICT_TYPE.AUTHORIZATION_STATUS" :value="row.status" />
        </template>
        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleApproval(row)">查看详情 </Button>
        </template>
      </Table>
    </ContentWrap>
  </div>
</template>

<script setup>
import { ContentWrap, Search, Button, Table } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { getAuthorizationApplicationPage } from '@/api/law/authorization'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { reactive, ref, onMounted } from 'vue'
import AuthorizationDetail from '../detail/index.vue'
import { dateFormatter2, dateFormatter } from '@/utils/formatTime'
import { useRouter } from 'vue-router'
const router = useRouter()
const searchRef = ref()
const authorizationDetailRef = ref()

// 搜索参数
const searchParams = reactive({
  authorizer: '',
  authorized: '',
  authorizationType: '',
  applicationStatus: ''
})

// 搜索配置
const searchConfig = [
  {
    field: 'authorizer',
    label: '授权人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入授权人'
    }
  },
  {
    field: 'authorized',
    label: '被授权人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入被授权人'
    }
  },
  {
    field: 'authorizationType',
    label: '授权类型',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AUTHORIZATION_TYPE),
      placeholder: '请选择授权类型'
    }
  },
  {
    field: 'applicationStatus',
    label: '审批状态',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS),
      placeholder: '请选择审批状态'
    }
  }
]

// 表格列配置
const tableColumns = [
  {
    field: 'index',
    label: '序号',
    type: 'index'
  },
  {
    label: '授权编号',
    field: 'authorizationCode'
    // width: 150
  },
  {
    label: '授权人',
    field: 'authorizer'
    // width: 120
  },
  {
    label: '被授权人',
    field: 'authorizedPerson'
    // width: 120
  },
  {
    label: '授权类型',
    field: 'authorizationType',
    // width: 120,
    dictType: DICT_TYPE.AUTHORIZATION_TYPE
  },
  {
    label: '授权起始日期',
    field: 'startDate',
    // width: 120,
    formatter: dateFormatter2
  },
  {
    label: '授权截止日期',
    field: 'endDate',
    // width: 120,
    formatter: dateFormatter2
  },
  {
    label: '审批状态',
    field: 'applicationStatus',
    // width: 100,
    dictType: DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS
  },
  {
    label: '创建时间',
    field: 'createTime',
    width: 150,
    formatter: dateFormatter
  },
  {
    label: '操作',
    field: 'action',
    fixed: 'right'
  }
]

// 表格相关
const {
  tableObject,
  tableMethods: { getList: getTableList, setSearchParams },
  tableRegister
} = useTable({
  getListApi: getAuthorizationApplicationPage
})
// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)
const tableMethods = {
  getList: getTableList
}

// 重置
const handleReset = () => {
  // searchRef.value.resetFields()
  searchParams.value = {}
  tableMethods.getList()
}

// 审批处理
const handleApproval = (row) => {
  router.push({
    path: '/bpm/process-instance/detail',
    query: {
      id: row.processInstanceId
    }
  })
}

// 关闭详情弹窗
const handleDetailClose = () => {
  // 处理关闭逻辑
}

// 保存详情
const handleDetailSave = () => {
  getTableList()
}

onMounted(() => {
  getTableList()
})
</script>
