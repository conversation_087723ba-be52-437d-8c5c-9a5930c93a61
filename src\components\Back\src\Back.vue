<!--
 * @Author: fcc
 * @Date: 2024-04-28 11:28:39
 * @LastEditors: fcc
 * @LastEditTime: 2024-12-05 17:46:13
 * @FilePath: /-data-manage-web/src/views/Basic/components/EventBack.vue
 * @Description: 返回组件
-->
<script setup lang="tsx">
import { Icon } from '@/components/Icon'
import { propTypes } from '@/utils/propTypes'
import { useSlots } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const props = defineProps({
  showSave: propTypes.bool.def(false),
  showCancel: propTypes.bool.def(false),
  back: propTypes.bool.def(true),
  hasEmit: propTypes.bool.def(false),
  to: propTypes.string.def('/'),
  title: propTypes.string.def('')
})

const emit = defineEmits(['back', 'close', 'cancel', 'ok'])

const goback = () => {
  !props.hasEmit && router.push(props.to)
  props.hasEmit && emit('back', props.to)
}

/**
 * 关闭
 */
const handleClose = () => {
  emit('close', true)
}

/**
 * 保存
 */
const handleSave = () => {
  emit('ok', true)
}

/**
 * 取消
 */
const handleCancel = () => {
  emit('cancel', true)
}

const slots = useSlots()

/**
 * 渲染return
 */
const render = () => (
  <div class="border-b border-light-700 flex items-center flex-start  border-b-solid h-32px w-full">
    <>
      {props.back && (
        <Button link onClick={() => goback()}>
          <Icon icon="ep:arrow-left" size={12} class="mr-1"></Icon>
          返回
        </Button>
      )}
    </>

    <>
      {props.title && (
        <div class="text-center flex justify-center w-full pr-44px">
          <p class="text-center text-16px text-gray-500">{props.title}</p>
        </div>
      )}
    </>

    <div>{slots.default && slots.default()}</div>

    <>
      {!props.back && !props.showCancel && (
        <div
          class="w-24px h-24px flex items-center justify-center cursor-pointer hover:bg-light-400"
          onClick={() => handleClose()}
        >
          <Icon size={20} icon="svg-icon:icon-close"></Icon>
        </div>
      )}
    </>

    <>
      {(props.showCancel || props.showSave) && (
        <div class="flex items-center">
          {props.showCancel && <Button onClick={() => handleCancel()}>取消</Button>}
          {props.showSave && (
            <Button type="primary" onClick={() => handleSave()}>
              保存
            </Button>
          )}
        </div>
      )}
    </>
  </div>
)
</script>

<template>
  <component :is="render()" />
</template>
