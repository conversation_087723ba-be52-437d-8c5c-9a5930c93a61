<template>
  <div class="file-type-tree">
    <div class="head-container">
      <el-input v-model="filterText" class="mb-20px" clearable placeholder="请输入文件类型">
        <template #prefix>
          <Icon icon="ep:search" />
        </template>
      </el-input>
    </div>
    <div class="tree-container">
      <el-tree
        ref="treeRef"
        :data="fileTypeList"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :props="treeProps"
        default-expand-all
        highlight-current
        node-key="id"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span class="tree-node">
            <Icon :icon="data.icon" class="mr-5px" />
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import { getTypeManagListApi } from '@/api/ICM/file/typeManag'

defineOptions({ name: 'FileTypeTree' })

const filterText = ref('')
const fileTypeList = ref<any[]>([])
const treeRef = ref<InstanceType<typeof ElTree>>()

const treeProps = {
  children: 'children',
  label: 'label',
  value: 'id'
}

// 构建文件类型树结构
const buildFileTypeTree = (typeList: any[]) => {
  const tree = [
    {
      id: 'all',
      label: '全部文件',
      icon: 'ep:folder',
      children: []
    }
  ]

  // 按文件类型分组
  const typeGroups = new Map()
  typeList.forEach((item) => {
    if (!typeGroups.has(item.type)) {
      typeGroups.set(item.type, [])
    }
    typeGroups.get(item.type).push(item)
  })

  // 构建树结构
  typeGroups.forEach((items, type) => {
    const typeNode = {
      id: `type_${type}`,
      label: type,
      icon: 'ep:document',
      type: 'fileType',
      value: type,
      children: items.map((item) => ({
        id: item.id,
        label: `${item.typeCode} - ${item.type}`,
        icon: 'ep:document-copy',
        type: 'fileItem',
        value: item.id,
        data: item
      }))
    }
    tree[0].children.push(typeNode)
  })

  return tree
}

/** 获取文件类型列表 */
const getFileTypeList = async () => {
  try {
    const res = await getTypeManagListApi({
      pageNum: 1,
      pageSize: 1000,
      status: 1 // 只获取启用的文件类型
    })

    if (res && res.code === 200 && res.data && res.data.list) {
      fileTypeList.value = buildFileTypeTree(res.data.list)
    } else {
      fileTypeList.value = buildFileTypeTree([])
    }
  } catch (error) {
    console.error('获取文件类型列表失败:', error)
    fileTypeList.value = buildFileTypeTree([])
  }
}

/** 基于名字过滤 */
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.includes(value)
}

/** 处理节点被点击 */
let currentNode: any = {}
const handleNodeClick = async (data: any, node: any) => {
  // 判断选中状态
  if (currentNode && currentNode.id === data.id) {
    node.checked = !node.checked
  } else {
    node.checked = true
  }

  if (node.checked) {
    // 选中
    currentNode = data
    emits('node-click', data)
  } else {
    // 取消选中
    treeRef.value!.setCurrentKey(undefined)
    emits('node-click', undefined)
    currentNode = null
  }
}

const emits = defineEmits(['node-click'])

/** 监听filterText */
watch(filterText, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getFileTypeList()
})
</script>

<style lang="scss" scoped>
.file-type-tree {
  height: 100%;

  .head-container {
    margin-bottom: 10px;
  }

  .tree-container {
    height: calc(100% - 60px);
    overflow-y: auto;
  }

  .tree-node {
    display: flex;
    align-items: center;

    .mr-5px {
      margin-right: 5px;
    }
  }
}

:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}
</style>
