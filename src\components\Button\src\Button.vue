<script setup lang="tsx">
import { useDesign } from '@/hooks/web/useDesign'
import { ElButton, ElTooltip, ComponentSize, ButtonType } from 'element-plus'
import { PropType, Component, computed, ref, unref } from 'vue'
import { useAppStore } from '@/store/modules/app'

const appStore = useAppStore()

const getTheme = computed(() => appStore.getTheme)

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('button')

const props = defineProps({
  size: {
    type: String as PropType<ComponentSize>,
    default: undefined
  },
  type: {
    type: String as PropType<ButtonType>,
    default: 'default'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  plain: {
    type: Boolean,
    default: false
  },
  text: {
    type: Boolean,
    default: false
  },
  bg: {
    type: Boolean,
    default: false
  },
  link: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  },
  circle: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingIcon: {
    type: [String, Object] as PropType<String | Component>,
    default: undefined
  },
  icon: {
    type: [String, Object] as PropType<String | Component>,
    default: undefined
  },
  autofocus: {
    type: Boolean,
    default: false
  },
  nativeType: {
    type: String as PropType<'button' | 'submit' | 'reset'>,
    default: 'button'
  },
  tooltip: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  },
  position: {
    type: String as PropType<
      | 'top'
      | 'top-start'
      | 'top-end'
      | 'bottom'
      | 'bottom-start'
      | 'bottom-end'
      | 'left'
      | 'left-start'
      | 'left-end'
      | 'right'
      | 'right-start'
      | 'right-end'
    >,
    default: 'top'
  },

  autoInsertSpace: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: ''
  },
  darker: {
    type: Boolean,
    default: false
  },
  tag: {
    type: [String, Object] as PropType<String | Component>,
    default: 'button'
  }
})

const emits = defineEmits(['click'])

const color = computed(() => {
  const { type } = props
  if (type === 'primary') {
    return unref(getTheme).elColorPrimary
  }
  return ''
})

const style = computed(() => {
  const { type, link } = props
  if (type === 'primary' && !link) {
    return '--el-button-text-color: #fff; --el-button-hover-text-color: #fff'
  }
  if (type === 'primary' && link) {
    return '--el-button-text-color: var(--el-color-primary); --el-button-hover-text-color: var(--el-color-primary)'
  }
  return ''
})


const isButtonDisabled = ref(false)
const handleClick = () => {
  if (isButtonDisabled.value) return // 如果按钮已禁用，则直接返回
  isButtonDisabled.value = true
  emits('click', 'ok')
  setTimeout(() => {
    isButtonDisabled.value = false
  }, 400)
}
</script>

<template>
  <ElTooltip
    v-if="tooltip"
    :effect="darker ? 'dark' : 'light'"
    :content="content"
    :placement="position"
  >
    <ElButton
      :class="`${prefixCls} color-#fff`"
      v-bind="{ ...props }"
      :color="color"
      :style="style"
      @click.stop="handleClick"
    >
      <slot></slot>
      <slot name="icon"></slot>
      <slot name="loading"></slot>
    </ElButton>
  </ElTooltip>
  <template v-else>
    <ElButton
      v-bind="{ ...props }"
      :class="`${prefixCls} color-#fff`"
      :color="color"
      :style="style"
      @click.stop="handleClick"
    >
      <slot></slot>
      <slot name="icon"></slot>
      <slot name="loading"></slot>
    </ElButton>
  </template>
</template>
