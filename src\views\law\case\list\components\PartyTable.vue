<template>
  <div class="mt-6">
    <div class="flex items-center justify-between mb-3">
      <h4 class="text-16px font-medium text-gray-800">{{ title }}</h4>
      <Button
        v-if="!props.disabled"
        link
        type="primary"
        @click="handleAdd"
        :disabled="props.data.length >= 10"
      >
        <Icon icon="svg-icon:icon-plus" :size="14" class="mr-1" />
        新增
      </Button>
    </div>

    <ElForm :ref="(el: any) => setFormRef(el, 0)" :model="{ data: props.data }">
      <Table :data="props.data || []" :columns="columns" :pagination="null" border stripe>
        <template #partyName="{ row, $index }">
          <ElFormItem :prop="`data.${$index}.partyName`" :rules="nameRules">
            <ElInput
              v-model="row.partyName"
              placeholder="请输入名称"
              maxlength="50"
              clearable
              :disabled="props.disabled"
            />
          </ElFormItem>
        </template>

        <template #legalRepresentative="{ row, $index }">
          <ElFormItem
            :prop="`data.${$index}.legalRepresentative`"
            :rules="legalRepresentativeRules"
          >
            <ElInput
              v-model="row.legalRepresentative"
              placeholder="请输入法定代表人"
              maxlength="50"
              clearable
              :disabled="props.disabled"
            />
          </ElFormItem>
        </template>

        <template #contactPerson="{ row, $index }">
          <ElFormItem :prop="`data.${$index}.contactPerson`" :rules="contactRules">
            <ElInput
              v-model="row.contactPerson"
              placeholder="请输入联系人"
              maxlength="50"
              clearable
              :disabled="props.disabled"
            />
          </ElFormItem>
        </template>

        <template #contactPhone="{ row, $index }">
          <ElFormItem :prop="`data.${$index}.contactPhone`" :rules="phoneRules">
            <ElInput
              v-model="row.contactPhone"
              placeholder="请输入联系电话"
              maxlength="20"
              clearable
              :disabled="props.disabled"
            />
          </ElFormItem>
        </template>

        <template #address="{ row, $index }">
          <ElFormItem :prop="`data.${$index}.address`" :rules="addressRules">
            <ElInput
              v-model="row.address"
              placeholder="请输入地址"
              maxlength="200"
              clearable
              :disabled="props.disabled"
            />
          </ElFormItem>
        </template>

        <template #actions="{ $index }">
          <Button
            type="danger"
            link
            @click="handleRemove($index)"
            :disabled="props.data.length <= 1"
          >
            删除
          </Button>
        </template>
      </Table>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { Icon, Button, Table } from '@/components'
import { ElForm, ElFormItem, ElInput } from 'element-plus'
import type { PartyInfo } from '../hooks/usePartyManagement'
import { computed, defineEmits, ref } from 'vue'
import { FORM_REGEX } from '@/constants/case'
import type { TableColumn } from '@/types/table'

interface Props {
  title: string
  data: PartyInfo[]
  disabled?: boolean
}

interface Emits {
  (e: 'add'): void
  (e: 'remove', index: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表格列配置
const columns = computed<TableColumn[]>(() => {
  const cols: TableColumn[] = [
    {
      field: 'partyName',
      label: '名称'
      // width: 150
    },
    {
      field: 'legalRepresentative',
      label: '法定代表人'
      // width: 120
    },
    {
      field: 'contactPerson',
      label: '联系人'
      // width: 100
    },
    {
      field: 'contactPhone',
      label: '联系电话'
      // width: 130
    },
    {
      field: 'address',
      label: '地址'
      // width: 200
    }
  ]

  // 如果不是禁用状态，添加操作列
  if (!props.disabled) {
    cols.push({
      field: 'actions',
      label: '操作',
      width: 120,
      fixed: 'right'
    })
  }

  return cols
})

// 表单引用
const formRef = ref<any>(null)

// 各字段的验证规则（移除必填校验，保留格式校验）
const nameRules = [
  {
    pattern: FORM_REGEX.CHINESE_ENGLISH_NUMBER,
    message: '请输入中英文字符和数字',
    trigger: 'change'
  }
]

const legalRepresentativeRules = [
  {
    validator: (_rule: any, value: any, callback: any) => {
      if (value && !FORM_REGEX.CHINESE_ENGLISH.test(value)) {
        callback(new Error('请输入中英文字符'))
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
]

const contactRules = [
  {
    validator: (_rule: any, value: any, callback: any) => {
      if (value && !FORM_REGEX.CHINESE_ENGLISH.test(value)) {
        callback(new Error('请输入中英文字符'))
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
]

const phoneRules = [
  {
    validator: (_rule: any, value: any, callback: any) => {
      if (value && !FORM_REGEX.NUMBER.test(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
]

const addressRules = [
  {
    validator: (_rule: any, value: any, callback: any) => {
      if (value && !FORM_REGEX.CHINESE_ENGLISH_NUMBER.test(value)) {
        callback(new Error('请输入中英文字符和数字'))
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
]

// 设置表单引用
const setFormRef = (el: any, _index: number) => {
  if (el && el.validate) {
    formRef.value = el
  }
}

// 验证所有表单
const validateAll = async (): Promise<boolean> => {
  try {
    if (!formRef.value) {
      return false
    }
    const result = await formRef.value.validate()
    return result
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

// 获取所有数据
const getAllData = (): PartyInfo[] => {
  return props.data || []
}

// 暴露方法给父组件
defineExpose({
  validateAll,
  getAllData
})

const handleAdd = () => {
  emit('add')
}

const handleRemove = (index: number) => {
  emit('remove', index)
}
</script>

<style scoped>
/* 如果需要特定样式可以在这里添加 */
</style>
