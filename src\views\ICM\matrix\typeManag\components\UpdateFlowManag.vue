<template>
  <Dialog
    :model-value="true"
    :title="dialogTitle"
    width="600px"
    :maxHeight="600"
    @close="handleClose"
  >
    <Form
      ref="formRef"
      :schema="formSchema"
      :rules="formRules"
      :disabled="props.mode === 'view'"
      label-width="120px"
      @register="register"
    >
    </Form>

    <template #footer>
      <div class="dialog-footer">
        <Button @click="handleClose">取消</Button>
        <Button
          v-if="props.mode !== 'view'"
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          确定
        </Button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { Dialog, Form, Button } from '@/components'
import { ElMessage } from 'element-plus'
import { FormSchema } from '@/types/form'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { schemas } from '../schemas/flowManagSchemas'
import {
  createTypeManagApi,
  updateTypeManagApi,
  type TypeManagVO
} from '@/api/ICM/matrix/typeManag'
import { useForm } from '@/hooks/web/useForm'

// 组件props
interface Props {
  mode: 'add' | 'edit' | 'view'
  data?: any
  parentInfo?: any
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add',
  data: () => ({}),
  parentInfo: null
})

// 组件emits
const emit = defineEmits<{
  success: []
  close: []
}>()

// 使用 CRUD Schemas 获取表单配置
const { allSchemas } = useCrudSchemas(schemas)

// 响应式数据
const currentId = ref<number>()
const submitLoading = ref(false)
const formRef = ref()
const { register } = useForm()

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增内控流程类型',
    edit: '编辑内控流程类型',
    view: '查看内控流程类型'
  }
  return titles[props.mode]
})

// 表单配置
const formSchema = computed((): FormSchema[] => {
  const baseSchema = allSchemas.formSchema.filter((item) => item.field !== 'action')

  // 如果有父级信息，在开头添加 parentTypeId 字段
  if (props.parentInfo && props.parentInfo.id) {
    const parentTypeIdSchema: FormSchema = {
      field: 'parentTypeId',
      label: '父级类型',
      component: 'Select',
      componentProps: {
        disabled: true,
        options: [
          {
            label: props.parentInfo.processTypeName,
            value: props.parentInfo.id
          }
        ],
        style: {
          width: '100%'
        }
      }
    }
    return [parentTypeIdSchema, ...baseSchema]
  }

  return baseSchema
})

// 表单验证规则
const formRules = computed(() => {
  const rules = {
    processTypeName: [
      { required: true, message: '请输入内控流程类型', trigger: 'blur' }
      // { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
    ],
    // typeCode: [
    //   { required: true, message: '请输入流程类型编号', trigger: 'blur' }
    //   // { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
    // ],
    companyId: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }]
    // description: [{ max: 200, message: '长度不能超过200个字符', trigger: 'blur' }]
  }

  // 如果有父级信息，添加 parentTypeId 验证规则
  if (props.parentInfo && props.parentInfo.id) {
    rules.parentTypeId = [{ required: true, message: '请选择父级类型', trigger: 'change' }]
  }

  return rules
})

// 初始化组件
const initComponent = async () => {
  // 等待表单组件完全初始化
  await nextTick()

  currentId.value = props.data?.id

  // 等待表单注册完成
  if (!formRef.value) {
    await nextTick()
  }

  // 设置表单数据
  if (formRef.value) {
    let formData = {}

    if ((props.mode === 'edit' || props.mode === 'view') && props.data) {
      // 编辑或查看模式，使用传入的数据
      formData = { ...props.data }
    } else {
      // 新增模式，设置默认值
      formData = {
        type: '',
        typeCode: '',
        belongUnitId: '',
        status: 1,
        description: '',
        parentTypeId: 0
      }
    }

    // 如果有父级信息，设置 parentTypeId
    if (props.parentInfo && props.parentInfo.id) {
      formData.parentTypeId = props.parentInfo.id
    }

    await formRef.value.setValues(formData)
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await initComponent()
})

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const elFormRef = formRef.value?.getElFormRef()
    if (!elFormRef) {
      ElMessage.error('表单未初始化')
      return
    }

    const valid = await elFormRef.validate()
    if (!valid) return

    submitLoading.value = true

    // 获取表单数据
    const formModel = formRef.value?.formModel || {}

    let res
    if (props.mode === 'add') {
      res = await createTypeManagApi(formModel)
    } else {
      res = await updateTypeManagApi({
        ...formModel,
        id: currentId.value
      })
    }

    ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;

  .el-button + .el-button {
    margin-left: 10px;
  }
}
</style>
