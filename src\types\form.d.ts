import type { CSSProperties } from 'vue'
import { ColProps, ComponentProps, ComponentName, ComponentOptions } from '@/types/components'
import type { AxiosPromise } from 'axios'

export type FormSetPropsType = {
  field: string
  path: string
  value: any
}

export type FormValueType = string | number | string[] | number[] | boolean | undefined | null

export type FormItemProps = {
  labelWidth?: string | number
  required?: boolean
  rules?: Recordable
  error?: string
  showMessage?: boolean
  inlineMessage?: boolean
  style?: CSSProperties
}

// 联动配置类型
export type FormLinkageConfig = {
  // 触发字段（当哪个字段变化时触发）
  triggerField: string
  // 目标字段（要更新选项的字段）
  targetField: string
  // 获取选项的API函数
  api: (triggerValue: any, formModel?: Recordable) => Promise<any>
  // 选项数据映射函数
  optionMapper?: (data: any) => ComponentOptions[]
  // 是否清空目标字段的值（默认true）
  clearValue?: boolean
  // 是否清空目标字段的选项（默认true）
  clearOptions?: boolean
  // 联动条件判断函数（可选，用于复杂条件判断）
  condition?: (triggerValue: any, formModel?: Recordable) => boolean
}

export type FormSchema = {
  // 唯一值
  field: string
  // 标题
  label?: string
  // 提示
  labelMessage?: string
  // col组件属性
  colProps?: ColProps
  // 表单组件属性，slots对应的是表单组件的插槽，规则：${field}-xxx，具体可以查看element-plus文档
  componentProps?: { slots?: Recordable } & ComponentProps
  // formItem组件属性
  formItemProps?: FormItemProps
  // 渲染的组件
  component?: ComponentName
  // 初始值
  value?: FormValueType
  // 是否隐藏
  hidden?: boolean
  // 远程加载下拉项
  api?: <T = any>() => AxiosPromise<T>
  // 联动配置
  linkage?: FormLinkageConfig | FormLinkageConfig[]
}
