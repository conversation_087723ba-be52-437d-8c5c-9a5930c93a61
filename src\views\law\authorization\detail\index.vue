<script setup>
import { computed, defineExpose, nextTick } from 'vue'
import { Back } from '@/components'
import { useMessage } from '@/hooks/web/useMessage'
import AuthorizationInfo from './components/AuthorizationInfo.vue'
import { Button } from '@/components'
import { getDictLabel, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { LogList } from '@/components/LogList'
const props = defineProps({
  id: {
    type: [Number, String],
    default: undefined
  },
  showClose: {
    type: Boolean,
    default: false
  }
})

const message = useMessage()
const emit = defineEmits(['close', 'save'])
const mode = ref('view') // 模式：view, approval
const visible = ref(false)
const title = computed(() => {
  switch (mode.value) {
    case 'view':
      return '查看授权委托'
    case 'approval':
      return '授权委托审批'
    default:
      return ''
  }
})
const editId = ref(props.id)

const handleClose = () => {
  editId.value = ''
  activName.value = ''
  emit('close')
}

const tabList = [
  {
    name: '授权信息',
    component: AuthorizationInfo
  },
  {
    name: '操作日志',
    component: LogList
  }
]

const handleSaveAction = async () => {}
const activName = ref('授权信息')
</script>

<template>
  <div>
    <div class="h-full flex flex-col bg-white">
      <div class="w-full h-auto flex-shrink-0 p-4 border-b">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <h2 class="text-lg font-semibold">{{ title }}</h2>
          </div>
          <div class="flex items-center space-x-4">
            <!-- <div class="flex items-center">
            <div class="mr-2">当前状态：</div>
            <dict-tag :type="DICT_TYPE.APPROVAL_TYPE" value="1" size="large" />
          </div>
          <div class="flex space-x-2" v-if="mode === 'approval'">
            <Button type="primary" @click="handleApprove('1')">通过</Button>
            <Button type="danger" @click="handleApprove('0')">驳回</Button>
          </div> -->
            <Button @click="handleClose" v-if="showClose">返回</Button>
          </div>
        </div>
      </div>
    </div>
    <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
      <ElTabs type="border-card" v-model="activName">
        <ElTabPane v-for="item in tabList" :key="item.name" :label="item.name" :name="item.name">
          <component :is="item.component" :id="editId" :type="6" />
        </ElTabPane>
      </ElTabs>
    </ElScrollbar>
  </div>
</template>
