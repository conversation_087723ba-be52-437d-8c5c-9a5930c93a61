<template>
  <div>
    <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
    <div class="mt-12px">
      <Descriptions :data="data" :schema="allSchemas.detailSchema" :column="2" />
    </div>

    <!-- 知识内容单独展示 -->
    <div class="mt-4">
      <div class="bg-#f1f2fe lh-40px pl-5">知识内容</div>
      <div class="mt-12px p-4 bg-gray-50 rounded-md">
        <div v-html="data.knowledgeContent"></div>
      </div>
    </div>

    <!-- 相关音视频和文档 -->
    <div class="mt-4" v-if="data.audioVideo || data.relatedDocs">
      <div class="bg-#f1f2fe lh-40px pl-5">相关资源</div>
      <div class="mt-12px">
        <!-- <div class="grid grid-cols-1 md:grid-cols-2 gap-4"> -->
        <div v-if="data.audioVideo" class="p-3 rounded-md">
          <div class="text-14px font-medium mb-1">相关音视频</div>
          <div class="text-12px text-gray-600">
            <UploadFiles preview v-model="data.audioVideo" />
          </div>
        </div>
        <div v-if="data.relatedDocs" class="p-3 rounded-md">
          <div class="text-14px font-medium mb-1">相关文档</div>
          <UploadFiles preview v-model="data.audioVideo" />
        </div>
        <!-- </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Descriptions } from '@/components'
import { defineProps, computed } from 'vue'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getBasicInfoSchemas } from '../schemas/knowledgeSchemas'
import { UploadFiles } from '@/components/UploadFile'
const basicSchemas = getBasicInfoSchemas()
const { allSchemas } = useCrudSchemas(basicSchemas)

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})
const data = computed(() => props.data)
</script>

<style lang="scss" scoped></style>
