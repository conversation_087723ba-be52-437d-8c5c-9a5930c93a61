import { ref, unref, nextTick } from 'vue'
import { FormSchema, FormLinkageConfig, FormSetPropsType } from '@/types/form'
import { ComponentOptions } from '@/types/components'

export const useFormLinkage = () => {
  // 存储所有联动配置，key为触发字段，value为联动配置数组
  const linkageMap = ref<Map<string, FormLinkageConfig[]>>(new Map())

  /**
   * 初始化联动配置
   * @param schema 表单schema配置
   */
  const initLinkage = (schema: FormSchema[]) => {
    const map = new Map<string, FormLinkageConfig[]>()

    schema.forEach((item) => {
      if (item.linkage) {
        const linkages = Array.isArray(item.linkage) ? item.linkage : [item.linkage]

        linkages.forEach((linkage) => {
          if (!map.has(linkage.triggerField)) {
            map.set(linkage.triggerField, [])
          }
          map.get(linkage.triggerField)!.push({
            ...linkage,
            clearValue: linkage.clearValue !== false,
            clearOptions: linkage.clearOptions !== false
          })
        })
      }
    })

    linkageMap.value = map
  }

  /**
   * 初始化表单联动选项（基于表单现有数据）
   * @param formModel 表单数据
   * @param setSchema 设置schema的方法
   */
  const initializeLinkageOptions = async (
    formModel: Recordable,
    setSchema: (schemaProps: FormSetPropsType[]) => void
  ) => {
    const linkageEntries = Array.from(unref(linkageMap).entries())

    // 并行处理所有有值的触发字段
    const promises = linkageEntries.map(async ([triggerField, linkages]) => {
      const triggerValue = formModel[triggerField]

      // 如果触发字段有值，就初始化其对应的目标字段选项
      if (triggerValue != null && triggerValue !== '') {
        const linkagePromises = linkages.map(async (linkage) => {
          try {
            // 检查联动条件
            if (linkage.condition && !linkage.condition(triggerValue, formModel)) {
              return
            }

            // 调用API获取选项，传入完整的表单数据
            const res = await linkage.api(triggerValue, formModel)
            let options: ComponentOptions[] = []

            if (res) {
              if (linkage.optionMapper) {
                // 如果有自定义映射函数，直接使用API返回的数据
                const rawData = res.data || res.list || res
                options = linkage.optionMapper(rawData)
              } else {
                // 默认映射逻辑 - 支持多种数据格式
                let rawData: any[] = []

                if (Array.isArray(res)) {
                  // 直接返回数组格式
                  rawData = res
                } else if (res.data && Array.isArray(res.data)) {
                  // { data: [] } 格式
                  rawData = res.data
                } else if (res.list && Array.isArray(res.list)) {
                  // { list: [] } 格式
                  rawData = res.list
                }

                options = rawData.map((item: any) => ({
                  label: item.name || item.label || item.title,
                  value: item.id || item.value || item.code,
                  ...item
                }))
              }
            }

            // 更新目标字段的选项
            setSchema([
              {
                field: linkage.targetField,
                path: 'componentProps.options',
                value: options
              }
            ])
          } catch (error) {
            console.error(`初始化联动失败 ${triggerField} -> ${linkage.targetField}:`, error)
          }
        })

        await Promise.all(linkagePromises)
      }
    })

    await Promise.all(promises)
  }

  /**
   * 处理字段变化联动
   * @param triggerField 触发字段名
   * @param triggerValue 触发字段的值
   * @param formModel 当前表单数据
   * @param setValues 设置表单值的方法
   * @param setSchema 设置schema的方法
   */
  const handleLinkage = async (
    triggerField: string,
    triggerValue: any,
    formModel: Recordable,
    setValues: (data: Recordable) => void,
    setSchema: (schemaProps: FormSetPropsType[]) => void
  ) => {
    const linkages = unref(linkageMap).get(triggerField)
    if (!linkages) return

    // 处理所有相关联动
    const promises = linkages.map(async (linkage) => {
      try {
        // 检查联动条件
        if (linkage.condition && !linkage.condition(triggerValue, formModel)) {
          return
        }

        // 清空目标字段的值
        if (linkage.clearValue) {
          setValues({ [linkage.targetField]: '' })
        }

        // 如果触发值为空，清空选项
        if (!triggerValue) {
          if (linkage.clearOptions) {
            setSchema([
              {
                field: linkage.targetField,
                path: 'componentProps.options',
                value: []
              }
            ])
          }
          return
        }

        // 调用API获取新选项
        const res = await linkage.api(triggerValue, formModel)
        let options: ComponentOptions[] = []

        if (res) {
          if (linkage.optionMapper) {
            // 如果有自定义映射函数，直接使用API返回的数据
            const rawData = res.data || res.list || res
            options = linkage.optionMapper(rawData)
          } else {
            // 默认映射逻辑 - 支持多种数据格式
            let rawData: any[] = []

            if (Array.isArray(res)) {
              // 直接返回数组格式
              rawData = res
            } else if (res.data && Array.isArray(res.data)) {
              // { data: [] } 格式
              rawData = res.data
            } else if (res.list && Array.isArray(res.list)) {
              // { list: [] } 格式
              rawData = res.list
            }

            options = rawData.map((item: any) => ({
              label: item.name || item.label || item.title,
              value: item.id || item.value || item.code,
              ...item
            }))
          }
        }

        // 更新目标字段的选项
        setSchema([
          {
            field: linkage.targetField,
            path: 'componentProps.options',
            value: options
          }
        ])
      } catch (error) {
        console.error(`字段联动失败 ${triggerField} -> ${linkage.targetField}:`, error)
      }
    })

    await Promise.all(promises)
  }

  return {
    initLinkage,
    initializeLinkageOptions,
    handleLinkage,
    linkageMap: unref(linkageMap)
  }
}
