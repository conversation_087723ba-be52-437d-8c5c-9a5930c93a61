<template>
  <div class="popular-law-knowledge-list">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="allSchemas.searchSchema"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
          :model="searchParams"
        />
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button type="primary" @click="handleExport">导出</Button>
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="allSchemas.tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <!-- 知识简介列 -->
        <template #summary="{ row }">
          <div class="max-w-200px">
            <el-tooltip :content="row.summary" placement="top" :show-after="500">
              <span class="text-truncate">{{ row.summary }}</span>
            </el-tooltip>
          </div>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleView(row)">详情</Button>
          <Button link type="primary" @click="handleEdit(row)">编辑</Button>
          <Button link type="danger" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>
    </ContentWrap>

    <!-- 新增/编辑弹窗 -->
    <UpdateKnowledge ref="updateRef" @close="handleUpdateClose" @save="handleSaveSuccess" />

    <!-- 详情弹窗 -->
    <KnowledgeDetail ref="detailRef" @close="handleDetailClose" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getPopularLawKnowledgeListApi,
  deletePopularLawKnowledgeApi
} from '@/api/law/knowledge/popularLaw'
import { knowledgeSchemas } from './schemas/knowledgeSchemas'
import { ElMessage, ElMessageBox } from 'element-plus'
import UpdateKnowledge from './components/UpdateKnowledge.vue'
import KnowledgeDetail from './components/KnowledgeDetail.vue'

// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(knowledgeSchemas)

// 搜索组件引用
const searchRef = ref()

// 搜索参数
const searchParams = ref({})

// 使用表格hook
const {
  tableRegister,
  tableMethods: { getList: getTableList, setSearchParams },
  tableObject
} = useTable({
  getListApi: getPopularLawKnowledgeListApi,
  immediate: true
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 弹窗状态
const updateRef = ref()
const detailRef = ref()

// 导出
const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

// 新建
const handleAdd = () => {
  updateRef.value.open('add', {})
}

// 关闭新增/编辑弹窗
const handleUpdateClose = () => {
  // 弹窗关闭逻辑
}

// 保存成功回调
const handleSaveSuccess = () => {
  getTableList()
}

// 查看详情
const handleView = (row) => {
  detailRef.value.open(row)
}

// 关闭详情弹窗
const handleDetailClose = () => {
  // 详情弹窗关闭逻辑
}

// 编辑
const handleEdit = (row) => {
  updateRef.value.open('edit', row)
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除知识"${row.title}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deletePopularLawKnowledgeApi(row.id)
      ElMessage.success('删除成功')
      getTableList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.popular-law-knowledge-list {
  height: 100%;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
</style>
