import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { DICT_TYPE } from '@/utils/dict'
/**
 * 内部法律顾问管理 - 统一Schema配置
 * 通过 isSearch、isTable、isForm、isDetail 控制字段在不同场景下的显示
 */

export const interiorTeamSchemas: CrudSchema[] = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    isTable: true,
    isSearch: false,
    isForm: false
  },
  // ==================== 基础信息组 ====================
  {
    field: 'lawyerName',
    label: '姓名',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名'
      }
    },
    table: {
      // width: 100
    },
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'belongUnitId',
    label: '所属单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DeptTreeSelect',
      componentProps: {
        placeholder: '请输入所属单位'
      }
    },
    table: {
      formatter: (row: any) => {
        return row.belongUnitName
      }
    },
    detail: {
      formatter: (field, row: any) => {
        return row.belongUnitName
      }
    },
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'birthDate',
    label: '出生年月',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'DatePicker',
      componentProps: {
        style: { width: '300px' },
        type: 'month',
        format: 'YYYY-MM',
        valueFormat: 'x',
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'gender',
    label: '性别',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.SYSTEM_USER_SEX,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'ethnicity',
    label: '民族',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.NATION,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true,
        filterable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'position',
    label: '职务职级',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.POSITION,
    table: {
      // width: 120
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'workNature',
    label: '工作性质',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.WORK_NATURE,
    table: {
      // width: 100
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        // options: [
        //   { label: '全职', value: '全职' },
        //   { label: '兼职', value: '兼职' }
        // ],
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'professionalFields',
    label: '专业领域',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.PROFESSIONAL_FIELDS,
    table: {
      // width: 120
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        multiple: true,
        collapseTags: true,
        maxCollapseTags: 2,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'politicalAffiliation',
    label: '政治面貌',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.POLITICAL_STATUS,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'status',
    label: '在岗状态',
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.ON_DUTY_STATUS,
    table: {
      // width: 100
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'contactPhone',
    label: '联系方式',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      // width: 120
    },
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'practiceYear',
    label: '开始执业年份',
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      // width: 120
    },
    form: {
      component: 'DatePicker',
      componentProps: {
        style: { width: '300px' },
        type: 'year',
        format: 'YYYY',
        valueFormat: 'YYYY',
        clearable: true
      },
      colProps: { span: 12 }
    }
  },
  {
    field: 'remark',
    label: '备注',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        type: 'textarea',
        rows: 4,
        maxlength: 500,
        clearable: true
      },
      colProps: { span: 24 }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'attachment',
    label: '附件',
    isTable: false,
    isForm: true,
    isDetail: false, // 详情页面特殊处理
    form: {
      component: 'UploadFiles',
      componentProps: {
        // placeholder: '附件上传功能开发中...'
      },
      colProps: { span: 24 }
    }
  },

  // ==================== 时间信息组 ====================
  {
    field: 'createTime',
    label: '创建时间',
    isTable: false,
    isDetail: true,
    isSearch: false,
    isForm: false,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'updateTime',
    label: '更新时间',
    isTable: false,
    isDetail: true,
    isSearch: false,
    isForm: false,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },

  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isTable: true,
    isSearch: false,
    isForm: false,
    table: {
      width: 220,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于详情页面分组显示）
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return interiorTeamSchemas.filter((schema) =>
    [
      'name',
      'organization',
      'birthDate',
      'gender',
      'nation',
      'position',
      'workType',
      'specialty',
      'politicalStatus',
      'workStatus',
      'contact',
      'practiceYear',
      'remark',
      'createTime',
      'updateTime'
    ].includes(schema.field)
  )
}

/**
 * 获取表单验证规则
 */
export const getInteriorTeamFormRules = () => {
  return {
    lawyerName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    belongUnitId: [{ required: true, message: '请选择所属单位', trigger: 'blur' }],
    birthDate: [{ required: true, message: '请选择出生年月', trigger: 'blur' }],
    gender: [{ required: true, message: '请选择性别', trigger: 'blur' }],
    ethnicity: [{ required: true, message: '请选择民族', trigger: 'blur' }],
    position: [{ required: true, message: '请输入职务职级', trigger: 'blur' }],
    workNature: [{ required: true, message: '请输入工作性质', trigger: 'blur' }],
    professionalFields: [{ required: true, message: '请输入专业领域', trigger: 'blur' }],
    politicalAffiliation: [{ required: true, message: '请选择政治面貌', trigger: 'blur' }],
    status: [{ required: true, message: '请选择在岗状态', trigger: 'blur' }],
    contactPhone: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
    practiceYear: [{ required: true, message: '请选择开始执业年份', trigger: 'blur' }]
  }
}
