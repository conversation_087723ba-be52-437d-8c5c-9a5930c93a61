/*
 * @Description: 内部队伍管理相关接口
 */
import request from '@/config/axios'

// 内部队伍数据类型
export interface InteriorTeamItem {
  id?: number
  name: string // 姓名
  organization: string // 所属单位
  birthDate: string // 出生年月
  gender: string // 性别
  nation: string // 民族
  position: string // 职务职级
  workType: string // 工作性质
  specialty: string // 专业领域
  politicalStatus: string // 政治面貌
  workStatus: string // 在岗/离岗
  contact: string // 联系方式
  practiceYear: string // 开始执业年份
  remark?: string // 备注
  attachments?: string[] // 附件
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 内部队伍操作日志类型
export interface InteriorTeamActionLogItem {
  id: number
  time: string // 操作时间
  actionName: string // 操作人
  action: string // 操作类型
  remark?: string // 备注信息
}

// 内部队伍查询参数类型
export interface InteriorTeamQueryParams {
  name?: string
  organization?: string
  specialty?: string
  workType?: string
  workStatus?: string
  pageNum?: number
  pageSize?: number
}

// 内部队伍列表响应类型
export interface InteriorTeamListResponse {
  list: InteriorTeamItem[]
  total: number
}

// 获取内部队伍列表
export const getInteriorTeamListApi = (params: InteriorTeamQueryParams) => {
  return request.get<InteriorTeamListResponse>({
    url: '/law/internal-team/page',
    params
  })
}

// 获取内部队伍详情
export const getInteriorTeamDetailApi = (id: number) => {
  return request.get<InteriorTeamItem>({
    url: '/law/internal-team/get',
    params: { id }
  })
}

// 删除内部队伍
export const deleteInteriorTeamApi = (id: number) => {
  return request.delete({
    url: '/law/internal-team/delete',
    params: { id }
  })
}

// 新增内部队伍
export const addInteriorTeamApi = (data: Partial<InteriorTeamItem>) => {
  return request.post({
    url: '/law/internal-team/create',
    data
  })
}

// 编辑内部队伍
export const updateInteriorTeamApi = (data: Partial<InteriorTeamItem>) => {
  return request.put({
    url: '/law/internal-team/update',
    data
  })
}
//导出内部队伍
export const exportInteriorTeamApi = (params: InteriorTeamQueryParams) => {
  return request.download({
    url: '/law/internal-team/export-excel',
    params
  })
}
