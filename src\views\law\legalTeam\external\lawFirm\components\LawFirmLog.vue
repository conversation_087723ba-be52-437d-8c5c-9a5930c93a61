<template>
  <div class="">
    <Title title="操作日志">
      <Table :columns="tableColumns" :data="tableData" v-loading="loading"></Table>
    </Title>
  </div>
</template>

<script setup lang="ts">
import { Title, Table } from '@/components'
import { defineProps, watch, ref, onMounted } from 'vue'
import { getExternalLawFirmActionLog } from '@/api/law/legalTeam/externalLawFirm'
import type { ExternalLawFirmActionLogItem } from '@/api/law/legalTeam/externalLawFirm'

const props = defineProps({
  id: {
    type: Number,
    required: true
  }
})

const tableData = ref<ExternalLawFirmActionLogItem[]>([])
const loading = ref(false)

const tableColumns = [
  {
    label: '时间',
    field: 'time',
    width: '200px'
  },
  {
    label: '操作人',
    field: 'actionName',
    width: '150px'
  },
  {
    label: '操作',
    field: 'action',
    width: '150px'
  },
  {
    label: '备注',
    field: 'remark'
  }
]

const getActionLogData = async (id: number) => {
  if (!id) return

  try {
    loading.value = true
    const res = await getExternalLawFirmActionLog(id)
    tableData.value = res
  } catch (error) {
    console.error('获取操作日志失败:', error)
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 监听id变化
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      getActionLogData(newId)
    }
  },
  { immediate: true }
)

onMounted(() => {
  if (props.id) {
    getActionLogData(props.id)
  }
})
</script>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
