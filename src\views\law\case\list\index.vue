<template>
  <div class="case-list">
    <ContentWrap v-if="!showDetail">
      <!-- 搜索区域 -->
      <div class="flex items-start">
        <Search
          ref="searchRef"
          :schema="allSchemas.searchSchema"
          :show-search="true"
          :show-reset="true"
          :expand="false"
          layout="inline"
          @search="setSearchParams"
          @reset="setSearchParams"
          :model="searchParams"
        />
        <Button type="primary" @click="handleAdd">新建案件</Button>
        <Button type="primary" @click="handleExport">导出</Button>
      </div>

      <!-- 表格区域 -->
      <Table
        ref="tableRef"
        v-model:pageSize="pageSize"
        height="calc(70vh)"
        maxHeight="calc(70vh)"
        v-model:currentPage="currentPage"
        :columns="allSchemas.tableColumns"
        :data="tableList"
        :loading="loading"
        :pagination="{
          total: total
        }"
        @register="tableRegister"
      >
        <!-- 操作列 -->
        <template #action="{ row }">
          <Button link type="primary" @click="handleView(row)">详情</Button>
          <Button link type="primary" @click="handleEdit(row)">编辑</Button>
          <Button link type="danger" @click="handleDelete(row)">删除</Button>
        </template>
      </Table>
    </ContentWrap>

    <!-- <UpdateCase ref="updateRef" @close="handleUpdateClose" @save="handleSaveSuccess" /> -->
    <CaseDetail
      v-if="showDetail"
      ref="caseDetailRef"
      :id="detailId"
      showClose
      @close="handleCaseDetailClose"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { getCaseListApi, deleteCaseApi, getCaseDetailApi, exportCaseApi } from '@/api/law/case'
import { ElMessage, ElMessageBox } from 'element-plus'
// import UpdateCase from './components/UpdateCase.vue'
import CaseDetail from '../detail/index.vue'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { caseSchemas } from '../schemas/caseSchemas'
import { useRouter } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
const { prompt } = useMessage()
const router = useRouter()
// 使用 CRUD Schemas
const { allSchemas } = useCrudSchemas(caseSchemas)

// 搜索组件引用
const searchRef = ref()

// 搜索参数
const searchParams = ref({})

// 使用表格hook
const {
  tableRegister,
  tableMethods: { getList: getTableList, setSearchParams, exportList },
  tableObject
} = useTable({
  getListApi: getCaseListApi,
  exportListApi: exportCaseApi,
  immediate: true
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 弹窗状态
const updateRef = ref()
// 导出
const handleExport = () => {
  prompt('请输入导出文件名', '批量导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    //必填
    inputValidator: (value) => {
      if (!value) {
        return '请输入导出文件名'
      }
      return true
    }
  }).then((fileName) => {
    exportList(fileName.value, false)
  })
}

// 新建
const handleAdd = () => {
  router.push({
    name: 'LawCaseCreate',
    query: {
      mode: 'add'
    }
  })
}

// 关闭弹窗
const handleUpdateClose = () => {
  // updateRef.value.close()
}

// 保存成功回调
const handleSaveSuccess = () => {
  // handleUpdateClose()
  getTableList()
  // ElMessage.success('操作成功')
}
const caseDetailRef = ref()
// 查看详情
const showDetail = ref(false)
const detailId = ref(null)
const handleView = async (row) => {
  detailId.value = row.id
  showDetail.value = true
  // router.push({
  //   path: '/bpm/process-instance/detail',
  //   query: {
  //     id: row.processInstanceId
  //   }
  // })
}
// 关闭案件详情弹窗
const handleCaseDetailClose = () => {
  showDetail.value = false
}

// 编辑
const handleEdit = async (row) => {
  router.push({
    name: 'LawCaseCreate',
    query: {
      mode: 'edit',
      id: row.id
    }
  })
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除案件"${row.caseName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteCaseApi(row.id)
      ElMessage.success('删除成功')
      getTableList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}
onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.case-list {
  height: 100%;
}
</style>
