<template>
  <div class="upload-container">
    <el-upload
      v-model:file-list="fileList"
      :accept="fileType.join(',')"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :class="['upload', drag ? 'no-border' : '', preview ? 'preview-mode' : '']"
      :disabled="disabled || preview"
      :drag="drag && !preview"
      :http-request="httpRequest"
      :limit="limit"
      :multiple="true"
      :on-error="uploadError"
      :on-exceed="handleExceed"
      :on-success="uploadSuccess"
      list-type="picture-card"
    >
      <div v-if="!preview" class="upload-empty">
        <slot name="empty">
          <Icon icon="ep:plus" />
          <!-- <span>请上传文件</span> -->
        </slot>
      </div>
      <template #file="{ file }">
        <div class="file-preview">
          <div class="file-icon-wrapper">
            <Icon :icon="getFileIcon(file.name!)" class="file-icon" />
            <div class="file-name" :title="file.name">{{ file.name }}</div>
          </div>
          <div class="upload-handle" @click.stop>
            <div class="handle-icon" @click="downloadFile(file)">
              <Icon icon="ep:download" />
              <span>下载</span>
            </div>
            <div v-if="!disabled && !preview" class="handle-icon" @click="handleRemove(file)">
              <Icon icon="ep:delete" />
              <span>删除</span>
            </div>
          </div>
        </div>
      </template>
    </el-upload>
    <div v-if="!preview" class="el-upload__tip">
      <slot name="tip"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UploadFile, UploadProps, UploadUserFile } from 'element-plus'
import { ElNotification } from 'element-plus'

import { propTypes } from '@/utils/propTypes'
import { useUpload } from '@/components/UploadFile/src/useUpload'
import { useMessage } from '@/hooks/web/useMessage'
import { isString } from '@/utils/is'
defineOptions({ name: 'UploadFiles' })

const message = useMessage() // 消息弹窗

// 文件下载
const downloadFile = (file: UploadFile) => {
  if (file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name || '文件'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop() || ''
  const iconMap: Record<string, string> = {
    // 文档类
    pdf: 'ep:document',
    doc: 'ep:document',
    docx: 'ep:document',
    xls: 'ep:document',
    xlsx: 'ep:document',
    ppt: 'ep:document',
    pptx: 'ep:document',
    txt: 'ep:document',
    rtf: 'ep:document',

    // 压缩包
    zip: 'ep:box',
    rar: 'ep:box',
    '7z': 'ep:box',
    tar: 'ep:box',
    gz: 'ep:box',

    // 图片
    jpg: 'ep:picture',
    jpeg: 'ep:picture',
    png: 'ep:picture',
    gif: 'ep:picture',
    bmp: 'ep:picture',
    svg: 'ep:picture',
    webp: 'ep:picture',

    // 音视频
    mp4: 'ep:video-camera',
    avi: 'ep:video-camera',
    mov: 'ep:video-camera',
    wmv: 'ep:video-camera',
    flv: 'ep:video-camera',
    mp3: 'ep:microphone',
    wav: 'ep:microphone',
    flac: 'ep:microphone',

    // 代码文件
    js: 'ep:document',
    ts: 'ep:document',
    html: 'ep:document',
    css: 'ep:document',
    vue: 'ep:document',
    java: 'ep:document',
    py: 'ep:document',

    // 其他
    xml: 'ep:document',
    json: 'ep:document'
  }

  return iconMap[ext] || 'ep:document'
}

const props = defineProps({
  modelValue: propTypes.oneOfType<string | string[]>([String, Array<String>]).isRequired,
  drag: propTypes.bool.def(true), // 是否支持拖拽上传 ==> 非必传（默认为 true）
  disabled: propTypes.bool.def(false), // 是否禁用上传组件 ==> 非必传（默认为 false）
  preview: propTypes.bool.def(false), // 是否为预览模式 ==> 非必传（默认为 false）
  limit: propTypes.number.def(5), // 最大文件上传数 ==> 非必传（默认为 5个）
  fileSize: propTypes.number.def(10), // 文件大小限制 ==> 非必传（默认为 10M）
  fileType: propTypes.array.def([
    '.doc',
    '.docx',
    '.pdf',
    '.txt',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
    '.zip',
    '.rar',
    '.7z',
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.mp4',
    '.mp3'
  ]), // 文件类型限制 ==> 非必传
  height: propTypes.string.def('150px'), // 组件高度 ==> 非必传（默认为 150px）
  width: propTypes.string.def('150px'), // 组件宽度 ==> 非必传（默认为 150px）
  borderradius: propTypes.string.def('8px'), // 组件边框圆角 ==> 非必传（默认为 8px）
  directory: propTypes.string.def(undefined), // 上传目录 ==> 非必传（默认为 undefined）
  returnType: propTypes.string.def('string') // 返回值类型 ==> 非必传（默认为 'string'，可选 'array'）
})

const { uploadUrl, httpRequest } = useUpload(props.directory)

const fileList = ref<UploadUserFile[]>([])
const uploadNumber = ref<number>(0)
const uploadList = ref<UploadUserFile[]>([])

/**
 * @description 文件上传之前判断
 * @param rawFile 上传的文件
 * */
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const fileSize = rawFile.size / 1024 / 1024 < props.fileSize
  const fileName = rawFile.name.toLowerCase()
  const fileExt = '.' + fileName.split('.').pop()

  if (!props.fileType.includes(fileExt)) {
    ElNotification({
      title: '温馨提示',
      message: '上传文件不符合所需的格式！',
      type: 'warning'
    })
  }
  if (!fileSize) {
    ElNotification({
      title: '温馨提示',
      message: `上传文件大小不能超过 ${props.fileSize}M！`,
      type: 'warning'
    })
  }
  uploadNumber.value++
  return props.fileType.includes(fileExt) && fileSize
}

// 文件上传成功
interface UploadEmits {
  (e: 'update:modelValue', value: string | string[]): void
}

const emit = defineEmits<UploadEmits>()
const uploadSuccess: UploadProps['onSuccess'] = (res: any): void => {
  message.success('上传成功')
  // 删除自身
  const index = fileList.value.findIndex((item) => (item.response as any)?.data === res.data)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
  uploadList.value.push({ name: res.data, url: res.data })
  if (uploadList.value.length == uploadNumber.value) {
    fileList.value.push(...uploadList.value)
    uploadList.value = []
    uploadNumber.value = 0
    emitUpdateModelValue()
  }
}

// 监听模型绑定值变动
watch(
  () => props.modelValue,
  (val: string | string[]) => {
    if (!val) {
      fileList.value = [] // fix：处理掉缓存，表单重置后上传组件的内容并没有重置
      return
    }
    fileList.value = [] // 保障数据为空
    // 情况1：字符串
    if (isString(val)) {
      fileList.value.push(
        ...val.split(',').map((url) => ({ name: url.substring(url.lastIndexOf('/') + 1), url }))
      )
      return
    }
    // 情况2：数组
    fileList.value.push(
      ...(val as string[]).map((url) => ({ name: url.substring(url.lastIndexOf('/') + 1), url }))
    )
  },
  { immediate: true, deep: true }
)
// 发送文件链接列表更新
const emitUpdateModelValue = () => {
  // 获取文件链接列表
  let result: string | string[] = fileList.value.map((file) => file.url!)

  // 根据 returnType 决定返回格式
  if (props.returnType === 'string' || (props.limit === 1 && props.returnType !== 'array')) {
    // 字符串拼接模式（英文逗号分隔）
    result = result.join(',')
  }
  // 否则返回数组模式

  console.log(result, '  result')

  emit('update:modelValue', result)
}

// 删除上传文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.map((f) => f.name).indexOf(file.name)
  if (index > -1) {
    fileList.value.splice(index, 1)
    emitUpdateModelValue()
  }
}

// 文件上传错误提示
const uploadError = () => {
  ElNotification({
    title: '温馨提示',
    message: '文件上传失败，请您重新上传！',
    type: 'error'
  })
}

// 文件数超出提示
const handleExceed = () => {
  ElNotification({
    title: '温馨提示',
    message: `当前最多只能上传 ${props.limit} 个文件，请移除后上传！`,
    type: 'warning'
  })
}
</script>

<style lang="scss" scoped>
.is-error {
  .upload {
    :deep(.el-upload--picture-card),
    :deep(.el-upload-dragger) {
      border: 1px dashed var(--el-color-danger) !important;

      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}

:deep(.disabled) {
  .el-upload--picture-card,
  .el-upload-dragger {
    cursor: not-allowed;
    background: var(--el-disabled-bg-color) !important;
    border: 1px dashed var(--el-border-color-darker);

    &:hover {
      border-color: var(--el-border-color-darker) !important;
    }
  }
}

.upload-container {
  width: 100% !important;
  :deep(.el-upload--picture-card) {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    gap: 8px !important;

    .el-upload-list {
      display: flex !important;
      flex-direction: row !important;
      flex-wrap: wrap !important;
      margin: 0 !important;
    }

    .el-upload-list__item {
      margin: 0 !important;
    }
  }

  .no-border {
    :deep(.el-upload--picture-card) {
      border: none !important;
    }
  }

  .preview-mode {
    :deep(.el-upload--picture-card) {
      display: none !important;
    }
  }

  :deep(.upload) {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
    align-items: flex-start !important;

    .el-upload-list {
      display: flex !important;
      flex-wrap: wrap !important;
      gap: 8px !important;
      margin: 0 !important;
      order: 1;
    }

    .el-upload--picture-card {
      order: 0 !important; // 确保上传图标在最前面
      margin: 0 !important;
    }

    .el-upload-dragger {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 0;
      overflow: hidden;
      border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderradius);

      &:hover {
        border: 1px dashed var(--el-color-primary);
      }
    }

    .el-upload-dragger.is-dragover {
      background-color: var(--el-color-primary-light-9);
      border: 2px dashed var(--el-color-primary) !important;
    }

    .el-upload-list__item,
    .el-upload--picture-card {
      width: v-bind(width);
      height: v-bind(height);
      background-color: transparent;
      border-radius: v-bind(borderradius);
      margin: 0 !important;
      display: inline-block !important;
      vertical-align: top !important;
    }

    // 强制覆盖 Element Plus 的默认样式
    &.el-upload--picture-card {
      display: flex !important;
      flex-direction: row !important;
      flex-wrap: wrap !important;

      .el-upload-list {
        display: flex !important;
        flex-direction: row !important;
        flex-wrap: wrap !important;
      }
    }

    .file-preview {
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: var(--el-fill-color-light);
      border-radius: v-bind(borderradius);
      overflow: hidden;

      .file-icon-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 10px;
        text-align: center;

        .file-icon {
          font-size: 28px;
          color: var(--el-color-primary);
          margin-bottom: 8px;
        }

        .file-name {
          font-size: 12px;
          color: var(--el-text-color-regular);
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          line-height: 1.2;
        }
      }
    }

    .upload-handle {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      width: 100%;
      height: 100%;
      cursor: pointer;
      background: rgb(0 0 0 / 60%);
      opacity: 0;
      box-sizing: border-box;
      transition: var(--el-transition-duration-fast);
      align-items: center;
      justify-content: center;

      .handle-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 6%;
        color: aliceblue;

        .el-icon {
          margin-bottom: 15%;
          font-size: 140%;
        }

        span {
          font-size: 100%;
        }
      }
    }

    .el-upload-list__item {
      &:hover {
        .upload-handle {
          opacity: 1;
        }
      }
    }

    .upload-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      line-height: 30px;
      color: var(--el-color-info);

      .el-icon {
        font-size: 28px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .el-upload__tip {
    line-height: 15px;
    text-align: center;
  }
}
</style>
