import request from '@/config/axios'

// 内控流程类型管理接口

/**
 * 内控流程类型数据结构
 */
export interface TypeManagVO {
  id?: number
  type: string // 内控流程类型
  typeCode: string // 流程类型编号
  belongUnitId: number // 所属单位ID
  belongUnitName?: string // 所属单位名称
  status: number // 状态 0-禁用 1-启用
  description?: string // 描述
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

/**
 * 查询参数
 */
export interface TypeManagPageReqVO {
  pageNo: number
  pageSize: number
  type?: string
  companyId?: number
  status?: number
}

/**
 * 获取内控流程类型分页列表
 */
export const getTypeManagListApi = (params: TypeManagPageReqVO) => {
  return request.get({
    url: 'ic/process-type/page',
    params
  })
}

/**
 * 获取内控流程类型详情
 */
export const getTypeManagDetailApi = (id: number) => {
  return request.get({
    url: `/ic/process-type/get-detail`,
    params: { id }
  })
}

/**
 * 创建内控流程类型
 */
export const createTypeManagApi = (data: TypeManagVO) => {
  return request.post({
    url: '/ic/process-type/create',
    data
  })
}

/**
 * 更新内控流程类型
 */
export const updateTypeManagApi = (data: TypeManagVO) => {
  return request.put({
    url: '/ic/process-type/update',
    data
  })
}

/**
 * 删除内控流程类型
 */
export const deleteTypeManagApi = (id: number) => {
  return request.delete({
    url: `/ic/process-type/delete`,
    params: { id }
  })
}

/**
 * 导出内控流程类型
 */
export const exportTypeManagApi = (params: TypeManagPageReqVO) => {
  return request.download({
    url: '/ic/process-type/export-excel',
    params
  })
}
