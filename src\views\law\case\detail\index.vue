<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import CaseInfo from './components/CaseInfo.vue'
import { LogList } from '@/components/LogList'
const props = defineProps({
  id: {
    type: [Number, String],
    default: undefined
  },
  showClose: {
    type: Boolean,
    default: false
  }
})

const mode = ref('view') // 模式：add, edit, view, approval
const editId = ref<number | undefined>(undefined)

const title = computed(() => {
  switch (mode.value) {
    case 'add':
      return '新增案件'
    case 'edit':
      return '编辑案件'
    case 'view':
      return '查看案件'
    case 'approval':
      return '案件审批'
    default:
      return ''
  }
})

const emit = defineEmits(['close', 'save'])

// 添加open方法供外部调用
const open = (modeValue: string, id?: number | string) => {
  mode.value = modeValue
  editId.value = id ? Number(id) : undefined
  activName.value = '案件信息'
}

// 暴露open方法给外部组件使用
defineExpose({
  open
})

onMounted(() => {
  editId.value = props.id ? Number(props.id) : undefined
  activName.value = '案件信息'
})

const tabList = [
  {
    name: '案件信息',
    component: CaseInfo
  },
  // {
  //   name: '审批流程',
  //   component: ApprovalProcess
  // },
  {
    name: '操作日志',
    component: LogList
  }
]

const activName = ref('案件信息')
const handleClose = () => {
  emit('close')
}
// }
</script>
<template>
  <div class="h-full flex flex-col bg-white">
    <div class="w-full h-auto flex-shrink-0 p-4 border-b">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <h2 class="text-lg font-semibold">{{ title }}</h2>
        </div>
        <div class="flex items-center space-x-4">
          <!-- <div class="flex items-center">
            <div class="mr-2">当前状态：</div>
            <dict-tag :type="DICT_TYPE.APPROVAL_TYPE" value="1" size="large" />
          </div>
          <div class="flex space-x-2" v-if="mode === 'approval'">
            <Button type="primary" @click="handleApprove('1')">通过</Button>
            <Button type="danger" @click="handleApprove('0')">驳回</Button>
          </div> -->
          <Button @click="handleClose" v-if="showClose">返回</Button>
        </div>
      </div>
    </div>

    <div class="flex-1 overflow-hidden">
      <ElTabs type="border-card" v-model="activName" class="h-full">
        <ElTabPane v-for="item in tabList" :key="item.name" :label="item.name" :name="item.name">
          <div class="h-full overflow-y-auto">
            <component :is="item.component" :id="editId" :type="1" />
          </div>
        </ElTabPane>
      </ElTabs>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
