import request from '@/config/axios'

// 评价计划管理接口

/**
 * 附件数据结构
 */
export interface AttachmentVO {
  id?: number
  fileName: string // 文件名称
  fileUrl: string // 文件URL
  fileSize: number // 文件大小
  uploadTime: string // 上传时间
  uploader: string // 上传人
  version: string // 版本号
  isActive: boolean // 是否启用
}

/**
 * 评价任务范围数据结构
 */
export interface EvaluationTaskRangeVO {
  id?: number
  relatedNode: string // 关联节点
  relatedFlow: string // 关联流程
  controlFunctionName: string // 控制措施名称
  controlFunctionDesc: string // 控制措施描述
  controlLevel: string // 控制级别
  controlType: string // 控制类型
  responsibleDept: string // 责任部门
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

/**
 * 评价计划数据结构
 */
export interface EvaluationPlanVO {
  id?: number
  planName: string // 评价计划名称
  planYear: string // 评价计划年度
  evaluationUnit: string[] // 评价单位
  evaluatedUnit: string[] // 被评价单位
  planTimeRange: string[] // 评价计划时间范围
  planDescription: string // 评价计划描述
  taskRanges: EvaluationTaskRangeVO[] // 评价任务范围
  taskRangeCount: number // 评价任务范围数量
  attachments?: AttachmentVO[] // 相关附件
  status: number // 状态（0-草稿，1-进行中，2-已完成，3-已暂停）
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

/**
 * 评价计划分页查询参数
 */
export interface EvaluationPlanPageReqVO {
  pageNum: number
  pageSize: number
  planName?: string // 评价计划名称
  evaluationUnit?: string // 评价单位
  evaluatedUnit?: string // 被评价单位
  planStatus?: number // 计划状态
  planYear?: string // 选择年度
  timeRange?: string[] // 时间范围
}

/**
 * 评价任务范围分页查询参数
 */
export interface TaskRangePageReqVO {
  pageNum: number
  pageSize: number
  keyword?: string // 关键字搜索
  controlFunctionName?: string // 内控措施名称
  controlLevel?: string // 控制级别
  controlType?: string // 控制类型
  flowType?: string // 流程类型
}

/**
 * 获取评价计划分页列表
 */
export const getEvaluationPlanListApi = (params: EvaluationPlanPageReqVO) => {
  return request.get({
    url: '/ic/evaluation-plan/page',
    params
  })
}

/**
 * 获取评价计划详情
 */
export const getEvaluationPlanDetailApi = (id: number) => {
  return request.get({
    url: `/ic/evaluation-plan/get`,
    params: { id }
  })
}

/**
 * 创建评价计划
 */
export const createEvaluationPlanApi = (data: EvaluationPlanVO) => {
  return request.post({
    url: 'ic/evaluation-plan/create',
    data
  })
}

/**
 * 更新评价计划
 */
export const updateEvaluationPlanApi = (data: EvaluationPlanVO) => {
  return request.put({
    url: '/ic/evaluation-plan/update',
    data
  })
}

/**
 * 删除评价计划
 */
export const deleteEvaluationPlanApi = (id: number) => {
  return request.delete({
    url: `/ic/evaluation-plan/delete`,
    params: { id }
  })
}

/**
 * 导出评价计划
 */
export const exportEvaluationPlanApi = (params: any) => {
  return request.get({
    url: 'ic/evaluation-plan/export-excel',
    params
  })
}

/**
 * 获取评价任务范围分页列表
 */
export const getTaskRangeListApi = (params: TaskRangePageReqVO) => {
  return request.get({
    url: '/api/ICM/evaluate/taskRange/page',
    params
  })
}

/**
 * 获取流程类型树形结构
 */
export const getFlowTypeTreeApi = () => {
  return request.get({
    url: '/api/ICM/evaluate/flowType/tree'
  })
}

/**
 * 获取部门选项列表
 */
export const getDepartmentOptionsApi = () => {
  return request.get({
    url: '/api/ICM/evaluate/department/options'
  })
}

/**
 * 获取单位选项列表
 */
export const getUnitOptionsApi = () => {
  return request.get({
    url: '/api/ICM/evaluate/unit/options'
  })
}
