<template>
  <div v-loading="loading">
    <!-- 基础信息 -->
    <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
    <div class="mt-12px">
      <Descriptions :schema="basicDetailConfig" :data="detailData" :columns="2" />
    </div>

    <!-- 专业领域展示 -->
    <div v-if="detailData.professionalFields" class="mt-4">
      <div class="bg-#f1f2fe lh-40px pl-5">专业领域</div>
      <div class="mt-12px p-4">
        <div class="flex flex-wrap gap-2">
          <DictTag
            v-for="(specialty, index) in detailData.professionalFields.split(',')"
            :key="index"
            :type="DICT_TYPE.PROFESSIONAL_FIELDS"
            :value="specialty"
          >
          </DictTag>
        </div>
      </div>
    </div>

    <!-- 评价信息 -->
    <div class="mt-4">
      <div class="bg-#f1f2fe lh-40px pl-5">评价信息</div>
      <div class="mt-12px p-4">
        <!-- 评分展示 -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium">态度评分：</span>
            <el-rate
              v-model="detailData.attitudeRating"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}分"
            />
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium">能力评分：</span>
            <el-rate
              v-model="detailData.abilityRating"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}分"
            />
          </div>
        </div>

        <!-- 其他评价信息 -->
        <Descriptions :schema="evaluationDetailConfig" :data="detailData" :columns="1" />
      </div>
    </div>

    <!-- 附件展示 -->
    <div class="mt-4">
      <div class="bg-#f1f2fe lh-40px pl-5">附件信息</div>
      <div class="mt-12px p-4">
        <UploadFiles preview v-model="detailData.attachment" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Descriptions } from '@/components'
import { defineProps, watch, ref } from 'vue'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getExternalLawyerDetailApi } from '@/api/law/legalTeam/externalLawyer'
import {
  lawyerSchemas,
  getBasicInfoSchemas,
  getEvaluationInfoSchemas
} from '../schemas/lawyerSchemas'
import type { ExternalLawyerItem } from '@/api/law/legalTeam/externalLawyer'
import { DICT_TYPE } from '@/utils/dict'
// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(lawyerSchemas)

const props = defineProps({
  id: {
    type: Number,
    required: true
  }
})

const detailData = ref<ExternalLawyerItem>({} as ExternalLawyerItem)
const loading = ref(false)

// 基础信息配置 - 使用统一Schema
const basicDetailConfig = allSchemas.detailSchema.filter((schema) =>
  getBasicInfoSchemas()
    .map((s) => s.field)
    .includes(schema.field)
)

// 评价信息配置 - 使用统一Schema
const evaluationDetailConfig = allSchemas.detailSchema.filter((schema) =>
  getEvaluationInfoSchemas()
    .map((s) => s.field)
    .includes(schema.field)
)

// 获取详情数据
const getDetail = async (id: number) => {
  if (!id) return

  try {
    loading.value = true
    const data = await getExternalLawyerDetailApi(id)
    detailData.value = data
  } catch (error) {
    console.error('获取详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听id变化
watch(
  () => props.id,
  (newId) => {
    if (newId) {
      getDetail(newId)
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
