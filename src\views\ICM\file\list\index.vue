<template>
  <el-row :gutter="20">
    <!-- 左侧文件类型树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <FileTypeTree @node-click="handleFileTypeNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <!-- 搜索区域 -->
      <ContentWrap>
        <div class="flex items-start">
          <Search
            ref="searchRef"
            :schema="allSchemas.searchSchema"
            :show-search="true"
            :show-reset="true"
            :expand="false"
            layout="inline"
            @search="handleSearch"
            @reset="handleReset"
            :model="searchParams"
          />
          <Button type="primary" @click="handleAdd">新增</Button>
          <Button type="primary" @click="handleExport">导出</Button>
        </div>
      </ContentWrap>

      <!-- 表格区域 -->
      <ContentWrap>
        <Table
          ref="tableRef"
          v-model:pageSize="pageSize"
          height="calc(70vh)"
          maxHeight="calc(70vh)"
          v-model:currentPage="currentPage"
          :columns="allSchemas.tableColumns"
          :data="tableList"
          :loading="loading"
          :pagination="{
            total: total
          }"
          @register="tableRegister"
          @refresh="tableMethods.getList"
        >
          <!-- 文件标题列 -->
          <template #title="{ row }">
            <div class="max-w-200px">
              <el-tooltip :content="row.title" placement="top" :show-after="500">
                <span class="text-truncate cursor-pointer text-blue-600" @click="handleView(row)">
                  {{ row.title }}
                </span>
              </el-tooltip>
            </div>
          </template>

          <!-- 文件状态列 -->
          <template #status="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>

          <!-- 当前版本列 -->
          <template #currentVersion="{ row }">
            <el-tag :type="row.currentVersion ? 'success' : 'info'">
              {{ row.currentVersion ? '是' : '否' }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template #action="{ row }">
            <Button link type="primary" @click="handleView(row)">详情</Button>
            <Button link type="primary" @click="handleEdit(row)">修订</Button>
            <Button link type="danger" @click="handleDelete(row)">删除</Button>
          </template>
        </Table>
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 新增/编辑弹窗 -->
  <UpdateFile ref="updateRef" @close="handleUpdateClose" @save="handleSaveSuccess" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getICMFileListApi, deleteICMFileApi, exportICMFileApi } from '@/api/ICM/file'
import { fileSchemas } from './schemas/fileSchemas'
import { FILE_STATUS_MAP, FILE_STATUS_COLOR_MAP } from './constants/fileConstants'
import { ElMessage, ElMessageBox } from 'element-plus'
import FileTypeTree from './components/FileTypeTree.vue'
import UpdateFile from './components/UpdateFile.vue'
// import FileDetail from './components/FileDetail.vue'

defineOptions({ name: 'ICMFileList' })

// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(fileSchemas)

// 搜索组件引用
const searchRef = ref()

// 搜索参数
const searchParams = ref({})

// 文件类型筛选参数
const fileTypeFilter = ref('')

// 获取列表数据的API函数
const getListApi = async () => {
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...searchParams.value
  }

  // 添加文件类型筛选
  if (fileTypeFilter.value) {
    params.fileType = fileTypeFilter.value
  }

  const res = await getICMFileListApi(params)
  return res
}

// 使用表格hook
const { tableRegister, tableMethods, tableObject } = useTable({
  getListApi,
  immediate: true
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 搜索
const handleSearch = (searchData: any) => {
  searchParams.value = { ...searchData }
  tableMethods.getList()
}

// 重置
const handleReset = () => {
  searchParams.value = {}
  tableMethods.getList()
}

// 弹窗状态
const updateRef = ref()
const detailRef = ref()

// 文件类型节点点击
const handleFileTypeNodeClick = (data: any) => {
  if (data && data.type === 'fileType') {
    fileTypeFilter.value = data.value
  } else {
    fileTypeFilter.value = ''
  }
  tableMethods.getList()
}

// 获取状态类型
const getStatusType = (status: number) => {
  return FILE_STATUS_COLOR_MAP[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  return FILE_STATUS_MAP[status] || '未知'
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      ...searchParams.value
    }
    if (fileTypeFilter.value) {
      params.fileType = fileTypeFilter.value
    }

    await exportICMFileApi(params)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 新建
const handleAdd = () => {
  updateRef.value.open('add')
}

// 关闭新增/编辑弹窗
const handleUpdateClose = () => {
  // 弹窗关闭逻辑
}

// 保存成功回调
const handleSaveSuccess = () => {
  tableMethods.getList()
}

// 查看详情
const handleView = (row: any) => {
  detailRef.value.open(row.id)
}

// 关闭详情弹窗
const handleDetailClose = () => {
  // 详情弹窗关闭逻辑
}

// 编辑
const handleEdit = (row: any) => {
  updateRef.value.open('edit', row.id)
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除文件"${row.title}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteICMFileApi(row.id)
      ElMessage.success('删除成功')
      tableMethods.getList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  tableMethods.getList()
})
</script>

<style lang="scss" scoped>
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.cursor-pointer {
  cursor: pointer;
}

.text-blue-600 {
  color: #2563eb;
}

.text-blue-600:hover {
  color: #1d4ed8;
}
</style>
