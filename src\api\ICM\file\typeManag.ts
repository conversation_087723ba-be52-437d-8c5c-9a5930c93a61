import request from '@/config/axios'

// 内控文件类型管理接口

/**
 * 内控文件类型数据结构
 */
export interface TypeManagVO {
  id?: number
  type: string // 内控文件类型
  typeCode: string // 文件类型编号
  belongUnitId: number // 所属单位ID
  belongUnitName?: string // 所属单位名称
  status: number // 状态 0-禁用 1-启用
  description?: string // 描述
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

/**
 * 查询参数
 */
export interface TypeManagPageReqVO {
  pageNum: number
  pageSize: number
  type?: string
  belongUnitId?: number
  status?: number
}

/**
 * 获取内控文件类型分页列表
 */
export const getTypeManagListApi = (params: TypeManagPageReqVO) => {
  return request.get({
    url: 'ic/files-type/page',
    params
  })
}

/**
 * 获取内控文件类型详情
 */
export const getTypeManagDetailApi = (id: number) => {
  return request.get({
    url: `/ic/files-type/get-detail`,
    params: { id }
  })
}

/**
 * 创建内控文件类型
 */
export const createTypeManagApi = (data: TypeManagVO) => {
  return request.post({
    url: '/ic/files-type/create',
    data
  })
}

/**
 * 更新内控文件类型
 */
export const updateTypeManagApi = (data: TypeManagVO) => {
  return request.put({
    url: '/ic/files-type/update',
    data
  })
}

/**
 * 删除内控文件类型
 */
export const deleteTypeManagApi = (id: number) => {
  return request.delete({
    url: `/ic/files-type/delete`,
    params: { id }
  })
}

/**
 * 导出内控文件类型
 */
export const exportTypeManagApi = (params: TypeManagPageReqVO) => {
  return request.download({
    url: '/ic/files-type/export-excel',
    params
  })
}
