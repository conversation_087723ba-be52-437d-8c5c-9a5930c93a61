import { computed } from 'vue'
import { useAppStoreWithOut } from '@/store/modules/app'

/**
 * 使用布局尺寸的composable函数
 * 
 * 使用示例：
 * 
 * 1. 基本用法（使用默认配置）：
 * ```vue
 * <script setup>
 * import { useLayout } from '@/hooks/web/useLayout'
 * 
 * const { getModalStyle, layoutSize, isCollapsed } = useLayout()
 * </script>
 * 
 * <template>
 *   <div class="fixed inset-0 z-999 bg-white" :style="getModalStyle">
 *     <!-- 弹窗内容 -->
 *   </div>
 * </template>
 * ```
 * 
 * 2. 自定义配置：
 * ```vue
 * <script setup>
 * import { useLayout } from '@/hooks/web/useLayout'
 * 
 * const { getModalStyle } = useLayout({
 *   collapsedWidth: 80,    // 自定义折叠宽度
 *   expandedWidth: 240,    // 自定义展开宽度
 *   topBarHeight: 60       // 自定义顶部栏高度
 * })
 * </script>
 * ```
 * 
 * 3. 获取内容区域样式：
 * ```vue
 * <script setup>
 * import { useLayout } from '@/hooks/web/useLayout'
 * 
 * const { getContentStyle } = useLayout()
 * </script>
 * 
 * <template>
 *   <div class="absolute" :style="getContentStyle">
 *     <!-- 内容区域 -->
 *   </div>
 * </template>
 * ```
 * 
 * @param options 配置选项
 * @returns 布局尺寸相关的响应式数据和方法
 */
export const useLayout = (options?: {
  collapsedWidth?: number
  expandedWidth?: number
  topBarHeight?: number
}) => {
  const {
    collapsedWidth = 64,
    expandedWidth = 200,
    topBarHeight = 50
  } = options || {}

  // 获取应用状态
  const appStore = useAppStoreWithOut()
  const isCollapsed = computed(() => appStore.getCollapse)

  // 动态计算布局尺寸
  const layoutSize = computed(() => ({
    leftMenuWidth: isCollapsed.value ? collapsedWidth : expandedWidth,
    topBarHeight: topBarHeight
  }))

  // 获取弹窗样式
  const getModalStyle = computed(() => ({
    marginLeft: `${layoutSize.value.leftMenuWidth}px`,
    marginTop: `${layoutSize.value.topBarHeight}px`
  }))

  // 获取内容区域样式（考虑左侧菜单和顶部栏）
  const getContentStyle = computed(() => ({
    marginLeft: `${layoutSize.value.leftMenuWidth}px`,
    marginTop: `${layoutSize.value.topBarHeight}px`,
    width: `calc(100% - ${layoutSize.value.leftMenuWidth}px)`,
    height: `calc(100% - ${layoutSize.value.topBarHeight}px)`
  }))

  return {
    // 响应式数据
    isCollapsed,
    layoutSize,
    
    // 计算属性
    getModalStyle,
    getContentStyle,
    
    // 方法
    getLeftMenuWidth: () => layoutSize.value.leftMenuWidth,
    getTopBarHeight: () => layoutSize.value.topBarHeight
  }
} 