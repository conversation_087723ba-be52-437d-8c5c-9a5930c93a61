import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { DICT_TYPE } from '@/utils/dict'
import * as UserApi from '@/api/system/user'
import { dateFormatter2 } from '@/utils/formatTime'

/**
 * 授权委托管理 - 统一Schema配置
 * 通过 isSearch、isTable、isForm、isDetail 控制字段在不同场景下的显示
 */
export const authorizationSchemas: CrudSchema[] = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    isTable: true,
    isSearch: false,
    isForm: false
  },
  // ==================== 基础信息组 ====================
  {
    field: 'authorizationCode',
    label: '授权编号',
    isTable: true,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        disabled: true,
        placeholder: '系统自动生成'
      }
    }
  },
  {
    field: 'authorizationType',
    label: '授权类型',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.AUTHORIZATION_TYPE,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择授权类型'
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true,
        placeholder: '请选择授权类型'
      }
    }
  },

  // ==================== 授权人信息组 ====================
  {
    field: 'authorizer',
    label: '授权人',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      api: () => UserApi.getSimpleUserList() as any,
      componentProps: {
        placeholder: '请选择授权人',
        optionsAlias: {
          labelField: 'nickname',
          valueField: 'id'
        }
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      api: () => UserApi.getSimpleUserList() as any,
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true,
        placeholder: '请选择授权人',
        optionsAlias: {
          labelField: 'nickname',
          valueField: 'id'
        }
      }
    }
  },
  {
    field: 'authorizerPosition',
    label: '授权人职务',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true,
        placeholder: '请输入授权人职务'
      }
    }
  },
  {
    field: 'authorizerUnitId',
    label: '授权人单位',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true,
        placeholder: '请选择授权人单位'
      }
    }
  },
  {
    field: 'authorizerIdCard',
    label: '授权人身份证号码',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 18,
        clearable: true,
        placeholder: '请输入授权人身份证号码'
      }
    }
  },
  {
    field: 'authorizerAddress',
    label: '授权人地址',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        maxlength: 200,
        clearable: true,
        style: { width: '300px' },
        placeholder: '请输入授权人地址'
      }
    },
    detail: {
      span: 24
    }
  },

  // ==================== 被授权人信息组 ====================
  {
    field: 'authorizedPerson',
    label: '被授权人',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入被授权人'
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true,
        placeholder: '请输入被授权人'
      }
    }
  },
  {
    field: 'authorizedPosition',
    label: '被授权人职务',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 50,
        clearable: true,
        placeholder: '请输入被授权人职务'
      }
    }
  },
  {
    field: 'authorizedUnitId',
    label: '被授权人单位',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true,
        placeholder: '请输入被授权人单位'
      }
    }
  },
  {
    field: 'authorizedIdCard',
    label: '被授权人身份证号码',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      componentProps: {
        style: { width: '300px' },
        maxlength: 18,
        clearable: true,
        placeholder: '请输入被授权人身份证号码'
      }
    }
  },
  {
    field: 'authorizedAddress',
    label: '被授权人地址',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        maxlength: 200,
        clearable: true,
        placeholder: '请输入被授权人地址'
      }
    },
    detail: {
      span: 24
    }
  },

  // ==================== 授权详情组 ====================
  {
    field: 'startDate',
    label: '授权起始日期',
    isTable: true,
    isForm: true,
    isDetail: true,

    form: {
      component: 'DatePicker',
      componentProps: {
        style: { width: '300px' },
        type: 'date',
        clearable: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'x',
        placeholder: '请选择授权起始日期'
      }
    },
    table: {
      width: 120,
      formatter: dateFormatter2
    },
    detail: {
      dateFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'endDate',
    label: '授权截止日期',
    isTable: true,
    isForm: true,
    isDetail: true,
    form: {
      component: 'DatePicker',
      componentProps: {
        style: { width: '300px' },
        type: 'date',
        clearable: true,
        format: 'YYYY-MM-DD',
        valueFormat: 'x',
        placeholder: '请选择授权截止日期'
      }
    },
    table: {
      width: 120,
      formatter: dateFormatter2
    },
    detail: {
      dateFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'copyCount',
    label: '委托份数',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'InputNumber',
      componentProps: {
        style: { width: '300px' },
        min: 1,
        max: 999,
        controlsPosition: 'right',
        placeholder: '请输入委托份数'
      }
    },
    detail: {
      formatter: (row) => `${row?.copyCount || 0}份`
    }
  },
  {
    field: 'signCompanyId',
    label: '实际签署公司',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'DeptTreeSelect',
      componentProps: {
        style: { width: '300px' },
        maxlength: 100,
        clearable: true,
        placeholder: '请选择实际签署公司'
      }
    }
  },
  {
    field: 'isSubAuthorization',
    label: '是否为转授权',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.YES_NO_LIST,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true,
        placeholder: '请选择'
      }
    }
  },
  {
    field: 'allowSubAuthorization',
    label: '是否允许转授权',
    isTable: false,
    isForm: true,
    isDetail: true,
    dictType: DICT_TYPE.YES_NO_LIST,
    form: {
      component: 'Select',
      componentProps: {
        style: { width: '300px' },
        clearable: true,
        placeholder: '请选择'
      }
    }
  },
  {
    field: 'applicationReason',
    label: '申请理由',
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        type: 'textarea',
        rows: 4,
        maxlength: 500,
        clearable: true,
        placeholder: '请输入申请理由'
      }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'status',
    label: '审批状态',
    isSearch: true,
    isTable: true,
    isDetail: true,
    dictType: DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择审批状态'
      }
    },
    table: {
      width: 100
    }
  },
  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isTable: true,
    table: {
      width: 180,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于详情页面分组显示）
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return authorizationSchemas.filter((schema) =>
    ['authorizationCode', 'authorizationType', 'processStatus', 'createTime'].includes(schema.field)
  )
}

/**
 * 获取授权人信息字段（用于表单和详情页面分组显示）
 */
export const getAuthorizerSchemas = (): CrudSchema[] => {
  return authorizationSchemas.filter((schema) =>
    [
      'authorizer',
      'authorizerPosition',
      'authorizerUnitId',
      'authorizerIdCard',
      'authorizerAddress'
    ].includes(schema.field)
  )
}

/**
 * 获取被授权人信息字段（用于表单和详情页面分组显示）
 */
export const getAuthorizedSchemas = (): CrudSchema[] => {
  return authorizationSchemas.filter((schema) =>
    [
      'authorizedPerson',
      'authorizedPosition',
      'authorizedUnitId',
      'authorizedIdCard',
      'authorizedAddress'
    ].includes(schema.field)
  )
}

/**
 * 获取授权详情字段（用于表单和详情页面分组显示）
 */
export const getAuthorizationDetailSchemas = (): CrudSchema[] => {
  return authorizationSchemas.filter((schema) =>
    [
      'authorizationCode',
      'authorizationType',
      'startDate',
      'endDate',
      'copyCount',
      'signCompanyId',
      'isSubAuthorization',
      'allowSubAuthorization',
      'applicationReason'
    ].includes(schema.field)
  )
}

/**
 * 生成授权编号
 */
export const generateauthorizationCode = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hour = String(now.getHours()).padStart(2, '0')
  const minute = String(now.getMinutes()).padStart(2, '0')
  const second = String(now.getSeconds()).padStart(2, '0')
  return `SQ${year}${month}${day}${hour}${minute}${second}`
}
