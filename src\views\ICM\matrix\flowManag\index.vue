<template>
  <ContentWrap>
    <!-- 搜索区域 -->
    <div class="flex items-start">
      <Search
        ref="searchRef"
        :schema="allSchemas.searchSchema"
        :show-search="true"
        :show-reset="true"
        :expand="false"
        layout="inline"
        @search="setSearchParams"
        @reset="setSearchParams"
      />
      <Button type="primary" @click="handleAdd">新增</Button>
    </div>
  </ContentWrap>

  <!-- 表格区域 -->
  <ContentWrap>
    <Table
      ref="tableRef"
      v-model:pageSize="pageSize"
      height="calc(70vh)"
      maxHeight="calc(70vh)"
      v-model:currentPage="currentPage"
      :columns="allSchemas.tableColumns"
      :data="tableList"
      :loading="loading"
      :pagination="{
        total: total
      }"
      @register="tableRegister"
    >
      <!-- 流程名称列 -->
      <template #flowName="{ row }">
        <div class="max-w-150px">
          <el-tooltip :content="row.flowName" placement="top" :show-after="500">
            <span class="text-truncate cursor-pointer text-blue-600" @click="handleView(row)">
              {{ row.flowName }}
            </span>
          </el-tooltip>
        </div>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <Button link type="primary" @click="handleView(row)">查看</Button>
        <Button link type="primary" @click="handleEdit(row)">编辑</Button>
        <Button link type="danger" @click="handleDelete(row)">删除</Button>
      </template>
    </Table>
  </ContentWrap>

  <!-- 新增/编辑全屏覆盖 -->
  <UpdateFlow ref="updateRef" @close="handleUpdateClose" @save="handleSaveSuccess" />

  <!-- 查看详情全屏覆盖 -->
  <FlowDetail ref="detailRef" @close="handleDetailClose" />
</template>

<script lang="ts" setup>
import { ref, onMounted, toRefs, defineEmits } from 'vue'
import { ContentWrap, Search, Table, Button } from '@/components'
import { useTable } from '@/hooks/web/useTable'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getFlowManagementListApi, deleteFlowManagementApi } from '@/api/ICM/matrix/flowManag'
import { flowSchemas } from './schemas/flowSchemas'
import { ElMessage, ElMessageBox } from 'element-plus'
import UpdateFlow from './components/UpdateFlow.vue'
import FlowDetail from './components/FlowDetail.vue'
defineOptions({ name: 'ICMFlowManagement' })

// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(flowSchemas)

// 使用表格hook
const {
  register: tableRegister,
  tableMethods: { getList: getTableList, setSearchParams },
  tableObject
} = useTable({
  getListApi: getFlowManagementListApi,
  immediate: true
})

// 使用 toRefs 保持响应性
const { loading, tableList, total, pageSize, currentPage } = toRefs(tableObject)

// 弹窗状态
const updateRef = ref()
const detailRef = ref()

// 新建
const handleAdd = () => {
  updateRef.value.open('add')
}

// 关闭新增/编辑弹窗
const handleUpdateClose = () => {
  // 弹窗关闭逻辑
}

// 关闭详情弹窗
const handleDetailClose = () => {
  // 详情弹窗关闭逻辑
}

// 保存成功回调
const handleSaveSuccess = () => {
  getTableList()
}

// 查看详情
const handleView = (row: any) => {
  detailRef.value.open(row.id)
}

// 编辑
const handleEdit = (row: any) => {
  updateRef.value.open('edit', row.id)
}

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除流程"${row.processName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteFlowManagementApi(row.id)
      ElMessage.success('删除成功')
      getTableList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  getTableList()
})
</script>

<style lang="scss" scoped>
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.cursor-pointer {
  cursor: pointer;
}

.text-blue-600 {
  color: #2563eb;
}

.text-blue-600:hover {
  color: #1d4ed8;
}
</style>
