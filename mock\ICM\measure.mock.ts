import Mock from 'mockjs'
import { SUCCESS_CODE } from '../public.constant'

const timeout = 1000

// 模拟数据
const icmMeasureList = Mock.mock({
  'list|100': [
    {
      'id|+1': 1,
      measureName: () => Mock.Random.ctitle(8, 25) + '措施',
      measureCode: () => 'M' + Mock.Random.string('number', 6),
      belongCompany: () => Mock.Random.pick(['XXX集团公司', '全部']),
      measureType: () =>
        Mock.Random.pick(['system', 'process', 'operation', 'management', 'control', 'monitoring']),
      responsibleDept: () =>
        Mock.Random.pick([
          '风险管理部',
          '合规部',
          '法律事务部',
          '运营管理部',
          '信息科技部',
          '人力资源部',
          '财务会计部',
          '审计部'
        ]),
      controlItemName: () => Mock.Random.ctitle(6, 20) + '控制细项',
      controlItemCode: () => 'CTRL' + Mock.Random.string('number', 3),
      controlItemDesc: () => Mock.Random.cparagraph(3, 8),
      riskPoint: () => 'risk_point_' + Mock.Random.string('number', 3),
      riskLevel: () => Mock.Random.pick(['high', 'medium', 'low']),
      riskType: () =>
        Mock.Random.pick([
          'credit',
          'market',
          'operational',
          'liquidity',
          'reputation',
          'compliance'
        ]),
      riskDesc: () => Mock.Random.cparagraph(4, 10),
      attachments: () =>
        Mock.mock({
          'list|0-5': [
            {
              'id|+1': 1000,
              fileName: () =>
                Mock.Random.ctitle(5, 15) +
                Mock.Random.pick(['.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx']),
              fileUrl: () => Mock.Random.url('http'),
              fileSize: () => Mock.Random.integer(1024, 10485760),
              uploadTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
              uploader: () => Mock.Random.cname(),
              version: () => 'V' + Mock.Random.float(1, 5, 1, 1),
              'isActive|1': true
            }
          ]
        }).list,
      createTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    }
  ]
}).list

export default [
  // 获取内控措施分页列表
  {
    url: '/admin-api/api/ICM/measure/page',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        measureName,
        measureCode,
        measureType,
        responsibleDept,
        riskLevel,
        riskType,
        belongCompany
      } = query

      let filteredList = [...icmMeasureList]

      // 筛选条件
      if (measureName) {
        filteredList = filteredList.filter((item) => item.measureName.includes(measureName))
      }

      if (measureCode) {
        filteredList = filteredList.filter((item) => item.measureCode.includes(measureCode))
      }

      if (measureType) {
        filteredList = filteredList.filter((item) => item.measureType === measureType)
      }

      if (responsibleDept) {
        filteredList = filteredList.filter((item) => item.responsibleDept === responsibleDept)
      }

      if (riskLevel) {
        filteredList = filteredList.filter((item) => item.riskLevel === riskLevel)
      }

      if (riskType) {
        filteredList = filteredList.filter((item) => item.riskType === riskType)
      }

      if (belongCompany && belongCompany !== '全部') {
        filteredList = filteredList.filter((item) => item.belongCompany === belongCompany)
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + Number(pageSize)
      const list = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list,
          total: filteredList.length
        }
      }
    }
  },

  // 获取内控措施详情
  {
    url: '/admin-api/api/ICM/measure/get',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const item = icmMeasureList.find((item) => item.id === Number(id))

      return {
        code: SUCCESS_CODE,
        data: item || null
      }
    }
  },

  // 创建内控措施
  {
    url: '/admin-api/api/ICM/measure/create',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const newItem = {
        ...body,
        id: icmMeasureList.length + 1,
        attachments: body.attachments || [],
        createTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
        updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
      }

      icmMeasureList.push(newItem)

      return {
        code: SUCCESS_CODE,
        data: newItem.id
      }
    }
  },

  // 更新内控措施
  {
    url: '/admin-api/api/ICM/measure/update',
    method: 'put',
    timeout,
    response: ({ body }) => {
      const index = icmMeasureList.findIndex((item) => item.id === body.id)

      if (index !== -1) {
        body.updateTime = new Date().toISOString().replace('T', ' ').substr(0, 19)
        icmMeasureList[index] = { ...icmMeasureList[index], ...body }

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '更新失败，记录不存在'
      }
    }
  },

  // 删除内控措施
  {
    url: '/admin-api/api/ICM/measure/delete',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const index = icmMeasureList.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        icmMeasureList.splice(index, 1)

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '删除失败，记录不存在'
      }
    }
  },

  // 导出内控措施
  {
    url: '/admin-api/api/ICM/measure/export',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: SUCCESS_CODE,
        data: '导出成功'
      }
    }
  },

  // 获取风险点选项列表
  {
    url: '/admin-api/api/ICM/measure/risk-points',
    method: 'get',
    timeout,
    response: () => {
      const riskPoints = Mock.mock({
        'list|10': [
          {
            'id|+1': 1,
            value: () => 'risk_point_' + Mock.Random.string('number', 3),
            label: () => Mock.Random.ctitle(5, 15) + '风险点',
            description: () => Mock.Random.cparagraph(2, 5)
          }
        ]
      }).list

      return {
        code: SUCCESS_CODE,
        data: riskPoints
      }
    }
  },

  // 获取控制细项编号选项列表
  {
    url: '/admin-api/api/ICM/measure/control-items',
    method: 'get',
    timeout,
    response: () => {
      const controlItems = Mock.mock({
        'list|10': [
          {
            'id|+1': 1,
            value: () => 'CTRL' + Mock.Random.string('number', 3),
            label: () => 'CTRL' + Mock.Random.string('number', 3),
            description: () => Mock.Random.cparagraph(1, 3)
          }
        ]
      }).list

      return {
        code: SUCCESS_CODE,
        data: controlItems
      }
    }
  }
]
