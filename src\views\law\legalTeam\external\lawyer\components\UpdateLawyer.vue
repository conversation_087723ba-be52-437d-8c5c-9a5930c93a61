<script setup lang="tsx">
import { computed, defineExpose, nextTick } from 'vue'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getExternalLawyerDetailApi,
  addExternalLawyerApi,
  updateExternalLawyerApi
} from '@/api/law/legalTeam/externalLawyer'
import {
  lawyerSchemas,
  getBasicFormRules,
  getEvaluationFormRules,
  getBasicInfoSchemas,
  getEvaluationInfoSchemas
} from '../schemas/lawyerSchemas'
import { useMessage } from '@/hooks/web/useMessage'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle, layoutSize, isCollapsed } = useLayout()
// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(lawyerSchemas)

const message = useMessage()
const emit = defineEmits(['close', 'save'])

const mode = ref<'add' | 'edit' | 'view'>('add')
const visible = ref(false)
const editId = ref<number | null>(null)

const title = computed(() => {
  switch (mode.value) {
    case 'add':
      return '新增外聘律师'
    case 'edit':
      return '编辑外聘律师'
    case 'view':
      return '查看外聘律师'
    default:
      return ''
  }
})

// 计算属性：是否为只读模式
const isReadonly = computed(() => mode.value === 'view')

// 获取基础信息表单Schema - 排除评价信息字段
const basicFormSchema = computed(() => {
  return allSchemas.formSchema.filter((schema) =>
    [
      'lawyerName',
      'gender',
      'ethnicity',
      'birthDate',
      'politicalAffiliation',
      'contactPhone',
      'practiceYear',
      'practiceCertCode',
      'firmName',
      'firmPosition',
      'isArbitrator',
      'belongUnitId',
      'professionalFields',
      'remark',
      'attachment'
    ].includes(schema.field)
  )
})

// 获取评价信息表单Schema
const evaluationFormSchema = computed(() => {
  return allSchemas.formSchema.filter((schema) =>
    ['attitudeRating', 'abilityRating', 'advantages', 'defect', 'ratingRemark'].includes(
      schema.field
    )
  )
})

// 表单验证规则
const basicRules = getBasicFormRules()
const evaluationRules = getEvaluationFormRules()

// 表单管理
const { register: basicFormRegister, methods: basicFormMethods } = useForm()
const { register: evaluationFormRegister, methods: evaluationFormMethods } = useForm()

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  editId.value = null
  basicFormMethods.setValues({})
  evaluationFormMethods.setValues({})
  emit('close')
}

// 设置表单数据
const setValue = async () => {
  if (editId.value) {
    const data = await getExternalLawyerDetailApi(editId.value)
    if (data.professionalFields) {
      data.professionalFields = data.professionalFields.split(',')
    }

    // 分别设置基础信息和评价信息
    const basicData = {}
    const evaluationData = {}

    basicFormSchema.value.forEach((item) => {
      basicData[item.field] = data[item.field]
    })

    evaluationFormSchema.value.forEach((item) => {
      evaluationData[item.field] = data[item.field]
    })

    basicFormMethods.setValues(basicData)
    evaluationFormMethods.setValues(evaluationData)
  }
}

// 保存操作
const handleSaveAction = async () => {
  try {
    const [basicValid, evaluationValid] = await Promise.all([
      basicFormMethods.validate().catch(() => false),
      evaluationFormMethods.validate().catch(() => false)
    ])

    if (!basicValid || !evaluationValid) return

    const [basicFormData, evaluationFormData] = await Promise.all([
      basicFormMethods.getFormData(),
      evaluationFormMethods.getFormData()
    ])

    const submitData = {
      ...basicFormData,
      ...evaluationFormData,
      ...(mode.value === 'edit' && editId.value && { id: editId.value })
    }
    if (submitData.professionalFields) {
      submitData.professionalFields = submitData.professionalFields.join(',')
    }

    const apiMethod = mode.value === 'add' ? addExternalLawyerApi : updateExternalLawyerApi
    const actionText = mode.value === 'add' ? '新增' : '编辑'

    await apiMethod(submitData)
    message.success(`${actionText}成功`)

    visible.value = false
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 打开弹窗
const open = async (type: 'add' | 'edit' | 'view', id?: number) => {
  mode.value = type
  visible.value = true

  await nextTick()

  if (id && type !== 'add') {
    editId.value = id
    await setValue()
  }
}

defineExpose({
  open
})
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            :showSave="!isReadonly"
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSaveAction"
          />
        </div>
      </div>

      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <!-- 基础信息 -->
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
          <div class="mt-12px">
            <Form
              :disabled="isReadonly"
              :schema="basicFormSchema"
              @register="basicFormRegister"
              isCol
              :rules="basicRules"
            />
          </div>
        </div>

        <!-- 评价信息 -->
        <div class="mt-4">
          <div class="bg-#f1f2fe lh-40px pl-5">评价信息</div>
          <div class="mt-12px">
            <Form
              :disabled="isReadonly"
              :schema="evaluationFormSchema"
              @register="evaluationFormRegister"
              isCol
              :rules="evaluationRules"
            />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
