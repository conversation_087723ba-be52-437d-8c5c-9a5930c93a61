/*
 * @Description: 授权委托管理Mock数据
 */
import Mock from 'mockjs'
import { SUCCESS_CODE } from '../public.constant'

const timeout = 1000

// 授权类型
const authorizationTypes = ['1', '2', '3', '4', '5']
// 审批状态
const authorizationStatuses = ['1', '2', '3', '4'] // 待审批、已通过、已拒绝、已终止
// 是否选项
const yesNoOptions = ['1', '0']

// 生成Mock数据
const generateAuthorizationData = (count: number): any[] => {
  const list: any[] = []
  const companies = [
    '北京科技有限公司',
    '上海贸易有限公司',
    '深圳投资有限公司',
    '广州制造有限公司',
    '杭州网络有限公司'
  ]
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const positions = [
    '总经理',
    '副总经理',
    '法务经理',
    '财务经理',
    '人事经理',
    '部门经理',
    '主管',
    '专员'
  ]
  const authTypes = ['法律事务授权', '财务授权', '人事授权', '业务授权', '签约授权']

  for (let i = 1; i <= count; i++) {
    const authorizerName = Mock.Random.pick(names)
    const authorizedName = Mock.Random.pick(names.filter((n) => n !== authorizerName))

    list.push({
      id: i,
      authorizationNo: `SQ${new Date().getFullYear()}${Mock.Random.integer(1000, 9999)}${Mock.Random.integer(1000, 9999)}`,
      // 授权人信息
      authorizer: authorizerName,
      authorizerPosition: Mock.Random.pick(positions),
      authorizerCompany: Mock.Random.pick(companies),
      authorizerIdCard: Mock.Random.integer(100000000000000000, 999999999999999999).toString(),
      authorizerAddress: Mock.Random.county(true),
      // 被授权人信息
      authorized: authorizedName,
      authorizedPosition: Mock.Random.pick(positions),
      authorizedCompany: Mock.Random.pick(companies),
      authorizedIdCard: Mock.Random.integer(100000000000000000, 999999999999999999).toString(),
      authorizedAddress: Mock.Random.county(true),
      // 授权详情
      authorizationType: Mock.Random.pick(authorizationTypes),
      startDate: Mock.Random.date('yyyy-MM-dd'),
      endDate: Mock.Random.date('yyyy-MM-dd'),
      copies: Mock.Random.integer(1, 10),
      actualSignCompany: Mock.Random.pick(companies),
      isReauthorization: Mock.Random.pick(yesNoOptions),
      allowReauthorization: Mock.Random.pick(yesNoOptions),
      applicationReason: Mock.Random.pick([
        '因业务需要，需要授权处理相关事务',
        '临时出差，需要委托他人代为处理',
        '工作调动，需要转授权给接任人员',
        '专业需要，委托专业人员处理',
        '时间冲突，需要授权代为参与'
      ]),
      status: Mock.Random.pick(authorizationStatuses),
      createTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    })
  }
  return list
}

// 生成100条测试数据
const authorizationList: any[] = generateAuthorizationData(100)

export default [
  // 获取授权委托列表
  {
    url: '/admin-api/api/authorization/list',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        authorizer,
        authorized,
        authorizationType,
        status
      } = query

      let filteredList = [...authorizationList]

      // 根据授权人筛选
      if (authorizer) {
        filteredList = filteredList.filter((item: any) => item.authorizer.includes(authorizer))
      }

      // 根据被授权人筛选
      if (authorized) {
        filteredList = filteredList.filter((item: any) => item.authorized.includes(authorized))
      }

      // 根据授权类型筛选
      if (authorizationType) {
        filteredList = filteredList.filter(
          (item: any) => item.authorizationType === authorizationType
        )
      }

      // 根据状态筛选
      if (status) {
        filteredList = filteredList.filter((item: any) => item.status === status)
      }

      // 分页处理
      const start = (pageNum - 1) * pageSize
      const end = start + parseInt(pageSize)
      const pageData = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list: pageData,
          total: filteredList.length
        },
        msg: '查询成功'
      }
    }
  },

  // 获取授权委托详情
  {
    url: '/admin-api/api/authorization/detail',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const authorizationItem = authorizationList.find((item: any) => item.id == id)
      if (authorizationItem) {
        return {
          code: SUCCESS_CODE,
          data: authorizationItem,
          msg: '查询成功'
        }
      }
      return {
        code: 500,
        data: null,
        msg: '授权委托不存在'
      }
    }
  },

  // 删除授权委托
  {
    url: '/admin-api/api/authorization/delete',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const index = authorizationList.findIndex((item: any) => item.id == id)
      if (index > -1) {
        authorizationList.splice(index, 1)
        return {
          code: SUCCESS_CODE,
          data: null,
          msg: '删除成功'
        }
      }
      return {
        code: 500,
        data: null,
        msg: '授权委托不存在'
      }
    }
  },

  // 新增授权委托
  {
    url: '/admin-api/api/authorization/add',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const newAuthorization = {
        id: authorizationList.length + 1,
        authorizationNo: `SQ${new Date().getFullYear()}${Mock.Random.integer(1000, 9999)}${Mock.Random.integer(1000, 9999)}`,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        status: '1', // 默认待审批
        ...body
      }
      authorizationList.push(newAuthorization)
      return {
        code: SUCCESS_CODE,
        data: newAuthorization,
        msg: '新增成功'
      }
    }
  },

  // 编辑授权委托
  {
    url: '/admin-api/api/authorization/update',
    method: 'put',
    timeout,
    response: ({ body }) => {
      const { id } = body
      const index = authorizationList.findIndex((item: any) => item.id == id)
      if (index > -1) {
        authorizationList[index] = {
          ...authorizationList[index],
          ...body,
          updateTime: new Date().toISOString()
        }
        return {
          code: SUCCESS_CODE,
          data: authorizationList[index],
          msg: '更新成功'
        }
      }
      return {
        code: 500,
        data: null,
        msg: '授权委托不存在'
      }
    }
  },

  // 获取授权委托审批流程详情
  {
    url: '/admin-api/api/authorization/approval-process',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      return {
        code: SUCCESS_CODE,
        data: {
          data: [
            {
              approvalTime: '2024-01-15 10:00:00',
              approverName: '张经理',
              approvalResult: '同意',
              approvalComment: '授权委托符合要求，已通过审批'
            },
            {
              approvalTime: '2024-01-16 11:00:00',
              approverName: '李总监',
              approvalResult: '拒绝',
              approvalComment: '授权范围过大，需要重新调整'
            }
          ],
          info: {
            time: '2024-01-15 09:00:00',
            name: '王专员',
            event: '新建'
          }
        },
        msg: '查询成功'
      }
    }
  },

  // 获取授权委托操作日志
  {
    url: '/admin-api/api/authorization/action-log',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      return {
        code: SUCCESS_CODE,
        data: [
          {
            time: '2024-01-15 10:00:00',
            actionName: '张经理',
            action: '审批通过'
          },
          {
            time: '2024-01-15 09:30:00',
            actionName: '王专员',
            action: '提交审批'
          },
          {
            time: '2024-01-15 09:00:00',
            actionName: '王专员',
            action: '创建授权委托'
          }
        ],
        msg: '查询成功'
      }
    }
  },

  // 审批授权委托
  {
    url: '/admin-api/api/authorization/approve',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const { id, action, comment } = body
      const authorizationItem = authorizationList.find((item: any) => item.id == id)
      if (authorizationItem) {
        // 更新状态
        const statusMap = {
          '1': '2', // 通过
          '0': '3', // 拒绝
          '2': '4' // 终止
        }
        authorizationItem.status = statusMap[action] || '1'
        authorizationItem.updateTime = new Date().toISOString()

        const actionMap = {
          '1': '通过',
          '0': '拒绝',
          '2': '终止'
        }

        return {
          code: SUCCESS_CODE,
          data: null,
          msg: `授权委托已${actionMap[action]}，备注：${comment || '无'}`
        }
      }
      return {
        code: 500,
        data: null,
        msg: '授权委托不存在'
      }
    }
  }
]
