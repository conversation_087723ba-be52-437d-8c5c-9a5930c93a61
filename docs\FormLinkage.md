# 表单联动功能使用指南

表单联动功能可以实现当某个表单字段值发生变化时，自动更新其他字段的选项数据。这对于省市区选择、部门用户选择等场景非常有用。

## 🚀 功能特性

- **零配置编辑模式**：编辑时自动根据表单数据初始化联动选项
- **智能数据回显**：完美解决编辑模式下的选项显示问题
- **多种联动类型**：支持简单联动、条件联动、链式联动等
- **高性能**：并行加载选项数据，串行处理有依赖关系的联动
- **向后兼容**：不影响现有Form组件的使用

## 📋 基本用法

### 1. 简单的一对一联动

```typescript
import { FormSchema, FormLinkageConfig } from '@/types/form'
import { createSimpleLinkage, defaultOptionMapper } from '@/utils/formLinkage'

const formSchema: FormSchema[] = [
  {
    field: 'provinceId',
    label: '省份',
    component: 'Select',
    componentProps: {
      options: [] // 预设省份选项
    },
    // 配置联动：当省份变化时，更新城市选项
    linkage: createSimpleLinkage(
      'provinceId',    // 触发字段
      'cityId',        // 目标字段
      getCityListApi,  // API函数
      defaultOptionMapper  // 选项映射函数（可选）
    )
  },
  {
    field: 'cityId',
    label: '城市',
    component: 'Select',
    componentProps: {
      options: [],
      placeholder: '请先选择省份'
    }
  }
]

// API函数示例
const getCityListApi = (provinceId: string, formModel?: Recordable) => {
  return request.get({
    url: '/api/cities',
    params: { provinceId }
  })
}
```

### 2. 链式联动（省市区）

```typescript
const formSchema: FormSchema[] = [
  {
    field: 'provinceId',
    label: '省份',
    component: 'Select',
    componentProps: { options: [] },
    linkage: createSimpleLinkage('provinceId', 'cityId', getCityListApi)
  },
  {
    field: 'cityId',
    label: '城市',
    component: 'Select',
    componentProps: { options: [] },
    linkage: createSimpleLinkage('cityId', 'districtId', getDistrictListApi)
  },
  {
    field: 'districtId',
    label: '区县',
    component: 'TreeSelect', // 也支持TreeSelect
    componentProps: { options: [] }
  }
]
```

### 3. 条件联动

```typescript
const formSchema: FormSchema[] = [
  {
    field: 'userType',
    label: '用户类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '内部用户', value: 'internal' },
        { label: '外部用户', value: 'external' }
      ]
    },
    // 条件联动：只有内部用户才显示部门选择
    linkage: createConditionalLinkage(
      'userType',
      'deptId',
      getDepartmentListApi,
      (userType) => userType === 'internal', // 联动条件
      defaultOptionMapper
    )
  },
  {
    field: 'deptId',
    label: '部门',
    component: 'Select',
    componentProps: { options: [] }
  }
]
```

### 4. 编辑模式使用

推荐使用 `setEditData` 方法，一行代码搞定编辑模式：

```typescript
<template>
  <Form 
    :schema="formSchema"
    @register="register"
  />
  <el-button @click="loadEditData">加载编辑数据</el-button>
</template>

<script setup lang="ts">
const { register, methods } = useForm()

// 推荐方式：使用setEditData方法
const loadEditData = async () => {
  const editData = {
    provinceId: '110000',
    cityId: '110100',
    districtId: '110101'
  }
  
  // 一行代码搞定：设置数据 + 初始化联动选项
  await methods.setEditData(editData)
}

// 或者手动分步骤
const loadEditDataManual = async () => {
  // 先设置数据
  await methods.setValues(editData)
  // 再初始化联动
  await methods.initializeLinkage()
}
</script>
```

## 🛠️ 配置选项

### FormLinkageConfig 配置

```typescript
export type FormLinkageConfig = {
  triggerField: string      // 触发字段名
  targetField: string       // 目标字段名
  api: (triggerValue: any, formModel?: Recordable) => Promise<any>  // API函数
  optionMapper?: (data: any) => ComponentOptions[]  // 选项映射函数
  clearValue?: boolean      // 是否清空目标字段的值（默认true）
  clearOptions?: boolean    // 是否清空目标字段的选项（默认true）
  condition?: (triggerValue: any, formModel?: Recordable) => boolean  // 联动条件
}
```

### 工具函数

#### createSimpleLinkage
创建简单的一对一联动配置。

```typescript
createSimpleLinkage(
  triggerField: string,
  targetField: string,
  api: (value: any, formModel?: Recordable) => Promise<any>,
  optionMapper?: (data: any) => ComponentOptions[]
): FormLinkageConfig
```

#### createConditionalLinkage
创建带条件判断的联动配置。

```typescript
createConditionalLinkage(
  triggerField: string,
  targetField: string,
  api: (value: any, formModel?: Recordable) => Promise<any>,
  condition: (value: any, formModel?: Recordable) => boolean,
  optionMapper?: (data: any) => ComponentOptions[]
): FormLinkageConfig
```

#### createKeepValueLinkage
创建不清空值的联动配置（仅更新选项）。

```typescript
createKeepValueLinkage(
  triggerField: string,
  targetField: string,
  api: (value: any, formModel?: Recordable) => Promise<any>,
  optionMapper?: (data: any) => ComponentOptions[]
): FormLinkageConfig
```

### 选项映射函数

#### defaultOptionMapper
默认的选项映射函数，适用于大多数场景。

```typescript
// 输入数据格式
[
  { id: 1, name: '选项1' },
  { id: 2, name: '选项2' }
]

// 输出格式
[
  { label: '选项1', value: 1 },
  { label: '选项2', value: 2 }
]
```

#### treeOptionMapper
树形数据的选项映射函数。

```typescript
// 输入树形数据
[
  {
    id: 1,
    name: '一级',
    children: [
      { id: 11, name: '二级' }
    ]
  }
]

// 输出树形选项
[
  {
    label: '一级',
    value: 1,
    children: [
      { label: '二级', value: 11 }
    ]
  }
]
```

#### customOptionMapper
自定义字段映射函数。

```typescript
const mapper = customOptionMapper('title', 'code')
// 将使用title作为label，code作为value
```

#### formattedOptionMapper
带格式化的选项映射函数。

```typescript
const mapper = formattedOptionMapper(
  (item) => `${item.name} (${item.code})`,
  'id'
)
// 格式化label显示
```

## 📚 常用场景

### 省市区联动

```typescript
import { createRegionLinkage } from '@/utils/formLinkage'

const linkages = createRegionLinkage(
  getProvinceListApi,
  getCityListApi,
  getDistrictListApi
)

// 在schema中使用
const schema: FormSchema[] = [
  {
    field: 'provinceId',
    linkage: linkages[0]
  },
  {
    field: 'cityId', 
    linkage: linkages[1]
  }
  // ...
]
```

### 部门用户联动

```typescript
import { createDeptUserLinkage } from '@/utils/formLinkage'

const linkage = createDeptUserLinkage(
  getDepartmentListApi,
  getUserListApi
)

const schema: FormSchema[] = [
  {
    field: 'deptId',
    linkage: linkage  
  }
  // ...
]
```

### 分类商品联动

```typescript
import { createCategoryProductLinkage } from '@/utils/formLinkage'

const linkage = createCategoryProductLinkage(
  getCategoryListApi,
  getProductListApi
)
```

## 🔧 高级用法

### 多个联动配置

一个字段可以触发多个联动：

```typescript
{
  field: 'userType',
  linkage: [
    createConditionalLinkage('userType', 'deptId', getDeptApi, (v) => v === 'internal'),
    createConditionalLinkage('userType', 'companyId', getCompanyApi, (v) => v === 'external')
  ]
}
```

### 手动初始化联动

```typescript
const { register, methods } = useForm()

// 在需要的时候手动初始化联动
onMounted(async () => {
  await methods.initializeLinkage()
})
```

### 自定义API参数

API函数可以接收完整的表单数据：

```typescript
const getUserListApi = (deptId: string, formModel?: Recordable) => {
  return request.get({
    url: '/api/users',
    params: {
      deptId,
      // 可以使用其他表单字段
      status: formModel?.status || 'active',
      role: formModel?.role
    }
  })
}
```

### 复杂的选项格式化

```typescript
const productMapper = (data: any[]): ComponentOptions[] => {
  return data.map(item => ({
    label: `${item.name} - ¥${item.price}`,
    value: item.id,
    disabled: !item.inStock,
    // 可以保留原始数据
    ...item
  }))
}
```

## ⚠️ 注意事项

1. **API函数返回格式**：确保API返回的数据格式正确，通常应该是 `{ data: [] }` 格式
2. **循环依赖**：避免创建循环联动依赖，如 A→B→A
3. **性能考虑**：对于大量数据的联动，考虑使用防抖或缓存
4. **错误处理**：API调用失败不会影响表单的正常使用，会在控制台输出错误信息

## 🎯 最佳实践

### 新增模式
```typescript
// 清空表单
await methods.setValues({})
```

### 编辑模式
```typescript
// 一行代码搞定编辑模式
await methods.setEditData(editData)
```

### 用户交互
用户手动选择时，联动会自动触发，无需额外代码。

## 📚 完整示例

- `examples/FormLinkageExample.vue` - 完整功能示例
- `examples/SimpleLinkageExample.vue` - 简化使用示例

## ✨ 核心优势

1. **零配置编辑模式**：一行代码实现编辑数据设置和联动初始化
2. **明确的调用时机**：不会重复调用API，性能更好
3. **简洁的API设计**：只在需要的时候初始化，避免复杂的判断逻辑
4. **完美的用户体验**：用户操作流畅，数据回显正确

这个功能让表单联动变得非常简单，只需要在Schema中添加linkage配置即可！ 