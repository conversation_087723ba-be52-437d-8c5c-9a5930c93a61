<template>
  <div
    class="px-4 box-border bg-[white] duration-[0.2s] fixed z-990 top-50px h-[calc(100vh-65px)]"
    :class="{
      'left-200px w-[calc(100%-200px)]': true
    }"
  >
    <div class="w-full h-auto flex-shrink-0">
      <div class="flex items-center w-full">
        <Back
          :title="title"
          hasEmit
          showCancel
          :showSave="!isReadonly"
          @back="handleClose"
          @cancel="handleClose"
          @ok="handleSave"
        />
      </div>
    </div>
    <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
      <div>
        <div class="bg-#f1f2fe lh-40px pl-5">授权人信息</div>
        <div class="mt-12px">
          <Form
            :disabled="isReadonly"
            :schema="authorizerAllSchemas.formSchema"
            @register="authorizerFormRegister"
            isCol
            :rules="rules"
          />
        </div>
      </div>
      <div>
        <div class="bg-#f1f2fe lh-40px pl-5">被授权人信息</div>
        <div class="mt-12px">
          <Form
            :disabled="isReadonly"
            :schema="authorizedAllSchemas.formSchema"
            @register="authorizedFormRegister"
            isCol
            :rules="rules"
          />
        </div>
      </div>
      <div>
        <div class="bg-#f1f2fe lh-40px pl-5">授权详情</div>
        <div class="mt-12px">
          <Form
            :disabled="isReadonly"
            :schema="authorizationDetailAllSchemas.formSchema"
            @register="authorizationDetailFormRegister"
            isCol
            :rules="rules"
          />
        </div>
      </div>
    </ElScrollbar>
  </div>
</template>

<script setup>
import { computed, reactive, ref, nextTick, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useMessage } from '@/hooks/web/useMessage'
import {
  getAuthorizationApplication,
  createAuthorizationApplication,
  updateAuthorizationApplication
} from '@/api/law/authorization'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getAuthorizerSchemas,
  getAuthorizedSchemas,
  getAuthorizationDetailSchemas,
  generateauthorizationCode
} from '../../schemas/authorizationSchemas'

const message = useMessage()
const route = useRoute()
const router = useRouter()
const emit = defineEmits(['close', 'save'])

// 使用 CRUD Schemas 生成表单配置
const authorizerSchemas = getAuthorizerSchemas()
const authorizedSchemas = getAuthorizedSchemas()
const authorizationDetailSchemas = getAuthorizationDetailSchemas()

const { allSchemas: authorizerAllSchemas } = useCrudSchemas(authorizerSchemas)
const { allSchemas: authorizedAllSchemas } = useCrudSchemas(authorizedSchemas)
const { allSchemas: authorizationDetailAllSchemas } = useCrudSchemas(authorizationDetailSchemas)

// 从路由获取参数
const mode = computed(() => route.query.mode || 'add')
const editId = computed(() => route.query.id || null)

const title = computed(() => {
  switch (mode.value) {
    case 'add':
      return '新增授权委托'
    case 'edit':
      return '编辑授权委托'
    case 'view':
      return '查看授权委托'
    default:
      return ''
  }
})

// 计算属性：是否为只读模式
const isReadonly = computed(() => mode.value === 'view')

// 表单验证规则
const rules = reactive({
  authorizer: [{ required: true, message: '请输入授权人', trigger: 'blur' }],
  authorizerPosition: [{ required: true, message: '请输入授权人职务', trigger: 'blur' }],
  authorizerCompany: [{ required: true, message: '请输入授权人单位', trigger: 'blur' }],
  authorizerIdCard: [
    { required: true, message: '请输入授权人身份证号码', trigger: 'blur' },
    {
      pattern:
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号码',
      trigger: 'blur'
    }
  ],
  authorized: [{ required: true, message: '请输入被授权人', trigger: 'blur' }],
  authorizedPosition: [{ required: true, message: '请输入被授权人职务', trigger: 'blur' }],
  authorizedCompany: [{ required: true, message: '请输入被授权人单位', trigger: 'blur' }],
  authorizedIdCard: [
    { required: true, message: '请输入被授权人身份证号码', trigger: 'blur' },
    {
      pattern:
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号码',
      trigger: 'blur'
    }
  ],
  authorizationType: [{ required: true, message: '请选择授权类型', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择授权起始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择授权截止日期', trigger: 'change' }],
  applicationReason: [{ required: true, message: '请输入申请理由', trigger: 'blur' }]
})

// 为每个表单创建独立的注册器
const { register: authorizerFormRegister, methods: authorizerFormMethods } = useForm()
const { register: authorizedFormRegister, methods: authorizedFormMethods } = useForm()
const { register: authorizationDetailFormRegister, methods: authorizationDetailFormMethods } =
  useForm()

// 设置表单数据
const setValue = async () => {
  const data = await getAuthorizationApplication(editId.value)

  // 分别设置各个表单的数据
  const authorizerValues = {}
  authorizerAllSchemas.formSchema.forEach((item) => {
    authorizerValues[item.field] = data[item.field]
  })

  const authorizedValues = {}
  authorizedAllSchemas.formSchema.forEach((item) => {
    authorizedValues[item.field] = data[item.field]
  })

  const authorizationDetailValues = {}
  authorizationDetailAllSchemas.formSchema.forEach((item) => {
    authorizationDetailValues[item.field] = data[item.field]
  })

  // 调用 setValue 方法，批量设置表单数据
  authorizerFormMethods.setValues(authorizerValues)
  authorizedFormMethods.setValues(authorizedValues)
  authorizationDetailFormMethods.setValues(authorizationDetailValues)
}

// 保存操作
const handleSave = async () => {
  try {
    // 验证所有表单
    const [authorizerFormValid, authorizedFormValid, authorizationDetailFormValid] =
      await Promise.all([
        authorizerFormMethods.validate().catch(() => false),
        authorizedFormMethods.validate().catch(() => false),
        authorizationDetailFormMethods.validate().catch(() => false)
      ])

    // 检查所有验证结果
    const allValid = authorizerFormValid && authorizedFormValid && authorizationDetailFormValid
    if (!allValid) {
      return
    }

    // 构建提交数据
    const [authorizerFormData, authorizedFormData, authorizationDetailFormData] = await Promise.all(
      [
        authorizerFormMethods.getFormData(),
        authorizedFormMethods.getFormData(),
        authorizationDetailFormMethods.getFormData()
      ]
    )

    const submitData = {
      ...authorizerFormData,
      ...authorizedFormData,
      ...authorizationDetailFormData,
      ...(mode.value === 'edit' && { id: editId.value })
    }

    // 统一API调用逻辑
    const apiMethod =
      mode.value === 'add' ? createAuthorizationApplication : updateAuthorizationApplication
    const actionText = mode.value === 'add' ? '新增' : '编辑'

    await apiMethod(submitData)
    message.success(`${actionText}成功`)

    // 跳转回列表页面
    router.go(-1)
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 关闭页面
const handleClose = () => {
  router.go(-1)
  emit('close')
}

// 初始化数据
const initData = async () => {
  await nextTick()

  if (editId.value && mode.value !== 'add') {
    setValue()
  }

  if (mode.value === 'add') {
    console.log(generateauthorizationCode())

    // 新增时，生成授权编号
    authorizationDetailFormMethods.setValues({
      authorizationCode: generateauthorizationCode(),
      copies: 1,
      isReauthorization: '0',
      allowReauthorization: '0'
    })
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  initData()
})
</script>

<style lang="less" scoped></style>
