/**
 * 内控流程管理相关常量
 */

interface SelectItemType {
  label: string
  value: string
}

/**
 * 流程类型枚举
 */
export enum FLOW_TYPE_ENUM {
  FINANCIAL_MANAGEMENT = 1,
  BUSINESS_ACTIVITY = 2,
  SUPPORT = 3,
  COMPLIANCE_RISK = 4,
  CROSS_FUNCTION = 5
}

/**
 * 流程类型选项列表
 */
export const FLOW_TYPE_LIST: SelectItemType[] = [
  { label: '财务管理', value: FLOW_TYPE_ENUM.FINANCIAL_MANAGEMENT },
  { label: '业务活动', value: FLOW_TYPE_ENUM.BUSINESS_ACTIVITY },
  { label: '支持性', value: FLOW_TYPE_ENUM.SUPPORT },
  { label: '合规与风险管理', value: FLOW_TYPE_ENUM.COMPLIANCE_RISK },
  { label: '跨职能综合', value: FLOW_TYPE_ENUM.CROSS_FUNCTION }
]

/**
 * 所属企业枚举
 */
export enum COMPANY_ENUM {
  ALL = '全部',
  XXXX_COMPANY = 'XXXX公司'
}

/**
 * 所属企业选项列表
 */
export const COMPANY_LIST: SelectItemType[] = [
  { label: '全部', value: COMPANY_ENUM.ALL },
  { label: 'XXXX公司', value: COMPANY_ENUM.XXXX_COMPANY }
]

/**
 * 适用范围枚举
 */
export enum APPLICABLE_SCOPE_ENUM {
  ALL_EMPLOYEES = '全体员工',
  SPECIFIC_DEPARTMENTS = 'XX部门、XX部门',
  SINGLE_DEPARTMENT = 'XX部门'
}

/**
 * 适用范围选项列表
 */
export const APPLICABLE_SCOPE_LIST: SelectItemType[] = [
  { label: '全体员工', value: APPLICABLE_SCOPE_ENUM.ALL_EMPLOYEES },
  { label: 'XX部门、XX部门', value: APPLICABLE_SCOPE_ENUM.SPECIFIC_DEPARTMENTS },
  { label: 'XX部门', value: APPLICABLE_SCOPE_ENUM.SINGLE_DEPARTMENT }
]

/**
 * 流程节点默认配置
 */
export const DEFAULT_FLOW_NODE = {
  nodeName: '',
  nodeDescription: '',
  nodeOrder: 1
}

/**
 * 流程类型映射
 */
export const FLOW_TYPE_MAP = {
  [FLOW_TYPE_ENUM.FINANCIAL_MANAGEMENT]: '财务管理',
  [FLOW_TYPE_ENUM.BUSINESS_ACTIVITY]: '业务活动',
  [FLOW_TYPE_ENUM.SUPPORT]: '支持性',
  [FLOW_TYPE_ENUM.COMPLIANCE_RISK]: '合规与风险管理',
  [FLOW_TYPE_ENUM.CROSS_FUNCTION]: '跨职能综合'
}

/**
 * 企业映射
 */
export const COMPANY_MAP = {
  [COMPANY_ENUM.ALL]: '全部',
  [COMPANY_ENUM.XXXX_COMPANY]: 'XXXX公司'
}

/**
 * 适用范围映射
 */
export const APPLICABLE_SCOPE_MAP = {
  [APPLICABLE_SCOPE_ENUM.ALL_EMPLOYEES]: '全体员工',
  [APPLICABLE_SCOPE_ENUM.SPECIFIC_DEPARTMENTS]: 'XX部门、XX部门',
  [APPLICABLE_SCOPE_ENUM.SINGLE_DEPARTMENT]: 'XX部门'
}
