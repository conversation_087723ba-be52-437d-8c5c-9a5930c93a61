/*
 * @Description: 授权委托管理相关接口
 */
import request from '@/config/axios'

// 授权委托列表响应类型
export interface AuthorizationListResponse {
  list: AuthorizationApplicationVO[]
  total: number
}

export interface AuthorizationApplicationVO {
  id: number // 主键ID
  authorizationCode: string // 授权编号
  authorizer: string // 授权人
  authorizerPosition: string // 授权人职务（下拉选择）
  authorizerUnitId: number // 授权人单位ID（调用组织架构表）
  authorizerIdCard: string // 授权人身份证号
  authorizerAddress: string // 授权人地址
  authorizedPerson: string // 被授权人
  authorizedPosition: string // 被授权人职务（下拉选择）
  authorizedUnitId: number // 被授权人单位ID（调用组织架构表）
  authorizedIdCard: string // 被授权人身份证号
  authorizedAddress: string // 被授权人地址
  authorizationType: string // 授权类型（下拉选择）
  startDate: Date // 授权起始日期（格式YYYY-MM-DD）
  endDate: Date // 授权截止日期（格式YYYY-MM-DD）
  copyCount: number // 委托份数
  signCompanyId: number // 实际签署公司ID（调用组织架构表）
  isSubAuthorization: string // 是否为转授权
  allowSubAuthorization: string // 是否允许转授权
  applicationReason: string // 申请理由
  applicationStatus: string // 申请状态
}

// 授权委托列表响应类型
export interface AuthorizationListResponse {
  list: AuthorizationApplicationVO[]
  total: number
}

// 查询授权申请分页
export const getAuthorizationApplicationPage = async (params: any) => {
  return await request.get({ url: `/law/authorization-application/page`, params })
}
// 查询授权申请详情
export const getAuthorizationApplication = async (id: number) => {
  return await request.get({ url: `/law/authorization-application/get?id=` + id })
}

// 新增授权申请
export const createAuthorizationApplication = async (data: AuthorizationApplicationVO) => {
  return await request.post({ url: `/law/authorization-application/create`, data })
}

// 修改授权申请
export const updateAuthorizationApplication = async (data: AuthorizationApplicationVO) => {
  return await request.put({ url: `/law/authorization-application/update`, data })
}

// 删除授权申请
export const deleteAuthorizationApplication = async (id: number) => {
  return await request.delete({ url: `/law/authorization-application/delete?id=` + id })
}

// 导出授权申请 Excel
export const exportAuthorizationApplication = async (params) => {
  return await request.download({ url: `/law/authorization-application/export-excel`, params })
}
