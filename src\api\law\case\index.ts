/*
 * @Description: 案件管理相关接口
 */
import request from '@/config/axios'

// 当事人信息类型
export interface PartyInfo {
  name: string
  legalRepresentative: string
  contact: string
  phone: string
  address: string
}

// 案件查询参数类型
export interface CaseQueryParams {
  caseName?: string
  caseType?: string
  caseStatus?: string
  litigationPosition?: string
  pageNum?: number
  pageSize?: number
}

// 案件数据类型
export interface CaseItem {
  id?: number | null
  caseName: string // 案件名称
  caseNo: string // 案件编号
  caseType: string // 案件类型
  caseProcedure: string // 案件程序
  involvedAmount: number // 涉案金额
  caseRemark: string // 金额备注
  isMajor: string // 是否重大
  isForeign: string // 是否涉外
  litigationReason: string // 案由
  litigationDate: string // 立案日期
  litigationPosition: string // 诉讼地位
  lawyer: string // 代理律师
  caseStatus: string // 案件状态
  acceptanceAgency: string // 受理机构
  acceptanceAgencyType: string // 受理机构类型
  acceptanceHandler: string // 受理人
  acceptanceHandlerPhone: string // 受理人电话
  // 当事人信息
  plaintiffs: PartyInfo[] // 原告信息
  defendants: PartyInfo[] // 被告信息
  thirdParties: PartyInfo[] // 第三人信息
  // 附件信息
  attachments: string[] // 附件列表
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 案件列表响应类型
export interface CaseListResponse {
  list: CaseItem[]
  total: number
}

// 获取案件列表
export const getCaseListApi = (params: CaseQueryParams) => {
  return request.get<CaseListResponse>({
    url: '/law/case-info/page',
    params
  })
}

// 获取案件详情
export const getCaseDetailApi = (id: number | string) => {
  return request.get<CaseItem>({
    url: '/law/case-info/get-detail',
    params: { id }
  })
}

// 删除案件
export const deleteCaseApi = (id: number) => {
  return request.delete({
    url: '/law/case-info/delete',
    params: { id }
  })
}

// 新增案件
export const addCaseApi = (data: Partial<CaseItem>) => {
  return request.post({
    url: '/law/case-info/create',
    data
  })
}

// 编辑案件
export const updateCaseApi = (data: Partial<CaseItem>) => {
  return request.put({
    url: '/law/case-info/update',
    data
  })
}
//导出案件
export const exportCaseApi = (params: CaseQueryParams) => {
  return request.download({
    url: 'law/case-info/export-excel',
    params
  })
}
//获取案件审批流程详情
export const getCaseApprovalProcessApi = (id: number) => {
  return request.get({
    url: '/case/approval-process',
    params: { id }
  })
}
//获取案件操作日志
export const getCaseActionLog = (params) => {
  return request.get({
    url: '/law/operation-log/page',
    params
  })
}
//获取律师队伍
export const getLawyerTeamApi = () => {
  return request.get({
    url: '/law/internal-team/ieList'
  })
}
//审批案件
export const approveCaseApi = (data: { id: number; action: string; comment?: string }) => {
  return request.post({
    url: '/case/approve',
    data
  })
}
