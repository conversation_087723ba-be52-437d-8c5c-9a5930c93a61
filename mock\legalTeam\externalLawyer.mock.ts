import { MockMethod } from 'vite-plugin-mock'

// 外聘律师数据类型
interface ExternalLawyerItem {
  id?: number
  name: string
  gender: string
  nation: string
  birthDate: string
  politicalStatus: string
  contactPhone: string
  practiceYear: string
  licenseNumber: string
  lawFirm: string
  position: string
  isArbitrator: string
  specialties: string[]
  organization: string
  attachments?: string[]
  attitudeScore?: number
  abilityScore?: number
  advantages?: string
  disadvantages?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 模拟数据
const mockData: ExternalLawyerItem[] = [
  {
    id: 1,
    name: '张三律师',
    gender: '男',
    nation: '汉族',
    birthDate: '1985-06',
    politicalStatus: '中共党员',
    contactPhone: '13800138001',
    practiceYear: '2010',
    licenseNumber: '11001201001234567',
    lawFirm: '北京德恒律师事务所',
    position: '合伙人',
    isArbitrator: '是',
    specialties: ['民商法', '知识产权法'],
    organization: '总部',
    attachments: ['律师执业证.pdf', '个人简历.doc'],
    attitudeScore: 5,
    abilityScore: 4,
    advantages: '专业能力强，沟通能力佳',
    disadvantages: '有时过于严格',
    remark: '优秀律师',
    createTime: '2024-01-01 10:00:00',
    updateTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    name: '李四律师',
    gender: '女',
    nation: '汉族',
    birthDate: '1990-03',
    politicalStatus: '群众',
    contactPhone: '13800138002',
    practiceYear: '2015',
    licenseNumber: '31001201502345678',
    lawFirm: '上海金茂律师事务所',
    position: '执业律师',
    isArbitrator: '否',
    specialties: ['经济法', '劳动法'],
    organization: '上海分部',
    attachments: ['资格证书.pdf'],
    attitudeScore: 4,
    abilityScore: 5,
    advantages: '处理案件细致，责任心强',
    disadvantages: '工作压力大时容易焦虑',
    remark: '劳动法专家',
    createTime: '2024-01-02 10:00:00',
    updateTime: '2024-01-02 10:00:00'
  },
  {
    id: 3,
    name: '王五律师',
    gender: '男',
    nation: '汉族',
    birthDate: '1982-12',
    politicalStatus: '中共党员',
    contactPhone: '13800138003',
    practiceYear: '2008',
    licenseNumber: '44001200803456789',
    lawFirm: '广州华南律师事务所',
    position: '主任',
    isArbitrator: '是',
    specialties: ['刑法', '行政法'],
    organization: '广州分部',
    attachments: ['专业证书.pdf', '获奖证书.pdf'],
    attitudeScore: 5,
    abilityScore: 5,
    advantages: '经验丰富，案例成功率高',
    disadvantages: '收费较高',
    remark: '刑事辩护专家',
    createTime: '2024-01-03 10:00:00',
    updateTime: '2024-01-03 10:00:00'
  },
  {
    id: 4,
    name: '赵六律师',
    gender: '女',
    nation: '回族',
    birthDate: '1988-09',
    politicalStatus: '无党派人士',
    contactPhone: '13800138004',
    practiceYear: '2012',
    licenseNumber: '44001201204567890',
    lawFirm: '深圳前海律师事务所',
    position: '副主任',
    isArbitrator: '否',
    specialties: ['涉外法律', '知识产权法'],
    organization: '深圳分部',
    attachments: [],
    attitudeScore: 3,
    abilityScore: 4,
    advantages: '英语水平高，涉外案件经验丰富',
    disadvantages: '时间观念有待加强',
    remark: '涉外法律专家',
    createTime: '2024-01-04 10:00:00',
    updateTime: '2024-01-04 10:00:00'
  },
  {
    id: 5,
    name: '孙七律师',
    gender: '男',
    nation: '汉族',
    birthDate: '1992-07',
    politicalStatus: '共青团员',
    contactPhone: '13800138005',
    practiceYear: '2018',
    licenseNumber: '51001201805678901',
    lawFirm: '成都西部律师事务所',
    position: '执业律师',
    isArbitrator: '否',
    specialties: ['民商法', '劳动法'],
    organization: '成都分部',
    attachments: ['毕业证书.pdf'],
    attitudeScore: 4,
    abilityScore: 3,
    advantages: '年轻有活力，学习能力强',
    disadvantages: '经验相对不足',
    remark: '有潜力的年轻律师',
    createTime: '2024-01-05 10:00:00',
    updateTime: '2024-01-05 10:00:00'
  }
]

export default [
  // 获取外聘律师列表
  {
    url: '/admin-api/api/external-lawyer/list',
    method: 'get',
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, name, gender, lawFirm, specialties, organization } = query

      let filteredData = [...mockData]

      // 筛选条件
      if (name) {
        filteredData = filteredData.filter((item) => item.name.includes(name))
      }
      if (gender) {
        filteredData = filteredData.filter((item) => item.gender === gender)
      }
      if (lawFirm) {
        filteredData = filteredData.filter((item) => item.lawFirm.includes(lawFirm))
      }
      if (specialties) {
        filteredData = filteredData.filter((item) =>
          item.specialties.some((specialty) => specialty.includes(specialties))
        )
      }
      if (organization) {
        filteredData = filteredData.filter((item) => item.organization.includes(organization))
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredData.slice(start, end)

      return {
        code: 200,
        message: 'success',
        data: {
          list,
          total: filteredData.length
        }
      }
    }
  },

  // 获取外聘律师详情
  {
    url: '/admin-api/api/external-lawyer/detail',
    method: 'get',
    response: ({ query }) => {
      const { id } = query
      const item = mockData.find((item) => item.id === Number(id))

      if (item) {
        return {
          code: 200,
          message: 'success',
          data: item
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 新增外聘律师
  {
    url: '/admin-api/api/external-lawyer/add',
    method: 'post',
    response: ({ body }) => {
      const newItem = {
        id: Date.now(),
        ...body,
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }
      mockData.push(newItem)

      return {
        code: 200,
        message: '新增成功',
        data: newItem
      }
    }
  },

  // 编辑外聘律师
  {
    url: '/admin-api/api/external-lawyer/update',
    method: 'put',
    response: ({ body }) => {
      const { id } = body
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData[index] = {
          ...mockData[index],
          ...body,
          updateTime: new Date().toLocaleString()
        }

        return {
          code: 200,
          message: '更新成功',
          data: mockData[index]
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 删除外聘律师
  {
    url: '/admin-api/api/external-lawyer/delete',
    method: 'delete',
    response: ({ query }) => {
      const { id } = query
      const index = mockData.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        mockData.splice(index, 1)
        return {
          code: 200,
          message: '删除成功'
        }
      } else {
        return {
          code: 404,
          message: '数据不存在'
        }
      }
    }
  },

  // 获取外聘律师操作日志
  {
    url: '/admin-api/api/external-lawyer/action-log',
    method: 'get',
    response: ({ query }) => {
      const { id } = query

      // 模拟操作日志数据
      const actionLogs = [
        {
          id: 1,
          time: '2024-01-15 14:30:00',
          actionName: '管理员',
          action: '创建记录',
          remark: '初始创建外聘律师信息'
        },
        {
          id: 2,
          time: '2024-01-20 09:15:00',
          actionName: '张三',
          action: '编辑信息',
          remark: '更新联系方式和专业领域'
        },
        {
          id: 3,
          time: '2024-02-01 16:45:00',
          actionName: '李四',
          action: '评分更新',
          remark: '更新律师态度和能力评分'
        },
        {
          id: 4,
          time: '2024-02-15 11:20:00',
          actionName: '王五',
          action: '附件上传',
          remark: '上传了新的执业证书'
        },
        {
          id: 5,
          time: '2024-03-01 13:10:00',
          actionName: '赵六',
          action: '信息审核',
          remark: '审核通过律师信息变更申请'
        }
      ]

      return {
        code: 200,
        message: 'success',
        data: actionLogs
      }
    }
  }
] as MockMethod[]
