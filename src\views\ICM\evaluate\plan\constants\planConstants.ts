/**
 * 评价计划管理相关常量
 */

import { SelectItemType } from '@/api/public/public.types'

/**
 * 评价计划状态枚举
 */
export enum PLAN_STATUS_ENUM {
  DRAFT = 0, // 草稿
  IN_PROGRESS = 1, // 进行中
  COMPLETED = 2, // 已完成
  PAUSED = 3 // 已暂停
}

/**
 * 评价计划状态选项列表
 */
export const PLAN_STATUS_LIST: SelectItemType[] = [
  { label: '草稿', value: PLAN_STATUS_ENUM.DRAFT },
  { label: '进行中', value: PLAN_STATUS_ENUM.IN_PROGRESS },
  { label: '已完成', value: PLAN_STATUS_ENUM.COMPLETED },
  { label: '已暂停', value: PLAN_STATUS_ENUM.PAUSED }
]

/**
 * 控制级别枚举
 */
export enum CONTROL_LEVEL_ENUM {
  FIRST_LEVEL = '一级',
  SECOND_LEVEL = '二级',
  THIRD_LEVEL = '三级'
}

/**
 * 控制级别选项列表
 */
export const CONTROL_LEVEL_LIST: SelectItemType[] = [
  { label: '一级', value: CONTROL_LEVEL_ENUM.FIRST_LEVEL },
  { label: '二级', value: CONTROL_LEVEL_ENUM.SECOND_LEVEL },
  { label: '三级', value: CONTROL_LEVEL_ENUM.THIRD_LEVEL }
]

/**
 * 控制类型枚举
 */
export enum CONTROL_TYPE_ENUM {
  PREVENTIVE = '预防型',
  DETECTIVE = '检查型',
  CORRECTIVE = '纠正型'
}

/**
 * 控制类型选项列表
 */
export const CONTROL_TYPE_LIST: SelectItemType[] = [
  { label: '预防型', value: CONTROL_TYPE_ENUM.PREVENTIVE },
  { label: '检查型', value: CONTROL_TYPE_ENUM.DETECTIVE },
  { label: '纠正型', value: CONTROL_TYPE_ENUM.CORRECTIVE }
]

/**
 * 评价单位选项列表
 */
export const EVALUATION_UNIT_LIST: SelectItemType[] = [
  { label: '风险管理部', value: 'risk_mgmt' },
  { label: '合规部', value: 'compliance' },
  { label: '法律事务部', value: 'legal' },
  { label: '运营管理部', value: 'operation' },
  { label: '信息科技部', value: 'it' },
  { label: '人力资源部', value: 'hr' },
  { label: '财务会计部', value: 'finance' },
  { label: '审计部', value: 'audit' }
]

/**
 * 被评价单位选项列表
 */
export const EVALUATED_UNIT_LIST: SelectItemType[] = [
  { label: 'XX分行', value: 'branch_1' },
  { label: 'XX支行', value: 'subbranch_1' },
  { label: 'XX分行', value: 'branch_2' },
  { label: 'XX支行', value: 'subbranch_2' },
  { label: 'XX部门', value: 'dept_1' },
  { label: 'XX部门', value: 'dept_2' }
]

/**
 * 年度选项列表
 */
export const YEAR_LIST: SelectItemType[] = [
  { label: '2023', value: '2023' },
  { label: '2024', value: '2024' },
  { label: '2025', value: '2025' },
  { label: '2026', value: '2026' }
]

/**
 * 评价计划状态映射
 */
export const PLAN_STATUS_MAP = {
  [PLAN_STATUS_ENUM.DRAFT]: '草稿',
  [PLAN_STATUS_ENUM.IN_PROGRESS]: '进行中',
  [PLAN_STATUS_ENUM.COMPLETED]: '已完成',
  [PLAN_STATUS_ENUM.PAUSED]: '已暂停'
}

/**
 * 评价计划状态颜色映射
 */
export const PLAN_STATUS_COLOR_MAP = {
  [PLAN_STATUS_ENUM.DRAFT]: 'warning',
  [PLAN_STATUS_ENUM.IN_PROGRESS]: 'primary',
  [PLAN_STATUS_ENUM.COMPLETED]: 'success',
  [PLAN_STATUS_ENUM.PAUSED]: 'info'
}

/**
 * 支持的文件类型
 */
export const SUPPORTED_FILE_TYPES = '.doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx'

/**
 * 文件上传最大大小（50MB）
 */
export const MAX_FILE_SIZE = 50 * 1024 * 1024

/**
 * 默认分页大小
 */
export const DEFAULT_PAGE_SIZE = 10

/**
 * 表格最大高度
 */
export const TABLE_MAX_HEIGHT = 'calc(70vh)'
