import Mock from 'mockjs'
import { SUCCESS_CODE } from '../public.constant'

const timeout = 1000

// 评价计划模拟数据
const evaluationPlanList = Mock.mock({
  'list|100': [
    {
      'id|+1': 1,
      planName: () => Mock.Random.ctitle(8, 25) + '评价计划',
      planYear: () => Mock.Random.pick(['2023', '2024', '2025', '2026']),
      evaluationUnit: () =>
        Mock.Random.pick([
          ['风险管理部'],
          ['合规部'],
          ['风险管理部', '合规部'],
          ['法律事务部'],
          ['运营管理部'],
          ['风险管理部', '法律事务部'],
          ['审计部', '合规部']
        ]),
      evaluatedUnit: () =>
        Mock.Random.pick([
          ['XX分行'],
          ['XX支行'],
          ['XX分行', 'XX支行'],
          ['XX部门'],
          ['XX分行', 'XX部门'],
          ['XX支行', 'XX部门']
        ]),
      planTimeRange: () => {
        const start = Mock.Random.date('yyyy-MM-dd')
        const end = Mock.Random.date('yyyy-MM-dd')
        return [start, end]
      },
      planDescription: () => Mock.Random.cparagraph(3, 8),
      taskRanges: () =>
        Mock.mock({
          'list|5-15': [
            {
              'id|+1': 1000,
              relatedNode: () => Mock.Random.ctitle(4, 10),
              relatedFlow: () => Mock.Random.ctitle(6, 15),
              controlFunctionName: () => Mock.Random.ctitle(8, 20) + '控制措施',
              controlFunctionDesc: () => Mock.Random.cparagraph(2, 5),
              controlLevel: () => Mock.Random.pick(['一级', '二级', '三级']),
              controlType: () => Mock.Random.pick(['预防型', '检查型', '纠正型']),
              responsibleDept: () => Mock.Random.pick(['XX部门', 'XX分行', 'XX支行']),
              createTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
              updateTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
            }
          ]
        }).list,
      taskRangeCount: function () {
        return this.taskRanges ? this.taskRanges.length : 0
      },
      attachments: () =>
        Mock.mock({
          'list|0-3': [
            {
              'id|+1': 2000,
              fileName: () =>
                Mock.Random.ctitle(5, 15) +
                Mock.Random.pick(['.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx']),
              fileUrl: () => Mock.Random.url('http'),
              fileSize: () => Mock.Random.integer(1024, 10485760),
              uploadTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
              uploader: () => Mock.Random.cname(),
              version: () => 'V' + Mock.Random.float(1, 5, 1, 1),
              'isActive|1': true
            }
          ]
        }).list,
      'status|1': [0, 1, 2, 3],
      createTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    }
  ]
}).list

// 评价任务范围模拟数据
const taskRangeList = Mock.mock({
  'list|200': [
    {
      'id|+1': 1000,
      relatedNode: () => Mock.Random.ctitle(4, 10),
      relatedFlow: () => Mock.Random.ctitle(6, 15),
      controlFunctionName: () => Mock.Random.ctitle(8, 20) + '控制措施',
      controlFunctionDesc: () => Mock.Random.cparagraph(2, 5),
      controlLevel: () => Mock.Random.pick(['一级', '二级', '三级']),
      controlType: () => Mock.Random.pick(['预防型', '检查型', '纠正型']),
      responsibleDept: () =>
        Mock.Random.pick(['XX部门1', 'XX分行1', 'XX支行1', 'XX部门2', 'XX分行2', 'XX支行2']),
      createTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss')
    }
  ]
}).list

// 流程类型树形结构
const flowTypeTree = [
  {
    id: '1',
    label: '全部',
    children: [
      {
        id: '1-1',
        label: '流程类型',
        children: [
          {
            id: '1-1-1',
            label: 'XXX流程',
            type: 'flow',
            value: 'XXX_FLOW'
          },
          {
            id: '1-1-2',
            label: 'XXX流程',
            type: 'flow',
            value: 'XXX_FLOW_2'
          },
          {
            id: '1-1-3',
            label: 'XXX流程',
            type: 'flow',
            value: 'XXX_FLOW_3'
          }
        ]
      },
      {
        id: '1-2',
        label: '流程类型',
        children: [
          {
            id: '1-2-1',
            label: 'XXX流程',
            type: 'flow',
            value: 'XXX_FLOW_4'
          },
          {
            id: '1-2-2',
            label: 'XXX流程',
            type: 'flow',
            value: 'XXX_FLOW_5'
          }
        ]
      },
      {
        id: '1-3',
        label: '流程类型',
        children: [
          {
            id: '1-3-1',
            label: 'XXX流程',
            type: 'flow',
            value: 'XXX_FLOW_6'
          }
        ]
      }
    ]
  }
]

// 部门选项
const departmentOptions = [
  { label: '风险管理部', value: 'risk_mgmt' },
  { label: '合规部', value: 'compliance' },
  { label: '法律事务部', value: 'legal' },
  { label: '运营管理部', value: 'operation' },
  { label: '信息科技部', value: 'it' },
  { label: '人力资源部', value: 'hr' },
  { label: '财务会计部', value: 'finance' },
  { label: '审计部', value: 'audit' }
]

// 单位选项
const unitOptions = [
  { label: 'XX分行', value: 'branch_1' },
  { label: 'XX支行', value: 'subbranch_1' },
  { label: 'XX分行', value: 'branch_2' },
  { label: 'XX支行', value: 'subbranch_2' },
  { label: 'XX部门', value: 'dept_1' },
  { label: 'XX部门', value: 'dept_2' }
]

export default [
  // 获取评价计划分页列表
  {
    url: '/admin-api/api/ICM/evaluate/plan/page',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        planName,
        evaluationUnit,
        evaluatedUnit,
        planStatus,
        planYear,
        timeRange
      } = query

      let filteredList = [...evaluationPlanList]

      // 筛选条件
      if (planName) {
        filteredList = filteredList.filter((item) => item.planName.includes(planName))
      }

      if (evaluationUnit) {
        filteredList = filteredList.filter((item) =>
          item.evaluationUnit.some((unit) => unit.includes(evaluationUnit))
        )
      }

      if (evaluatedUnit) {
        filteredList = filteredList.filter((item) =>
          item.evaluatedUnit.some((unit) => unit.includes(evaluatedUnit))
        )
      }

      if (planStatus !== undefined && planStatus !== '') {
        filteredList = filteredList.filter((item) => item.status === Number(planStatus))
      }

      if (planYear) {
        filteredList = filteredList.filter((item) => item.planYear === planYear)
      }

      if (timeRange && timeRange.length === 2) {
        filteredList = filteredList.filter((item) => {
          const itemStart = new Date(item.planTimeRange[0])
          const itemEnd = new Date(item.planTimeRange[1])
          const queryStart = new Date(timeRange[0])
          const queryEnd = new Date(timeRange[1])

          return itemStart >= queryStart && itemEnd <= queryEnd
        })
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + Number(pageSize)
      const list = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list,
          total: filteredList.length
        }
      }
    }
  },

  // 获取评价计划详情
  {
    url: '/admin-api/api/ICM/evaluate/plan/get',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const item = evaluationPlanList.find((item) => item.id === Number(id))

      return {
        code: SUCCESS_CODE,
        data: item || null
      }
    }
  },

  // 创建评价计划
  {
    url: '/admin-api/api/ICM/evaluate/plan/create',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const newItem = {
        ...body,
        id: evaluationPlanList.length + 1,
        status: 0, // 新建时默认为草稿状态
        taskRangeCount: body.taskRanges ? body.taskRanges.length : 0,
        attachments: body.attachments || [],
        createTime: new Date().toISOString().replace('T', ' ').substr(0, 19),
        updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
      }

      evaluationPlanList.push(newItem)

      return {
        code: SUCCESS_CODE,
        data: newItem.id
      }
    }
  },

  // 更新评价计划
  {
    url: '/admin-api/api/ICM/evaluate/plan/update',
    method: 'put',
    timeout,
    response: ({ body }) => {
      const { id } = body
      const index = evaluationPlanList.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        evaluationPlanList[index] = {
          ...evaluationPlanList[index],
          ...body,
          taskRangeCount: body.taskRanges ? body.taskRanges.length : 0,
          updateTime: new Date().toISOString().replace('T', ' ').substr(0, 19)
        }

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '更新失败'
      }
    }
  },

  // 删除评价计划
  {
    url: '/admin-api/api/ICM/evaluate/plan/delete',
    method: 'delete',
    timeout,
    response: ({ query }) => {
      const { id } = query
      const index = evaluationPlanList.findIndex((item) => item.id === Number(id))

      if (index !== -1) {
        evaluationPlanList.splice(index, 1)

        return {
          code: SUCCESS_CODE,
          data: true
        }
      }

      return {
        code: 500,
        message: '删除失败'
      }
    }
  },

  // 导出评价计划
  {
    url: '/admin-api/api/ICM/evaluate/plan/export',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: SUCCESS_CODE,
        data: {
          url: 'http://example.com/export/evaluation-plan.xlsx',
          filename: '评价计划导出.xlsx'
        }
      }
    }
  },

  // 获取评价任务范围分页列表
  {
    url: '/admin-api/api/ICM/evaluate/taskRange/page',
    method: 'get',
    timeout,
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        keyword,
        controlFunctionName,
        controlLevel,
        controlType,
        flowType
      } = query

      let filteredList = [...taskRangeList]

      // 筛选条件
      if (keyword) {
        filteredList = filteredList.filter(
          (item) =>
            item.controlFunctionName.includes(keyword) ||
            item.controlFunctionDesc.includes(keyword) ||
            item.relatedFlow.includes(keyword) ||
            item.relatedNode.includes(keyword)
        )
      }

      if (controlFunctionName) {
        filteredList = filteredList.filter((item) =>
          item.controlFunctionName.includes(controlFunctionName)
        )
      }

      if (controlLevel) {
        filteredList = filteredList.filter((item) => item.controlLevel === controlLevel)
      }

      if (controlType) {
        filteredList = filteredList.filter((item) => item.controlType === controlType)
      }

      if (flowType) {
        filteredList = filteredList.filter((item) => item.relatedFlow.includes(flowType))
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + Number(pageSize)
      const list = filteredList.slice(start, end)

      return {
        code: SUCCESS_CODE,
        data: {
          list,
          total: filteredList.length
        }
      }
    }
  },

  // 获取流程类型树形结构
  {
    url: '/admin-api/api/ICM/evaluate/flowType/tree',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: SUCCESS_CODE,
        data: flowTypeTree
      }
    }
  },

  // 获取部门选项列表
  {
    url: '/admin-api/api/ICM/evaluate/department/options',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: SUCCESS_CODE,
        data: departmentOptions
      }
    }
  },

  // 获取单位选项列表
  {
    url: '/admin-api/api/ICM/evaluate/unit/options',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: SUCCESS_CODE,
        data: unitOptions
      }
    }
  }
]
