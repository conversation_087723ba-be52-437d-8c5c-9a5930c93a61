<template>
  <div class="">
    <Title title="操作日志">
      <Table :columns="tableColumns" :data="tableData" v-loading="loading"></Table>
    </Title>
  </div>
</template>

<script setup lang="ts">
import { Title, Table } from '@/components'
import { defineProps, watch, ref, onMounted } from 'vue'
import { getPopularLawKnowledgeActionLog } from '@/api/law/knowledge/popularLaw'
import type { PopularLawKnowledgeActionLogItem } from '@/api/law/knowledge/popularLaw'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const tableData = ref<PopularLawKnowledgeActionLogItem[]>([])
const loading = ref(false)

const tableColumns = [
  {
    label: '时间',
    field: 'time',
    width: '200px'
  },
  {
    label: '操作人',
    field: 'actionName',
    width: '150px'
  },
  {
    label: '操作',
    field: 'action',
    width: '150px'
  },
  {
    label: '备注',
    field: 'remark'
  }
]

const getActionLogData = async (id: number) => {
  if (!id) return

  try {
    loading.value = true
    const res = await getPopularLawKnowledgeActionLog(id)
    tableData.value = res
  } catch (error) {
    console.error('获取操作日志失败:', error)
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 监听id变化
watch(
  () => props.data.id,
  (newId) => {
    if (newId) {
      getActionLogData(newId)
    }
  },
  { immediate: true }
)

onMounted(() => {
  if (props.data?.id) {
    getActionLogData(props.data.id)
  }
})
</script>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
