import { CrudSchema } from '@/hooks/web/useCrudSchemas'

/**
 * 评价计划管理 - 统一Schema配置
 */
export const taskSchemas: CrudSchema[] = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    width: 80
  },
  // ==================== 基础信息组 ====================
  {
    field: 'year',
    label: '年度',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择年度',
        type: 'year'
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择年度',
        type: 'year',
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    //评价措施任务名称
    field: 'controlFunctionName',
    label: '评价措施任务名称',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: false,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入评价措施任务名称'
      }
    },
    table: {
      width: 180
    }
  },
  {
    //关联节点
    field: 'relatedNode',
    label: '关联节点',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false
  },
  {
    //关联流程
    field: 'relatedFlow',
    label: '关联流程',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false
  },
  {
    field: 'planName',
    label: '评价计划名称',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入评价计划名称'
      }
    },
    table: {
      width: 180
    },
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入评价计划名称',
        style: { width: '100%' },
        maxlength: 200,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },

  {
    field: 'evaluationUnit',
    label: '评价单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择评价单位',
        options: []
      }
    },
    table: {
      formatter: (row) => {
        return Array.isArray(row.evaluationUnit)
          ? row.evaluationUnit.join(', ')
          : row.evaluationUnit
      }
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择评价单位(多选)',
        options: [],
        style: { width: '100%' },
        multiple: true,
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'evaluatedUnit',
    label: '被评价单位',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择被评价单位',
        options: []
      }
    },
    table: {
      width: 150,
      formatter: (row) => {
        return Array.isArray(row.evaluatedUnit) ? row.evaluatedUnit.join(', ') : row.evaluatedUnit
      }
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择被评价单位(多选)',
        options: [],
        style: { width: '100%' },
        multiple: true,
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'planTimeRange',
    label: '评价计划时间范围',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'DatePicker',
      componentProps: {
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        style: { width: '100%' }
      }
    },
    table: {
      width: 180,
      formatter: (row) => {
        if (Array.isArray(row.planTimeRange) && row.planTimeRange.length === 2) {
          return `${row.planTimeRange[0]} 至 ${row.planTimeRange[1]}`
        }
        return row.planTimeRange
      }
    },
    form: {
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    //责任部门
    field: 'responsibleDept',
    label: '责任部门',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false
  },
  {
    field: 'taskType',
    label: '任务类型',
    isSearch: true,
    isTable: false,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择任务类型',
        options: [
          { label: '关键控制', value: 'KEY_CONTROL' },
          { label: '发现型', value: 'DISCOVERY_TYPE' },
          { label: '发现型控制', value: 'DISCOVERY_CONTROL' }
        ],
        clearable: true
      }
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择任务类型',
        options: [
          { label: '关键控制', value: 'KEY_CONTROL' },
          { label: '发现型', value: 'DISCOVERY_TYPE' },
          { label: '发现型控制', value: 'DISCOVERY_CONTROL' }
        ],
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'taskStatus',
    label: '任务状态',
    isSearch: true,
    isTable: false,
    isForm: false,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择任务状态',
        options: [
          { label: '未开始', value: 'NOT_STARTED' },
          { label: '进行中', value: 'IN_PROGRESS' },
          { label: '已完成', value: 'COMPLETED' },
          { label: '已暂停', value: 'PAUSED' }
        ],
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'status',
    label: '状态',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 100,
      slots: {
        default: 'status'
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'createTime',
    label: '创建时间',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 180,
      formatter: (row) => {
        return row.createTime ? row.createTime.replace('T', ' ') : ''
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'action',
    label: '操作',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {
      width: 240,
      fixed: 'right',
      slots: {
        default: 'action'
      }
    }
  }
]

/**
 * 评价任务范围 - Schema配置
 */
export const taskRangeSchemas: CrudSchema[] = [
  {
    field: 'relatedNode',
    label: '关联节点',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  },
  {
    field: 'relatedFlow',
    label: '关联流程',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  },
  {
    field: 'controlFunctionName',
    label: '控制措施名称',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: false,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入控制措施名称'
      }
    },
    table: {}
  },
  {
    field: 'controlFunctionDesc',
    label: '控制措施描述',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  },
  {
    field: 'controlLevel',
    label: '控制级别',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: false,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择控制级别',
        options: []
      }
    },
    table: {}
  },
  {
    field: 'controlType',
    label: '控制类型',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: false,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择控制类型',
        options: []
      }
    },
    table: {}
  },
  {
    field: 'responsibleDept',
    label: '责任部门',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {}
  }
]

/**
 * 表单验证规则
 */
export const getPlanFormRules = () => {
  return {
    planName: [
      { required: true, message: '请输入评价计划名称', trigger: 'blur' },
      { min: 2, max: 200, message: '评价计划名称长度在 2 到 200 个字符', trigger: 'blur' }
    ],
    planYear: [{ required: true, message: '请选择评价计划年度', trigger: 'change' }],
    evaluationUnit: [{ required: true, message: '请选择评价单位', trigger: 'change' }],
    evaluatedUnit: [{ required: true, message: '请选择被评价单位', trigger: 'change' }],
    planTimeRange: [{ required: true, message: '请选择评价计划时间范围', trigger: 'change' }],
    planDescription: [{ max: 500, message: '评价计划描述长度不能超过 500 个字符', trigger: 'blur' }]
  }
}
