<template>
  <div class="advanced-checkbox">
    <div class="checkbox-container">
      <el-checkbox-group v-model="checkboxValue" @change="handleChange">
        <el-checkbox
          v-for="(option, index) in allOptions"
          :key="`${option}-${index}`"
          :label="option"
          class="checkbox-item"
        >
          {{ option }}
          <Button
            v-if="index >= defaultOptionsCount"
            type="text"
            size="small"
            @click.stop="removeCustomOption(index)"
            class="remove-btn"
          >
            ×
          </Button>
        </el-checkbox>
      </el-checkbox-group>

      <div class="add-option-section">
        <el-input
          v-model="newOption"
          :placeholder="`添加自定义${type === 'advantage' ? '优点' : '缺点'}`"
          size="small"
          style="width: 150px; margin-right: 8px"
          @keyup.enter="addCustomOption"
        />
        <Button type="text" size="small" @click="addCustomOption"> 添加 </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Button } from '@/components'

interface Props {
  modelValue?: string | string[]
  type?: 'advantage' | 'disadvantage' // 优点或缺点
  outputMode?: 'string' | 'array' // 输出模式：字符串拼接或数组
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  type: 'advantage',
  outputMode: 'string'
})

const emit = defineEmits<Emits>()

// 预设选项
const defaultOptions = {
  advantage: ['耐心细致', '敏锐洞察', '经验丰富', '思维敏捷', '能言善辩'],
  disadvantage: ['粗心大意', '不负责任', '经验不足', '知识匮乏', '沟通障碍']
}

// 自定义选项
const customOptions = ref<string[]>([])
const newOption = ref('')

// 默认选项数量
const defaultOptionsCount = computed(() => defaultOptions[props.type].length)

// 所有选项（默认 + 自定义）
const allOptions = computed(() => [...defaultOptions[props.type], ...customOptions.value])

// 将 modelValue 转换为数组格式
const parseModelValue = (value: string | string[]): string[] => {
  if (Array.isArray(value)) {
    return value
  }
  if (typeof value === 'string' && value.trim()) {
    return value
      .split(',')
      .map((item) => item.trim())
      .filter((item) => item)
  }
  return []
}

// 初始化自定义选项：从 modelValue 中提取不在默认选项中的值
const initializeCustomOptions = () => {
  const currentValues = parseModelValue(props.modelValue)
  const defaultOpts = defaultOptions[props.type]
  const customOpts = currentValues.filter((value) => !defaultOpts.includes(value))
  customOptions.value = [...customOpts]
}

// 初始化时恢复自定义选项
initializeCustomOptions()

// 复选框绑定值
const checkboxValue = ref<string[]>(parseModelValue(props.modelValue))

// 处理选择变化
const handleChange = (value: string[]) => {
  if (props.outputMode === 'string') {
    emit('update:modelValue', value.join(','))
  } else {
    emit('update:modelValue', value)
  }
}

// 添加自定义选项
const addCustomOption = () => {
  if (newOption.value.trim() && !allOptions.value.includes(newOption.value.trim())) {
    customOptions.value.push(newOption.value.trim())
    newOption.value = ''
  }
}

// 移除自定义选项
const removeCustomOption = (index: number) => {
  const customIndex = index - defaultOptionsCount.value
  if (customIndex >= 0) {
    const removedOption = customOptions.value[customIndex]
    customOptions.value.splice(customIndex, 1)

    // 如果被移除的选项在已选中的项中，也要移除
    const currentValue = [...checkboxValue.value]
    const removedIndex = currentValue.indexOf(removedOption)
    if (removedIndex > -1) {
      currentValue.splice(removedIndex, 1)
      checkboxValue.value = currentValue
      if (props.outputMode === 'string') {
        emit('update:modelValue', currentValue.join(','))
      } else {
        emit('update:modelValue', currentValue)
      }
    }
  }
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    const currentValues = parseModelValue(newVal)
    checkboxValue.value = currentValues

    // 恢复自定义选项
    const defaultOpts = defaultOptions[props.type]
    const customOpts = currentValues.filter((value) => !defaultOpts.includes(value))
    customOptions.value = [...customOpts]
  },
  { deep: true }
)

// 监听 type 变化，重新初始化
watch(
  () => props.type,
  () => {
    // 重新初始化自定义选项，因为类型变了默认选项也变了
    initializeCustomOptions()
    // 重新解析 modelValue
    checkboxValue.value = parseModelValue(props.modelValue)
  }
)
</script>

<style scoped lang="scss">
.advanced-checkbox {
  .checkbox-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
  }

  :deep(.el-checkbox-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  .checkbox-item {
    display: flex;
    align-items: center;
    position: relative;
    margin-right: 0;
    margin-bottom: 0;

    .remove-btn {
      margin-left: 8px;
      padding: 0;
      min-height: auto;
      color: #f56c6c;
      font-size: 16px;
      font-weight: bold;

      &:hover {
        color: #f78989;
      }
    }
  }

  .add-option-section {
    display: flex;
    align-items: center;
    margin-top: 0;
  }
}
</style>
