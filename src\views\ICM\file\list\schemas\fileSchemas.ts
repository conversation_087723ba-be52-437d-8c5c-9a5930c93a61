import { CrudSchema } from '@/hooks/web/useCrudSchemas'
import {
  FILE_STATUS_LIST,
  FILE_TYPE_LIST,
  DEPARTMENT_LIST,
  COMPANY_LIST
} from '../constants/fileConstants'

/**
 * 内控文件管理 - 统一Schema配置
 */
export const fileSchemas: CrudSchema[] = [
  // ==================== 基础信息组 ====================
  {
    field: 'title',
    label: '内控文件标题',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入内控文件标题'
      }
    },
    table: {
      // width: 200,
      // showOverflowTooltip: true
    },
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入内控文件标题',
        style: { width: '100%' },
        maxlength: 200,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'fileNo',
    label: '发文号',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Input',
      componentProps: {
        placeholder: '请输入发文号'
      }
    },
    table: {
      width: 150
    },
    form: {
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请输入发文号',
        style: { width: '100%' },
        maxlength: 100,
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'department',
    label: '发文部门',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择发文部门',
        options: DEPARTMENT_LIST
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择发文部门',
        options: DEPARTMENT_LIST,
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'fileType',
    label: '文件类型',
    isSearch: true,
    isTable: true,
    isForm: true,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择文件类型',
        options: FILE_TYPE_LIST
      }
    },
    table: {
      width: 120
    },
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择文件类型',
        options: FILE_TYPE_LIST,
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'belongCompany',
    label: '所属企业',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择所属企业',
        options: COMPANY_LIST,
        style: { width: '100%' },
        clearable: true
      }
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'issueDate',
    label: '文件印发时间',
    isSearch: false,
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 120
    },
    form: {
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择文件印发时间',
        style: { width: '100%' },
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    },
    detail: {
      span: 1,
      dateFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'expireDate',
    label: '文件失效时间',
    isSearch: false,
    isTable: true,
    isForm: true,
    isDetail: true,
    table: {
      width: 120
    },
    form: {
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        placeholder: '请选择文件失效时间',
        style: { width: '100%' },
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    },
    detail: {
      span: 1,
      dateFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'status',
    label: '文件状态',
    isSearch: true,
    isTable: true,
    isForm: false,
    isDetail: true,
    search: {
      component: 'Select',
      componentProps: {
        placeholder: '请选择文件状态',
        options: FILE_STATUS_LIST
      }
    },
    table: {
      width: 100
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'description',
    label: '文件描述',
    isSearch: false,
    isTable: false,
    isForm: true,
    isDetail: true,
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入文件描述',
        type: 'textarea',
        rows: 3,
        maxlength: 500,
        clearable: true
      }
    },
    detail: {
      span: 2
    }
  },
  {
    field: 'version',
    label: '版本号',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 100
    },
    detail: {
      span: 1
    }
  },
  {
    field: 'currentVersion',
    label: '当前版本',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: true,
    table: {
      width: 100
    },
    detail: {
      span: 1
    }
  },
  // ==================== 时间信息组 ====================
  {
    field: 'createTime',
    label: '创建时间',
    isSearch: false,
    isTable: false,
    isForm: false,
    isDetail: true,
    table: {
      width: 160
    },
    detail: {
      span: 1,
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'updateTime',
    label: '更新时间',
    isSearch: false,
    isTable: false,
    isForm: false,
    isDetail: true,
    detail: {
      span: 1,
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  // ==================== 操作列 ====================
  {
    field: 'action',
    label: '操作',
    isSearch: false,
    isTable: true,
    isForm: false,
    isDetail: false,
    table: {
      width: 200,
      fixed: 'right'
    }
  }
]

/**
 * 获取基础信息字段（用于详情页面分组显示）
 */
export const getBasicInfoSchemas = (): CrudSchema[] => {
  return fileSchemas.filter((schema) =>
    [
      'title',
      'fileNo',
      'department',
      'fileType',
      'belongCompany',
      'issueDate',
      'expireDate',
      'status',
      'description',
      'version',
      'currentVersion',
      'createTime',
      'updateTime'
    ].includes(schema.field)
  )
}

/**
 * 获取表单验证规则
 */
export const getFileFormRules = () => {
  return {
    title: [
      { required: true, message: '请输入内控文件标题', trigger: 'blur' },
      { min: 2, max: 200, message: '内控文件标题长度为 2 到 200 个字符', trigger: 'blur' }
    ],
    fileNo: [
      { required: true, message: '请输入发文号', trigger: 'blur' },
      { max: 100, message: '发文号长度不能超过 100 个字符', trigger: 'blur' }
    ],
    department: [{ required: true, message: '请选择发文部门', trigger: 'change' }],
    fileType: [{ required: true, message: '请选择文件类型', trigger: 'change' }],
    belongCompany: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
    issueDate: [{ required: true, message: '请选择文件印发时间', trigger: 'change' }],
    expireDate: [{ required: true, message: '请选择文件失效时间', trigger: 'change' }],
    description: [
      { required: true, message: '请输入文件描述', trigger: 'blur' },
      { min: 10, max: 500, message: '文件描述长度为 10 到 500 个字符', trigger: 'blur' }
    ]
  }
}
