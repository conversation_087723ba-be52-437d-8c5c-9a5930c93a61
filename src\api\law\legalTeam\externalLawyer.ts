/*
 * @Description: 外聘律师管理相关接口
 */
import request from '@/config/axios'

// 外聘律师数据类型
export interface ExternalLawyerItem {
  id?: number
  name: string // 姓名
  gender: string // 性别
  nation: string // 民族
  birthDate: string // 出生年月
  politicalStatus: string // 政治面貌
  contactPhone: string // 联系方式
  practiceYear: string // 开始执业年份
  licenseNumber: string // 执业证号
  lawFirm: string // 所在律所
  position: string // 所内职务
  isArbitrator: string // 是否仲裁员
  specialties: string[] // 专业领域
  organization: string // 所属单位
  attachments?: string[] // 相关附件
  // 评价信息组
  attitudeScore?: number // 态度评分
  abilityScore?: number // 能力评分
  advantages?: string // 律师优点
  disadvantages?: string // 律师缺点
  remark?: string // 备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 外聘律师操作日志类型
export interface ExternalLawyerActionLogItem {
  id: number
  time: string // 操作时间
  actionName: string // 操作人
  action: string // 操作类型
  remark?: string // 备注信息
}

// 外聘律师查询参数类型
export interface ExternalLawyerQueryParams {
  name?: string
  gender?: string
  lawFirm?: string
  specialties?: string
  organization?: string
  pageNum?: number
  pageSize?: number
}

// 外聘律师列表响应类型
export interface ExternalLawyerListResponse {
  list: ExternalLawyerItem[]
  total: number
}

// 获取外聘律师列表
export const getExternalLawyerListApi = (params: ExternalLawyerQueryParams) => {
  return request.get<ExternalLawyerListResponse>({
    url: '/law/external-lawyer/page',
    params
  })
}

// 获取外聘律师详情
export const getExternalLawyerDetailApi = (id: number) => {
  return request.get<ExternalLawyerItem>({
    url: '/law/external-lawyer/get',
    params: { id }
  })
}

// 删除外聘律师
export const deleteExternalLawyerApi = (id: number) => {
  return request.delete({
    url: '/law/external-lawyer/delete',
    params: { id }
  })
}

// 新增外聘律师
export const addExternalLawyerApi = (data: Partial<ExternalLawyerItem>) => {
  return request.post({
    url: '/law/external-lawyer/create',
    data
  })
}

// 编辑外聘律师
export const updateExternalLawyerApi = (data: Partial<ExternalLawyerItem>) => {
  return request.put({
    url: '/law/external-lawyer/update',
    data
  })
}
//导出外聘律师
export const exportExternalLawyerApi = (params: ExternalLawyerQueryParams) => {
  return request.download({
    url: '/law/external-lawyer/export-excel',
    params
  })
}
