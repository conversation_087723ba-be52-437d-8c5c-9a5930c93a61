<script setup lang="tsx">
import { computed, defineExpose, nextTick } from 'vue'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getInteriorTeamDetailApi,
  addInteriorTeamApi,
  updateInteriorTeamApi
} from '@/api/law/legalTeam/interiorTeam'
import { interiorTeamSchemas, getInteriorTeamFormRules } from '../schemas/interiorTeamSchemas'
import { useMessage } from '@/hooks/web/useMessage'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle, layoutSize, isCollapsed } = useLayout()

// 使用 CRUD Schemas 生成配置
const { allSchemas } = useCrudSchemas(interiorTeamSchemas)

const message = useMessage()
const emit = defineEmits(['close', 'save'])

const mode = ref<'add' | 'edit' | 'view'>('add')
const visible = ref(false)
const editId = ref<number | null>(null)

const title = computed(() => {
  switch (mode.value) {
    case 'add':
      return '新增法律顾问'
    case 'edit':
      return '编辑法律顾问'
    case 'view':
      return '查看法律顾问'
    default:
      return ''
  }
})

// 计算属性：是否为只读模式
const isReadonly = computed(() => mode.value === 'view')

// 表单验证规则
const rules = getInteriorTeamFormRules()

// 表单管理
const { register: formRegister, methods: formMethods } = useForm()

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  editId.value = null
  // 清空表单数据
  formMethods.setValues({})
  emit('close')
}

// 设置表单数据
const setValue = async () => {
  if (editId.value) {
    const data = await getInteriorTeamDetailApi(editId.value)
    if (data.professionalFields) {
      data.professionalFields = data.professionalFields.split(',')
    }
    formMethods.setValues(data)
  }
}

// 保存操作
const handleSaveAction = async () => {
  try {
    const valid = await formMethods.validate()
    if (!valid) return

    const formData = await formMethods.getFormData()
    const submitData = {
      ...formData,
      ...(mode.value === 'edit' && editId.value && { id: editId.value })
    }

    const apiMethod = mode.value === 'add' ? addInteriorTeamApi : updateInteriorTeamApi
    const actionText = mode.value === 'add' ? '新增' : '编辑'
    if (submitData.professionalFields) {
      submitData.professionalFields = submitData.professionalFields.join(',')
    }
    await apiMethod(submitData)
    message.success(`${actionText}成功`)

    visible.value = false
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 打开弹窗
const open = async (type: 'add' | 'edit' | 'view', id?: number) => {
  mode.value = type
  visible.value = true

  await nextTick()

  if (id && type !== 'add') {
    editId.value = id
    await setValue()
  }
}

defineExpose({
  open
})
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            :showSave="!isReadonly"
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSaveAction"
          />
        </div>
      </div>

      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基本信息</div>
          <div class="mt-12px">
            <Form
              :disabled="isReadonly"
              :schema="allSchemas.formSchema"
              @register="formRegister"
              isCol
              :rules="rules"
            />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
