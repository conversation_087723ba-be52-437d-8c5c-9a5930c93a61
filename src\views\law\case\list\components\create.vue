<script setup lang="tsx">
import { computed, reactive, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Back, Form } from '@/components'
import { FormSchema } from '@/types/form'
import PartyTable from './PartyTable.vue'
import { usePartyManagement, PartyInfo } from '../hooks/usePartyManagement'
import { useForm } from '@/hooks/web/useForm'
import { getCaseDetailApi, addCase<PERSON><PERSON>, updateCase<PERSON><PERSON> } from '@/api/law/case'
import { FORM_REGEX, CASE_NO_PREFIX } from '@/constants/case'
import { useMessage } from '@/hooks/web/useMessage'
import dayjs from 'dayjs'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getBasicSchemas,
  getLitigationSchemas,
  getCaseProcedureOptions,
  caseFormRules
} from '../../schemas/caseSchemas'

// ==================== 类型定义 ====================
interface CaseParty extends PartyInfo {
  partyType: 'PLAINTIFF' | 'DEFENDANT' | 'THIRD_PARTY'
}

interface CaseDetailData {
  [key: string]: any
  casePartiess?: CaseParty[]
  attachments?: string | string[]
}

interface AllSchemasType {
  BasicSchema: FormSchema[]
  LitigationSchema: FormSchema[]
}

interface SubmitData {
  [key: string]: any
  casePartiess: CaseParty[]
  id?: number
  attachments?: string | string[]
}

// ==================== 基础设置 ====================
const message = useMessage()
const route = useRoute()
const router = useRouter()

// ==================== Schema配置 ====================
const basicSchemas = getBasicSchemas()
const litigationSchemas = getLitigationSchemas()
const { allSchemas: basicAllSchemas } = useCrudSchemas(basicSchemas)
const { allSchemas: litigationAllSchemas } = useCrudSchemas(litigationSchemas)

const allSchemas = reactive<AllSchemasType>({
  BasicSchema: basicAllSchemas.formSchema,
  LitigationSchema: litigationAllSchemas.formSchema
})

// ==================== 路由参数处理 ====================
const mode = computed(() => {
  const routeName = route.name as string
  if (routeName?.includes('add')) return 'add'
  if (routeName?.includes('edit')) return 'edit'
  if (routeName?.includes('view')) return 'view'
  return (route.query.mode as 'add' | 'edit' | 'view') || 'add'
})

const editId = computed(() => {
  return route.query.id ? Number(route.query.id) : null
})

const title = computed(() => {
  const titles = {
    add: '新增案件',
    edit: '编辑案件',
    view: '查看案件'
  }
  return titles[mode.value] || ''
})

const isReadonly = computed(() => mode.value === 'view')

// ==================== 表单管理 ====================
const { register: basicFormRegister, methods: basicFormMethods } = useForm()
const { register: litigationFormRegister, methods: litigationFormMethods } = useForm()

// ==================== 当事人管理 ====================
const plaintiff = reactive(usePartyManagement()) // 原告/申请人
const defendant = reactive(usePartyManagement()) // 被告/被申请人
const thirdParty = reactive(usePartyManagement()) // 第三人

const plaintiffRef = ref()
const defendantRef = ref()
const thirdPartyRef = ref()

// ==================== 表单配置处理 ====================
const handleCaseTypeChange = (value: string) => {
  basicFormMethods.setValues({ caseProcess: '' })
  const caseProcessSchema = allSchemas.BasicSchema.find((item) => item.field === 'caseProcess')
  if (caseProcessSchema) {
    caseProcessSchema.componentProps!.options = getCaseProcedureOptions(value)
  }
}

// 设置案件类型变化事件
const setupCaseTypeChangeHandler = () => {
  allSchemas.BasicSchema.forEach((schema) => {
    if (schema.field === 'caseType') {
      schema.componentProps = {
        ...schema.componentProps,
        onChange: handleCaseTypeChange
      }
    }
  })
}

// ==================== 数据处理工具函数 ====================
const generateCaseCode = () => {
  return `${CASE_NO_PREFIX}${dayjs().format('YYYYMMDDHHmmss')}`
}

const processFormData = (basicData: any, litigationData: any) => {
  return {
    ...basicData,
    ...litigationData
  }
}

const processPartyData = (): CaseParty[] => {
  return [
    ...plaintiff.list.map((item) => ({ ...item, partyType: 'PLAINTIFF' as const })),
    ...defendant.list.map((item) => ({ ...item, partyType: 'DEFENDANT' as const })),
    ...thirdParty.list.map((item) => ({ ...item, partyType: 'THIRD_PARTY' as const }))
  ]
}

const processAttachmentData = (data: SubmitData) => {
  if (data.attachments && Array.isArray(data.attachments) && data.attachments.length > 0) {
    data.attachments = data.attachments[0]
  }
}

const groupPartiesByType = (parties: CaseParty[]) => {
  return {
    plaintiffs: parties.filter((item) => item.partyType === 'PLAINTIFF'),
    defendants: parties.filter((item) => item.partyType === 'DEFENDANT'),
    thirdParties: parties.filter((item) => item.partyType === 'THIRD_PARTY')
  }
}

// ==================== 数据加载 ====================
const loadCaseData = async () => {
  if (!editId.value) return

  const data = (await getCaseDetailApi(editId.value)) as CaseDetailData

  // 设置基础表单数据
  const basicValues = {}
  allSchemas.BasicSchema.forEach((item) => {
    basicValues[item.field] = data[item.field]
  })

  handleCaseTypeChange(data.caseType)
  // 设置诉讼表单数据
  const litigationValues = {}
  allSchemas.LitigationSchema.forEach((item) => {
    litigationValues[item.field] = data[item.field]
  })
  litigationValues.lawyerInfo = [data.lawyerType, data.lawyerId]
  // 批量设置表单数据)

  basicFormMethods.setValues(basicValues)
  litigationFormMethods.setValues(litigationValues)

  // 处理当事人数据
  const caseParties = data.casePartiess || []
  if (caseParties.length > 0) {
    const { plaintiffs, defendants, thirdParties } = groupPartiesByType(caseParties)

    plaintiff.setData(plaintiffs)
    defendant.setData(defendants)
    thirdParty.setData(thirdParties)
  }

  // 更新案件程序选项
  updateCaseProcedureOptions(data.caseType)
}

const updateCaseProcedureOptions = (caseType: string) => {
  const caseProcessSchema = allSchemas.BasicSchema.find((item) => item.field === 'caseProcess')
  if (caseProcessSchema) {
    caseProcessSchema.componentProps!.options = getCaseProcedureOptions(caseType)
  }
}

// ==================== 表单验证 ====================
const validateAllForms = async () => {
  const validationPromises = [
    basicFormMethods.validate().catch(() => false),
    litigationFormMethods.validate().catch(() => false),
    plaintiffRef.value?.validateAll().catch(() => false),
    defendantRef.value?.validateAll().catch(() => false),
    thirdPartyRef.value?.validateAll().catch(() => false)
  ]

  const results = await Promise.all(validationPromises)
  return results.every(Boolean)
}

// ==================== 数据提交 ====================
const buildSubmitData = async (): Promise<SubmitData> => {
  const [basicFormData, litigationFormData] = await Promise.all([
    basicFormMethods.getFormData(),
    litigationFormMethods.getFormData()
  ])

  const formData = processFormData(basicFormData, litigationFormData)
  const casePartiess = processPartyData()

  const submitData: SubmitData = {
    ...formData,
    casePartiess,
    ...(mode.value === 'edit' && editId.value && { id: editId.value })
  }
  submitData.lawyerType = submitData.lawyerInfo[0]
  submitData.lawyerId = submitData.lawyerInfo[1]
  processAttachmentData(submitData)
  return submitData
}

const submitCaseData = async (data: SubmitData) => {
  const apiMethod = mode.value === 'add' ? addCaseApi : updateCaseApi
  const actionText = mode.value === 'add' ? '新增' : '编辑'

  await apiMethod(data)
  message.success(`${actionText}成功`)
}

// ==================== 事件处理 ====================
const handleClose = () => {
  router.go(-1)
}

const handleSaveAction = async () => {
  try {
    // 验证所有表单
    const isValid = await validateAllForms()
    if (!isValid) {
      return
    }

    // 构建并提交数据
    const submitData = await buildSubmitData()
    await submitCaseData(submitData)

    // 返回列表页
    router.go(-1)
  } catch (error) {
    // 错误处理已在各个函数内部处理
  }
}

// ==================== 初始化 ====================
const initializeForAddMode = () => {
  basicFormMethods.setValues({ caseCode: generateCaseCode() })
}

const initializeComponent = async () => {
  setupCaseTypeChangeHandler()

  if (editId.value && mode.value !== 'add') {
    // 编辑或查看模式，加载数据
    await loadCaseData()
  } else if (mode.value === 'add') {
    // 新增时，生成案件编号
    initializeForAddMode()
  }
}

// 组件挂载时初始化
onMounted(() => {
  initializeComponent()
})
</script>

<template>
  <ContentWrap>
    <div class="flex flex-col h-full p-18px">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <!-- eslint-disable-next-line vue/html-self-closing -->
          <Back
            :title="title"
            hasEmit
            showCancel
            :showSave="!isReadonly"
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSaveAction"
          />
        </div>
      </div>
      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
          <div class="mt-12px">
            <Form
              :disabled="isReadonly"
              :schema="allSchemas.BasicSchema"
              @register="basicFormRegister"
              isCol
              :rules="caseFormRules"
            ></Form>
          </div>
        </div>
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">诉讼信息</div>
          <div class="mt-12px">
            <Form
              :disabled="isReadonly"
              :schema="allSchemas.LitigationSchema"
              @register="litigationFormRegister"
              isCol
              :rules="caseFormRules"
            ></Form>
          </div>
        </div>
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">原被告信息</div>
          <div class="mt-12px">
            <PartyTable
              ref="plaintiffRef"
              title="原告/申请人"
              :data="plaintiff.list"
              :disabled="isReadonly"
              @add="plaintiff.add"
              @remove="plaintiff.remove"
            />

            <PartyTable
              ref="defendantRef"
              title="被告/被申请人"
              :data="defendant.list"
              :disabled="isReadonly"
              @add="defendant.add"
              @remove="defendant.remove"
            />

            <PartyTable
              ref="thirdPartyRef"
              title="第三人"
              :data="thirdParty.list"
              :disabled="isReadonly"
              @add="thirdParty.add"
              @remove="thirdParty.remove"
            />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </ContentWrap>
</template>

<style lang="sass" scoped></style>
