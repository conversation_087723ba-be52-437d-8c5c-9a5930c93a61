<script setup lang="tsx">
import { computed, defineExpose, nextTick } from 'vue'
import { Back, Form } from '@/components'
import { useForm } from '@/hooks/web/useForm'
import { useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import {
  getExternalLawFirmDetailApi,
  addExternalLawFirmApi,
  updateExternalLawFirmApi
} from '@/api/law/legalTeam/externalLawFirm'
import { lawFirmSchemas, getLawFirmFormRules } from '../schemas/lawFirmSchemas'
import { useMessage } from '@/hooks/web/useMessage'
import { useLayout } from '@/hooks/web/useLayout'
const { getModalStyle, layoutSize, isCollapsed } = useLayout()
// 使用 CRUD Schemas 生成表单配置
const { allSchemas } = useCrudSchemas(lawFirmSchemas)

const message = useMessage()
const emit = defineEmits(['close', 'save'])

const mode = ref<'add' | 'edit' | 'view'>('add')
const visible = ref(false)
const editId = ref<number | null>(null)

const title = computed(() => {
  switch (mode.value) {
    case 'add':
      return '新增律所'
    case 'edit':
      return '编辑律所'
    case 'view':
      return '查看律所'
    default:
      return ''
  }
})

// 计算属性：是否为只读模式
const isReadonly = computed(() => mode.value === 'view')

// 表单验证规则
const rules = getLawFirmFormRules()

// 表单管理
const { register: formRegister, methods: formMethods } = useForm()

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  editId.value = null
  formMethods.setValues({})
  emit('close')
}

// 设置表单数据
const setValue = async () => {
  if (editId.value) {
    const data = await getExternalLawFirmDetailApi(editId.value)
    //professionalFields字段是字符串，需要转换为数组
    if (data.professionalFields) {
      data.professionalFields = data.professionalFields.split(',')
    }
    formMethods.setValues(data)
  }
}

// 保存操作
const handleSaveAction = async () => {
  try {
    const valid = await formMethods.validate()
    if (!valid) return

    const formData = await formMethods.getFormData()
    const submitData = {
      ...formData,
      ...(mode.value === 'edit' && editId.value && { id: editId.value })
    }
    //professionalFields字段是数组，需要转换为字符串
    if (submitData.professionalFields) {
      submitData.professionalFields = submitData.professionalFields.join(',')
    }

    const apiMethod = mode.value === 'add' ? addExternalLawFirmApi : updateExternalLawFirmApi
    const actionText = mode.value === 'add' ? '新增' : '编辑'

    await apiMethod(submitData)
    message.success(`${actionText}成功`)

    visible.value = false
    emit('save')
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 打开弹窗
const open = async (type: 'add' | 'edit' | 'view', id?: number) => {
  mode.value = type
  visible.value = true

  await nextTick()

  if (id && type !== 'add') {
    editId.value = id
    await setValue()
  }
}

defineExpose({
  open
})
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__zoomIn animate__faster"
    leave-active-class="animate__animated animate__zoomOut"
  >
    <div v-if="visible" class="fixed inset-0 z-999 bg-[white] flex flex-col" :style="getModalStyle">
      <div class="w-full h-auto flex-shrink-0">
        <div class="flex items-center w-full">
          <Back
            :title="title"
            hasEmit
            showCancel
            :showSave="!isReadonly"
            @back="handleClose"
            @cancel="handleClose"
            @ok="handleSaveAction"
          />
        </div>
      </div>

      <ElScrollbar class="h-[calc(100%-50px)] overflow-y-auto">
        <div>
          <div class="bg-#f1f2fe lh-40px pl-5">基础信息</div>
          <div class="mt-12px">
            <Form
              :disabled="isReadonly"
              :schema="allSchemas.formSchema"
              @register="formRegister"
              isCol
              :rules="rules"
            />
          </div>
        </div>
      </ElScrollbar>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
// 样式可以根据需要添加
</style>
