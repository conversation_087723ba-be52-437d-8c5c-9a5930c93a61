import type { App } from 'vue'
import { Icon } from './Icon'
import { Table } from './Table'
import { Form } from './Form'
import { Search } from './Search'
import { Dialog } from './Dialog'
import { ContentWrap } from './ContentWrap'
import { Button } from './Button'
import { Back } from './Back'
import { Descriptions } from './Descriptions'
import { Title } from './Title'
import { AdvancedCheckbox } from './AdvancedCheckbox'

export const setupGlobCom = (app: App<Element>): void => {
  app.component('Icon', Icon)
}

export {
  Table,
  Form,
  Search,
  Dialog,
  ContentWrap,
  Button,
  Back,
  Icon,
  Descriptions,
  Title,
  AdvancedCheckbox
}
