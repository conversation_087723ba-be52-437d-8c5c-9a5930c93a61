# Mock JS 配置使用说明

## 概述

本项目已配置了 vite-plugin-mock 插件，支持部分接口走 mock，部分接口走真实请求。

## 配置文件说明

### 1. 环境变量配置（.env.local）

```bash
# 是否开启mock
VITE_USE_MOCK=true
```

- `VITE_USE_MOCK=true`: 启用 mock 功能
- `VITE_USE_MOCK=false`: 禁用 mock 功能，所有接口走真实请求

### 2. Vite 配置（build/vite/index.ts）

已在 vite 插件中配置了 mock 插件：

```typescript
viteMockServe({
  mockPath: 'mock', // mock文件夹路径
  localEnabled: isMock, // 开发环境是否启用mock
  prodEnabled: false, // 生产环境是否启用mock
  watchFiles: true, // 监听文件变化
  logger: true, // 是否在控制台显示请求日志
  supportTs: true, // 支持TypeScript
})
```

### 3. Mock 配置管理（mock/config.ts）

通过 `MOCK_CONFIG` 可以精确控制哪些模块/接口使用 mock：

```typescript
export const MOCK_CONFIG = {
  // 案件管理模块 - 全部启用mock
  case: {
    enabled: true,
    prefix: '/base/api/case'
  },
  
  // 用户管理模块 - 部分启用mock（只有查询接口）
  user: {
    enabled: true,
    prefix: '/admin-api/system/user',
    mockApis: [
      '/admin-api/system/user/page',  // 这个接口走mock
      '/admin-api/system/user/get'    // 这个接口走mock
    ]
    // 其他用户相关接口(create, update, delete)会走真实请求
  },
  
  // 菜单管理模块 - 不启用mock，全部走真实请求
  menu: {
    enabled: false,
    prefix: '/admin-api/system/menu'
  }
}
```

## 使用方式

### 1. 全局启用/禁用 Mock

修改 `.env.local` 文件：

```bash
# 启用 mock
VITE_USE_MOCK=true

# 禁用 mock（所有接口走真实请求）
VITE_USE_MOCK=false
```

### 2. 为特定模块创建 Mock 文件

在 `mock` 目录下创建对应的 mock 文件：

```
mock/
├── case/
│   └── case.mock.ts     # 案件管理相关mock
├── user/
│   └── user.mock.ts     # 用户管理相关mock
├── config.ts            # mock配置管理
└── public.constant.ts   # 公共常量
```

### 3. Mock 文件格式

```typescript
// mock/user/user.mock.ts
import Mock from 'mockjs'
import { SUCCESS_CODE } from '../public.constant'

export default [
  {
    url: '/admin-api/system/user/page', // 接口地址
    method: 'get',                      // 请求方法
    timeout: 1000,                     // 响应延迟
    response: ({ query }) => {          // 响应数据
      // 模拟数据逻辑
      return {
        code: SUCCESS_CODE,
        data: {
          list: [],
          total: 0
        },
        msg: '查询成功'
      }
    }
  }
]
```

## 实际使用场景

### 场景1：开发新功能时使用 Mock

```bash
# 1. 启用mock
VITE_USE_MOCK=true

# 2. 在mock目录下创建对应的mock文件
# 3. 定义需要的接口mock数据
# 4. 前端开发可以独立进行，不依赖后端接口
```

### 场景2：部分接口使用真实数据，部分使用 Mock

```typescript
// 在 mock/config.ts 中配置
export const MOCK_CONFIG = {
  user: {
    enabled: true,
    prefix: '/admin-api/system/user',
    mockApis: [
      '/admin-api/system/user/page',  // 查询接口使用mock
      '/admin-api/system/user/get'    // 详情接口使用mock
    ]
    // create, update, delete 接口走真实请求
  }
}
```

### 场景3：联调阶段逐步切换到真实接口

```bash
# 方式1：全局关闭mock
VITE_USE_MOCK=false

# 方式2：在 mock/config.ts 中逐个模块关闭
export const MOCK_CONFIG = {
  user: {
    enabled: false,  // 关闭用户模块mock
    prefix: '/admin-api/system/user'
  }
}
```

## 注意事项

1. **接口地址要匹配**：Mock 文件中的 `url` 要与实际请求的接口地址完全匹配
2. **响应格式要一致**：Mock 返回的数据格式要与后端接口保持一致
3. **开发环境使用**：建议只在开发环境使用 mock，生产环境关闭
4. **及时更新**：后端接口变更时，及时更新对应的 mock 数据

## 启动项目

```bash
# 安装依赖
pnpm install

# 启动开发服务器（会根据.env.local中的配置启用mock）
pnpm dev
```

启动后在浏览器控制台可以看到 mock 插件的日志输出，显示哪些接口走了 mock。
