<template>
  <div class="flow-node-manager">
    <!-- 节点列表 -->
    <div class="node-list">
      <div class="node-header">
        <el-table
          :data="nodeTableData"
          style="width: 100%"
          :show-header="true"
          border
          empty-text="请点击下方按钮添加节点"
        >
          <el-table-column label="序号" width="80" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="节点名称" min-width="200">
            <template #default="{ row }">
              <el-input
                v-model="row.nodeName"
                placeholder="请输入节点名称"
                :disabled="mode === 'view'"
                @change="handleNodeChange"
                maxlength="50"
                clearable
              />
            </template>
          </el-table-column>
          <el-table-column label="描述" min-width="300">
            <template #default="{ row }">
              <el-input
                v-model="row.nodeDescription"
                placeholder="请输入节点描述"
                :disabled="mode === 'view'"
                @change="handleNodeChange"
                maxlength="200"
                clearable
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" align="center" v-if="mode !== 'view'">
            <template #default="{ $index }">
              <el-button link type="primary" @click="moveUp($index)" :disabled="$index === 0">
                上移
              </el-button>
              <el-button
                link
                type="primary"
                @click="moveDown($index)"
                :disabled="$index === nodeTableData.length - 1"
              >
                下移
              </el-button>
              <el-button
                link
                type="danger"
                @click="removeNode($index)"
                :disabled="nodeTableData.length <= 1"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 添加节点按钮 -->
      <div class="node-actions" v-if="mode !== 'view'">
        <el-button type="primary" @click="addNode" :icon="Plus" style="margin-top: 12px">
          增加节点 +
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { DEFAULT_FLOW_NODE } from '../constants/flowConstants'
import type { FlowNodeVO } from '@/api/ICM/flowManagement'

interface Props {
  modelValue: FlowNodeVO[]
  mode?: string // 'add' | 'edit' | 'view'
}

interface Emits {
  (e: 'update:modelValue', value: FlowNodeVO[]): void
  (e: 'change', value: FlowNodeVO[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  mode: 'add'
})

const emit = defineEmits<Emits>()

// 节点表格数据
const nodeTableData = ref<FlowNodeVO[]>([])

// 监听外部传入的数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      nodeTableData.value = newValue.map((node, index) => ({
        ...node,
        nodeOrder: index + 1
      }))
    } else {
      // 初始化时至少有一个节点
      nodeTableData.value = [
        {
          ...DEFAULT_FLOW_NODE,
          nodeOrder: 1
        }
      ]
    }
  },
  { immediate: true, deep: true }
)

// 添加节点
const addNode = () => {
  const newNode: FlowNodeVO = {
    ...DEFAULT_FLOW_NODE,
    nodeOrder: nodeTableData.value.length + 1
  }
  nodeTableData.value.push(newNode)
  handleNodeChange()
}

// 删除节点
const removeNode = (index: number) => {
  if (nodeTableData.value.length <= 1) {
    return
  }
  nodeTableData.value.splice(index, 1)
  // 重新排序
  nodeTableData.value.forEach((node, idx) => {
    node.nodeOrder = idx + 1
  })
  handleNodeChange()
}

// 上移节点
const moveUp = (index: number) => {
  if (index === 0) return
  const temp = nodeTableData.value[index]
  nodeTableData.value[index] = nodeTableData.value[index - 1]
  nodeTableData.value[index - 1] = temp
  // 重新排序
  nodeTableData.value.forEach((node, idx) => {
    node.nodeOrder = idx + 1
  })
  handleNodeChange()
}

// 下移节点
const moveDown = (index: number) => {
  if (index === nodeTableData.value.length - 1) return
  const temp = nodeTableData.value[index]
  nodeTableData.value[index] = nodeTableData.value[index + 1]
  nodeTableData.value[index + 1] = temp
  // 重新排序
  nodeTableData.value.forEach((node, idx) => {
    node.nodeOrder = idx + 1
  })
  handleNodeChange()
}

// 节点变化处理
const handleNodeChange = () => {
  const nodes = nodeTableData.value.map((node, index) => ({
    ...node,
    nodeOrder: index + 1
  }))
  emit('update:modelValue', nodes)
  emit('change', nodes)
}

// 验证节点数据
const validateNodes = (): boolean => {
  if (nodeTableData.value.length === 0) {
    return false
  }

  return nodeTableData.value.every(
    (node) =>
      node.nodeName &&
      node.nodeName.trim() !== '' &&
      node.nodeDescription &&
      node.nodeDescription.trim() !== ''
  )
}

// 对外暴露验证方法
defineExpose({
  validateNodes
})
</script>

<style lang="scss" scoped>
.flow-node-manager {
  .node-list {
    .node-header {
      margin-bottom: 12px;
    }

    .node-actions {
      text-align: center;
      padding: 20px 0;
      border-top: 1px solid #ebeef5;
    }
  }
}

:deep(.el-table .el-table__empty-block) {
  min-height: 200px;
}

:deep(.el-table .el-table__empty-text) {
  color: #909399;
}
</style>
